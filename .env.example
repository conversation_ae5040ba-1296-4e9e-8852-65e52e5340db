# Database
MONGODB_URI=mongodb://localhost:27017/trading-platform

# Authentication
JWT_SECRET=your-super-secret-jwt-key-here
NEXTAUTH_SECRET=your-nextauth-secret-here
NEXTAUTH_URL=http://localhost:3000

# Server Ports
PORT=3000
FRONTEND_PORT=3000
BACKEND_PORT=3002

# Trading APIs
DHAN_ACCESS_TOKEN=your_dhan_access_token
DHAN_CLIENT_ID=your_dhan_client_id
ANGEL_API_KEY=your_angel_api_key
MOTILAL_API_KEY=your_motilal_api_key

# Socket.IO
NEXT_PUBLIC_SOCKET_URL=http://localhost:3002

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Subscription Settings
SUBSCRIPTION_TYPE=quote
INSTRUMENTS=NSE_EQ:1333,NSE_EQ:3045

# Node Environment
NODE_ENV=development
NODE_OPTIONS=--max-old-space-size=8192