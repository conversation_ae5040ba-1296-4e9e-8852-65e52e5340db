# 🚀 Port Configuration Guide

## Overview
This document explains the port configuration for the Market Dashboard application.

## Architecture

```
┌─────────────────────────────────────────────────────────────────────────┐
│                        Market Dashboard System                         │
├─────────────────────────────────────────────────────────────────────────┤
│  Frontend (React/Next.js)     │  Backend (Express/Socket.IO)           │
│  Port: 3000 (FRONTEND_PORT)   │  Port: 3001 (BACKEND_PORT)             │
│  ├─ User Interface             │  ├─ REST API (/api/*)                  │
│  ├─ Authentication UI          │  ├─ WebSocket Server (Socket.IO)       │
│  ├─ Market Data Display        │  ├─ MongoDB Connection                 │
│  └─ User Management            │  └─ Dhan API Integration               │
└─────────────────────────────────────────────────────────────────────────┘
```

## Port Configuration

### Environment Variables (.env)

| Variable | Default | Purpose | Used By |
|----------|---------|---------|---------|
| `FRONTEND_PORT` | 3000 | Next.js development server | React app, package.json scripts |
| `BACKEND_PORT` | 3001 | Express server + Socket.IO | WebSocket server, REST API |
| `PORT` | 3001 | Legacy compatibility | Backward compatibility only |

### Data Flow
```
Browser → Frontend:3000 → Backend:3001 → Dhan API → MongoDB
```

## Usage

### Check Port Configuration
```bash
npm run ports:check
```

### Check Port Status
```bash
npm run ports:status
```

### Kill Processes on Ports
```bash
npm run ports:kill
```

### Start Development Servers
```bash
# Starts both frontend and backend
npm run dev

# Start individually
npm run dev:next    # Frontend only
npm run dev:server  # Backend only
```

## Customizing Ports

### Method 1: Environment Variables
```bash
# In .env file
FRONTEND_PORT=3005
BACKEND_PORT=3006
```

### Method 2: Command Line
```bash
# Windows
set FRONTEND_PORT=3005 && npm run dev

# Linux/Mac
FRONTEND_PORT=3005 npm run dev
```

## Troubleshooting

### Port Already in Use
If you get "EADDRINUSE" errors:

1. **Check what's using the port:**
   ```bash
   netstat -ano | findstr :3001
   ```

2. **Kill the process:**
   ```bash
   npm run ports:kill
   ```

3. **Or use different ports:**
   ```bash
   # Update .env
   BACKEND_PORT=3002
   FRONTEND_PORT=3003
   ```

### Common Port Conflicts
- **3000**: Often used by other React apps
- **3001**: Common for Express servers
- **3002-3003**: Good alternatives

### Production Deployment
For production, use standard ports:
- **Frontend**: 80 (HTTP) or 443 (HTTPS)
- **Backend**: 8080 or custom port behind reverse proxy

## Notes

- All port configurations are centralized in `.env`
- Frontend automatically detects available ports if default is busy
- Backend will fail if port is in use (by design for consistency)
- MongoDB runs on default port 27017 (external service)
- Dhan API uses standard HTTPS/WSS ports (external service)
