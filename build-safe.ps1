#!/usr/bin/env pwsh

# Safe Build Script for Memory-Intensive Next.js Project
# This script builds the project with memory optimizations and crash prevention

Write-Host "🚀 Starting Safe Build Process..." -ForegroundColor Yellow

# Stop all Node.js processes to free memory
Write-Host "⏹️  Stopping existing Node.js processes..." -ForegroundColor Blue
Stop-Process -Name "node" -Force -ErrorAction SilentlyContinue
Start-Sleep -Seconds 3

# Clean all caches and build artifacts
Write-Host "🧹 Cleaning build artifacts and caches..." -ForegroundColor Blue
$cleanDirs = @(
    ".next",
    "dist", 
    "node_modules/.cache",
    ".next/cache",
    ".next/static",
    ".next/server"
)

foreach ($dir in $cleanDirs) {
    if (Test-Path $dir) {
        Write-Host "   Removing $dir..." -ForegroundColor Gray
        Remove-Item -Path $dir -Recurse -Force -ErrorAction SilentlyContinue
    }
}

# Clear npm cache
Write-Host "📦 Clearing npm cache..." -ForegroundColor Blue
npm cache clean --force 2>$null

# Wait for system to settle
Start-Sleep -Seconds 3

# Set memory-safe environment variables
Write-Host "⚙️  Setting build environment..." -ForegroundColor Blue
$env:NODE_OPTIONS = "--max-old-space-size=8192 --optimize-for-size"
$env:NODE_ENV = "production"

# Build frontend with memory monitoring
Write-Host "🏗️  Building Next.js frontend..." -ForegroundColor Green
try {
    npx next build
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Frontend build completed successfully!" -ForegroundColor Green
    } else {
        Write-Host "❌ Frontend build failed!" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Frontend build crashed: $_" -ForegroundColor Red
    exit 1
}

# Build backend
Write-Host "🔧 Building backend server..." -ForegroundColor Green
try {
    tsc src/server/index.ts --outDir dist --module commonjs --target es2020 --esModuleInterop --allowSyntheticDefaultImports
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Backend build completed successfully!" -ForegroundColor Green
    } else {
        Write-Host "❌ Backend build failed!" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Backend build crashed: $_" -ForegroundColor Red
    exit 1
}

Write-Host "🎉 Build completed successfully!" -ForegroundColor Green
Write-Host "📊 Build artifacts:" -ForegroundColor Cyan
Write-Host "   Frontend: .next/" -ForegroundColor Gray
Write-Host "   Backend: dist/" -ForegroundColor Gray
