#!/usr/bin/env pwsh

# Clean Cache Script for Next.js Project
# This script safely cleans all caches and restarts the development server

Write-Host "🧹 Cleaning Next.js caches..." -ForegroundColor Yellow

# Stop all Node.js processes
Write-Host "⏹️  Stopping Node.js processes..." -ForegroundColor Blue
Stop-Process -Name "node" -Force -ErrorAction SilentlyContinue
Start-Sleep -Seconds 2

# Remove cache directories
Write-Host "🗑️  Removing cache directories..." -ForegroundColor Blue
$cacheDirs = @(
    ".next",
    "node_modules/.cache", 
    "dist",
    ".next/cache",
    ".next/static",
    ".next/server"
)

foreach ($dir in $cacheDirs) {
    if (Test-Path $dir) {
        Write-Host "   Removing $dir..." -ForegroundColor Gray
        Remove-Item -Path $dir -Recurse -Force -ErrorAction SilentlyContinue
    }
}

# Clear npm cache
Write-Host "📦 Clearing npm cache..." -ForegroundColor Blue
npm cache clean --force 2>$null

# Wait a moment for file system to settle
Start-Sleep -Seconds 2

Write-Host "✅ Cache cleanup completed!" -ForegroundColor Green
Write-Host "🚀 Starting development server..." -ForegroundColor Yellow

# Start the development server
npm run dev
