const { config } = require('dotenv');

// Load environment variables
config();

console.log('=== Environment Variables Debug ===');
console.log('ACCESS_TOKEN exists:', !!process.env.ACCESS_TOKEN);
console.log('ACCESS_TOKEN length:', process.env.ACCESS_TOKEN?.length || 0);
console.log('ACCESS_TOKEN value (first 20 chars):', process.env.ACCESS_TOKEN?.substring(0, 20) || 'undefined');
console.log('ACCESS_TOKEN trimmed length:', process.env.ACCESS_TOKEN?.trim().length || 0);

console.log('\nCLIENT_ID exists:', !!process.env.CLIENT_ID);
console.log('CLIENT_ID length:', process.env.CLIENT_ID?.length || 0);
console.log('CLIENT_ID value:', process.env.CLIENT_ID || 'undefined');
console.log('CLIENT_ID trimmed length:', process.env.CLIENT_ID?.trim().length || 0);

console.log('\nDHAN_CLIENT_ID exists:', !!process.env.DHAN_CLIENT_ID);
console.log('DHAN_CLIENT_ID value:', process.env.DHAN_CLIENT_ID || 'undefined');

console.log('\nDHAN_ACCESS_TOKEN exists:', !!process.env.DHAN_ACCESS_TOKEN);
console.log('DHAN_ACCESS_TOKEN length:', process.env.DHAN_ACCESS_TOKEN?.length || 0);
console.log('DHAN_ACCESS_TOKEN value (first 20 chars):', process.env.DHAN_ACCESS_TOKEN?.substring(0, 20) || 'undefined');

console.log('\nAll environment variables starting with ACCESS or CLIENT or DHAN:');
Object.keys(process.env)
  .filter(key => key.includes('ACCESS') || key.includes('CLIENT') || key.includes('DHAN'))
  .forEach(key => {
    console.log(`${key}: ${process.env[key]?.substring(0, 20)}...`);
  });
