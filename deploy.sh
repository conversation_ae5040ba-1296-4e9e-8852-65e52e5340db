#!/bin/bash

# Production Deployment Script for Trading Application
# This script handles the complete deployment process

set -e  # Exit on any error

echo "🚀 Starting Production Deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [ "$EUID" -eq 0 ]; then
    print_warning "Running as root. Consider using a non-root user for security."
fi

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p logs
mkdir -p backups

# Backup existing deployment if it exists
if [ -d ".next" ] || [ -d "dist" ]; then
    print_status "Creating backup of existing deployment..."
    BACKUP_DIR="backups/backup-$(date +%Y%m%d-%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    [ -d ".next" ] && cp -r .next "$BACKUP_DIR/"
    [ -d "dist" ] && cp -r dist "$BACKUP_DIR/"
    print_success "Backup created at $BACKUP_DIR"
fi

# Install dependencies
print_status "Installing dependencies..."
npm ci --only=production

# Install PM2 globally if not installed
if ! command -v pm2 &> /dev/null; then
    print_status "Installing PM2 globally..."
    npm install -g pm2
fi

# Build the application
print_status "Building application..."
export NODE_ENV=production
npm run build

# Check if build was successful
if [ ! -d ".next" ] || [ ! -d "dist" ]; then
    print_error "Build failed! Missing .next or dist directories."
    exit 1
fi

# Stop existing PM2 processes
print_status "Stopping existing PM2 processes..."
pm2 stop ecosystem.config.js || true

# Start the application with PM2
print_status "Starting application with PM2..."
pm2 start ecosystem.config.js --env production

# Save PM2 configuration
pm2 save

# Setup PM2 startup script
print_status "Setting up PM2 startup script..."
pm2 startup || print_warning "Could not setup PM2 startup script. You may need to run this manually with sudo."

# Health check
print_status "Performing health check..."
sleep 10

# Check if processes are running
if pm2 list | grep -q "online"; then
    print_success "Application deployed successfully!"
    print_status "Application URLs:"
    echo "  Frontend: http://localhost:${FRONTEND_PORT:-3000}"
    echo "  Backend:  http://localhost:${BACKEND_PORT:-3001}"
    
    print_status "PM2 Management Commands:"
    echo "  View logs:    pm2 logs"
    echo "  Restart:      pm2 restart ecosystem.config.js"
    echo "  Stop:         pm2 stop ecosystem.config.js"
    echo "  Monitor:      pm2 monit"
else
    print_error "Deployment failed! Check PM2 logs for details."
    pm2 logs --lines 50
    exit 1
fi

print_success "Deployment completed successfully! 🎉"
