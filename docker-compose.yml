version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:6.0
    container_name: trading-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: your-secure-password
      MONGO_INITDB_DATABASE: trading_production
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - trading-network

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    container_name: trading-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - trading-network

  # Trading Application
  trading-app:
    build: .
    container_name: trading-app
    restart: unless-stopped
    environment:
      NODE_ENV: production
      FRONTEND_PORT: 3000
      BACKEND_PORT: 3001
      MONGODB_URI: **************************************************************************************
      REDIS_URL: redis://redis:6379
    ports:
      - "3000:3000"
      - "3001:3001"
    depends_on:
      - mongodb
      - redis
    volumes:
      - ./logs:/app/logs
      - ./webdata.xlsx:/app/webdata.xlsx:ro
    networks:
      - trading-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx Reverse Proxy (optional)
  nginx:
    image: nginx:alpine
    container_name: trading-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - trading-app
    networks:
      - trading-network

volumes:
  mongodb_data:
  redis_data:

networks:
  trading-network:
    driver: bridge
