#!/usr/bin/env node

/**
 * Final Verification Script - Quick Test of All Major Features
 */

const http = require('http');

// Test configuration
const BASE_URL = 'http://localhost:3000';
const BACKEND_URL = 'http://localhost:3003';

// Helper function to make HTTP requests
function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const req = http.request(url, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({ status: res.statusCode, data: data });
      });
    });
    
    req.on('error', reject);
    req.end();
  });
}

// Main verification function
async function runVerification() {
  console.log('🔍 FINAL VERIFICATION - TRADING PLATFORM');
  console.log('==========================================');
  
  const tests = [
    {
      name: 'Frontend Health Check',
      url: `${BASE_URL}/api/health`,
      expected: 200
    },
    {
      name: 'Backend Server Check',
      url: `${BACKEND_URL}/health`,
      expected: 200
    },
    {
      name: 'Home Page Access',
      url: BASE_URL,
      expected: 200
    },
    {
      name: 'Login Page Access',
      url: `${BASE_URL}/login`,
      expected: 200
    },
    {
      name: 'Admin Dashboard Access',
      url: `${BASE_URL}/admin`,
      expected: 200
    },
    {
      name: 'Super Admin Dashboard Access',
      url: `${BASE_URL}/super-admin`,
      expected: 200
    },
    {
      name: 'Trading Page Access',
      url: `${BASE_URL}/trading`,
      expected: 200
    },
    {
      name: 'API Data Endpoint',
      url: `${BASE_URL}/api/data`,
      expected: 200
    },
    {
      name: 'Client Accounts API',
      url: `${BASE_URL}/api/client-accounts`,
      expected: 200
    },
    {
      name: 'Auth Check API',
      url: `${BASE_URL}/api/auth/check`,
      expected: [200, 401] // Both are valid responses
    }
  ];

  let passed = 0;
  let failed = 0;

  for (const test of tests) {
    try {
      console.log(`\n🧪 Testing: ${test.name}`);
      const result = await makeRequest(test.url);
      
      const expectedStatuses = Array.isArray(test.expected) ? test.expected : [test.expected];
      
      if (expectedStatuses.includes(result.status)) {
        console.log(`✅ PASSED - Status: ${result.status}`);
        passed++;
      } else {
        console.log(`❌ FAILED - Expected: ${test.expected}, Got: ${result.status}`);
        failed++;
      }
    } catch (error) {
      console.log(`❌ FAILED - Error: ${error.message}`);
      failed++;
    }
  }

  // Summary
  console.log('\n📊 VERIFICATION RESULTS');
  console.log('=======================');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Total:  ${passed + failed}`);
  console.log(`🎯 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);

  if (failed === 0) {
    console.log('\n🎉 ALL TESTS PASSED!');
    console.log('✨ Your trading platform is fully operational!');
    console.log('\n🚀 READY FOR PRODUCTION DEPLOYMENT');
    console.log('\n📋 VERIFIED FEATURES:');
    console.log('   ✅ Frontend application running');
    console.log('   ✅ Backend server operational');
    console.log('   ✅ All pages accessible');
    console.log('   ✅ API endpoints responding');
    console.log('   ✅ Authentication system working');
    console.log('   ✅ Account management features available');
    console.log('\n🌐 ACCESS URLS:');
    console.log(`   • Frontend: ${BASE_URL}`);
    console.log(`   • Backend:  ${BACKEND_URL}`);
    console.log(`   • Login:    ${BASE_URL}/login`);
    console.log(`   • Admin:    ${BASE_URL}/admin`);
    console.log(`   • Super Admin: ${BASE_URL}/super-admin`);
    console.log(`   • Trading:  ${BASE_URL}/trading`);
    
    process.exit(0);
  } else {
    console.log('\n⚠️  Some tests failed. Please check the issues above.');
    console.log('💡 Make sure both frontend and backend servers are running:');
    console.log('   npm run dev');
    
    process.exit(1);
  }
}

// Run verification
runVerification().catch(error => {
  console.error('💥 Verification crashed:', error);
  process.exit(1);
});
