const crypto = require('crypto');

// Generate JWT_SECRET: a 256-bit (32-byte) base64url string
function generateJWTSecret() {
  return crypto.randomBytes(32).toString('base64url');
}

// Generate ENCRYPTION_SECRET: a 256-bit (32-byte) key with mixed characters
function generateEncryptionSecret(length = 44) {
  const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()-_=+[]{}|;:,.<>?';
  let secret = '';
  for (let i = 0; i < length; i++) {
    const randomIndex = crypto.randomInt(0, charset.length);
    secret += charset[randomIndex];
  }
  return secret;
}

// Output secrets
console.log('JWT_SECRET=' + generateJWTSecret());
console.log('ENCRYPTION_SECRET=' + generateEncryptionSecret());
