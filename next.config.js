/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: false, // Disable strict mode to prevent double rendering issues

  env: {
    FRONTEND_PORT: process.env.FRONTEND_PORT || '3000',
    BACKEND_PORT: process.env.BACKEND_PORT || '3003',
  },

  poweredByHeader: false,
  generateEtags: false,
  compress: true,

  // Updated for Next.js 15.3.3 compatibility
  serverExternalPackages: ['mongoose'],
  experimental: {
    optimizePackageImports: ['react', 'react-dom'],
  },

  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },

  // Add image configuration for better image handling
  images: {
    domains: ['localhost'],
    unoptimized: true, // For development
  },

  webpack: (config, { dev, isServer }) => {
    // Fix for WebSocket and buffer utilities
    if (!isServer) {
      config.externals = config.externals || [];
      config.externals.push({
        "utf-8-validate": "commonjs utf-8-validate",
        bufferutil: "commonjs bufferutil",
      });
    }

    // Simplified configuration to prevent clientReferenceManifest issues
    config.resolve = {
      ...config.resolve,
      symlinks: false,
      fallback: {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
      },
    };

    // Optimize for development
    if (dev) {
      config.cache = {
        type: 'memory',
        maxGenerations: 1,
      };
    }

    return config;
  },
};

module.exports = nextConfig;
