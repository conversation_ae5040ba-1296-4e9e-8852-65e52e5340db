/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,

  env: {
    FRONTEND_PORT: process.env.FRONTEND_PORT || '3000',
    BACKEND_PORT: process.env.BACKEND_PORT || '3003',
  },

  poweredByHeader: false,
  generateEtags: false,
  compress: true,

  // Removed deprecated 'turbo' option for Next.js 15.3.3 compatibility
  experimental: {
    optimizePackageImports: ['react', 'react-dom'],
  },

  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },

  // Add image configuration for better image handling
  images: {
    domains: ['localhost'],
    unoptimized: true, // For development
  },

  webpack: (config, { dev }) => {
    config.externals = config.externals || [];
    config.externals.push({
      "utf-8-validate": "commonjs utf-8-validate",
      bufferutil: "commonjs bufferutil",
    });

    if (!dev) {
      config.cache = false;
      config.parallelism = 1;
    } else {
      config.cache = {
        type: 'memory',
        maxGenerations: 1,
      };
    }

    config.optimization = {
      ...config.optimization,
      splitChunks: {
        chunks: 'all',
        minSize: 10000,
        maxSize: 100000,
        maxAsyncRequests: 5,
        maxInitialRequests: 3,
        cacheGroups: {
          default: {
            minChunks: 2,
            priority: -20,
            reuseExistingChunk: true,
            maxSize: 80000,
          },
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            priority: -10,
            chunks: 'all',
            maxSize: 100000,
          },
          react: {
            test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
            name: 'react',
            chunks: 'all',
            priority: 10,
            maxSize: 80000,
          },
        },
      },
      minimize: !dev,
      concatenateModules: false,
    };

    config.resolve = {
      ...config.resolve,
      symlinks: false,
    };

    return config;
  },
};

module.exports = nextConfig;
