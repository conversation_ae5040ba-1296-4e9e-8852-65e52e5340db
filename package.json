{"name": "dhan-websocket-server", "version": "1.0.0", "description": "Ultra-fast market data server for Dhan trading platform", "scripts": {"dev": "concurrently \"npm run dev:next\" \"npm run dev:server\"", "dev:next": "next dev", "dev:server": "cross-env NODE_OPTIONS=\"--no-deprecation\" ts-node-dev --respawn --transpile-only --require tsconfig-paths/register src/server/index.ts", "build": "next build", "build:production": "cross-env NODE_ENV=production next build", "build:frontend": "next build", "build:backend": "node scripts/build.js --backend-only --no-exit", "build:safe": "node scripts/build.js --verbose --no-exit", "build:manual": "node scripts/build.js --skip-clean --no-exit", "build:dev": "node scripts/build.js --no-exit --verbose", "build:custom": "node scripts/build.js --no-exit", "build:simple": "node scripts/simple-build.js", "start": "npm run start:production", "start:production": "concurrently \"npm run start:next\" \"npm run start:server\"", "start:server": "cross-env NODE_ENV=production node dist/server/index.js", "start:next": "cross-env NODE_ENV=production next start", "start:pm2": "pm2 start ecosystem.config.js", "stop:pm2": "pm2 stop ecosystem.config.js", "restart:pm2": "pm2 restart ecosystem.config.js", "logs:pm2": "pm2 logs", "deploy": "node scripts/deploy.js", "deploy:quick": "npm run build && npm run start:pm2", "lint": "next lint", "clean": "node scripts/clean.js", "clean:cache": "powershell -ExecutionPolicy Bypass -File clean-cache.ps1", "dev:safe": "npm run clean && npm run dev", "health": "curl http://localhost:3000/api/health && curl http://localhost:3003/health", "ports:check": "node -e \"console.log('🔍 Port Configuration:'); console.log('Frontend (Next.js):', process.env.FRONTEND_PORT || '3000'); console.log('Backend (Express):', process.env.BACKEND_PORT || '3003'); console.log('MongoDB: 27017 (external)');\"", "ports:kill": "npx kill-port 3000 3001 3002 3003 || echo Ports cleared", "ports:status": "node -e \"console.log('📊 Port Status Check - see terminal for netstat output')\" && netstat -an | findstr :3000 && netstat -an | findstr :3001 || echo ✅ Ports 3000 and 3001 are free", "db:indexes": "node scripts/create-indexes.js", "db:optimize": "npm run db:indexes"}, "dependencies": {"@auth/mongodb-adapter": "^3.10.0", "@types/nodemailer": "^6.4.17", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "glob": "^11.0.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.2", "multer": "^2.0.1", "next": "^15.3.3", "next-auth": "^4.24.11", "nodemailer": "^6.10.1", "react": "^18.2.0", "react-dom": "^18.2.0", "rimraf": "^6.0.1", "socket.io": "^4.6.1", "socket.io-client": "^4.8.1", "totp-generator": "^1.0.0", "ws": "^8.13.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^1.4.7", "@types/node": "^20.5.0", "@types/react": "^18.2.20", "@types/react-dom": "^18.2.7", "@types/ws": "^8.5.5", "@types/xlsx": "^0.0.35", "autoprefixer": "^10.4.15", "concurrently": "^8.2.1", "cross-env": "^7.0.3", "pm2": "^5.3.0", "postcss": "^8.4.28", "tailwindcss": "^3.3.3", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.6"}}