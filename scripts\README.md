# Build Scripts

This directory contains cross-platform build and deployment scripts for the trading platform.

## Scripts Overview

### 🧹 clean.js
Cross-platform cleanup script that removes build artifacts and cache directories.

**Usage:**
```bash
npm run clean
# or
node scripts/clean.js
```

**What it cleans:**
- `dist/` - Backend build output
- `.next/` - Next.js build cache
- `node_modules/.cache/` - NPM cache
- `.vercel/` - Vercel deployment cache
- `out/` - Static export output

### 🏗️ build.js
Comprehensive build script that handles both frontend and backend builds.

**Usage:**
```bash
npm run build                    # Full build (production)
npm run build:frontend          # Frontend only
npm run build:backend           # Backend only
npm run build:safe              # Verbose output
npm run build:manual            # Skip cleanup step
```

**Options:**
- `--verbose, -v` - Show detailed output
- `--skip-clean` - Skip the cleanup step
- `--frontend-only` - Build only Next.js frontend
- `--backend-only` - Build only TypeScript backend
- `--help, -h` - Show help

**Examples:**
```bash
node scripts/build.js --verbose
node scripts/build.js --frontend-only
node scripts/build.js --skip-clean
```

### 🚀 deploy.js
Deployment preparation script with pre-deployment checks.

**Usage:**
```bash
npm run deploy                  # Full deployment preparation
```

**What it does:**
1. Checks required files exist
2. Validates environment variables
3. Runs linting (optional)
4. Runs type checking
5. Builds the application

**Options:**
- `--verbose, -v` - Show detailed output
- `--skip-lint` - Skip ESLint checks
- `--help, -h` - Show help

## Environment Variables

The scripts respect these environment variables:

- `NODE_ENV` - Build environment (default: production)
- `NODE_OPTIONS` - Node.js options (default: --max-old-space-size=8192)

## Cross-Platform Compatibility

All scripts are designed to work on:
- ✅ Windows
- ✅ macOS  
- ✅ Linux
- ✅ CI/CD environments (Vercel, GitHub Actions, etc.)

## Troubleshooting

### Memory Issues
If you encounter memory issues during build:
```bash
export NODE_OPTIONS="--max-old-space-size=8192"
npm run build
```

### Permission Issues (Unix/Linux)
Make scripts executable:
```bash
chmod +x scripts/*.js
```

### Windows PowerShell Issues
If you encounter PowerShell execution policy issues, the Node.js scripts will work as an alternative to PowerShell-based commands.

## Integration with CI/CD

These scripts work well with CI/CD pipelines:

**GitHub Actions:**
```yaml
- name: Build application
  run: npm run build

- name: Deploy preparation
  run: npm run deploy
```

**Vercel:**
The build script automatically works with Vercel's build process.

**Docker:**
```dockerfile
RUN npm run build
```
