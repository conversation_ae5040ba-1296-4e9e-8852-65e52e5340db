#!/usr/bin/env node

/**
 * Cross-platform build script for the trading platform
 * Handles both frontend (Next.js) and backend (Node.js) builds
 */

const { spawn } = require('child_process');
const { clean } = require('./clean');
const path = require('path');

// Build configuration
const config = {
  nodeOptions: '--max-old-space-size=8192',
  environment: process.env.NODE_ENV || 'production',
  verbose: process.argv.includes('--verbose') || process.argv.includes('-v'),
  skipClean: process.argv.includes('--skip-clean'),
  frontendOnly: process.argv.includes('--frontend-only'),
  backendOnly: process.argv.includes('--backend-only'),
  noExit: process.argv.includes('--no-exit') || process.argv.includes('--keep-open')
};

/**
 * Execute a command and return a promise
 * @param {string} command - Command to execute
 * @param {string[]} args - Command arguments
 * @param {object} options - Spawn options
 */
function executeCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    if (config.verbose) {
      console.log(`🔧 Executing: ${command} ${args.join(' ')}`);
    }

    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      env: { ...process.env, ...options.env },
      ...options
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
}

/**
 * Build the frontend (Next.js)
 */
async function buildFrontend() {
  console.log('🏗️  Building frontend (Next.js)...');

  try {
    // Try npx first, fallback to direct next command
    try {
      await executeCommand('npx', ['next', 'build'], {
        env: {
          NODE_ENV: config.environment,
          NODE_OPTIONS: config.nodeOptions
        }
      });
    } catch (npxError) {
      if (config.verbose) {
        console.log('⚠️  npx not found, trying direct next command...');
      }
      await executeCommand('next', ['build'], {
        env: {
          NODE_ENV: config.environment,
          NODE_OPTIONS: config.nodeOptions
        }
      });
    }
    console.log('✅ Frontend build completed successfully!');
  } catch (error) {
    console.error('❌ Frontend build failed:', error.message);
    throw error;
  }
}

/**
 * Build the backend (TypeScript compilation)
 */
async function buildBackend() {
  console.log('🏗️  Building backend (TypeScript)...');
  
  try {
    await executeCommand('npx', [
      'tsc',
      'src/server/index.ts',
      '--outDir', 'dist',
      '--module', 'commonjs',
      '--target', 'es2020',
      '--esModuleInterop',
      '--allowSyntheticDefaultImports',
      '--skipLibCheck'
    ]);
    console.log('✅ Backend build completed successfully!');
  } catch (error) {
    console.error('❌ Backend build failed:', error.message);
    throw error;
  }
}

/**
 * Main build function
 */
async function build() {
  const startTime = Date.now();
  
  console.log('🚀 Starting build process...');
  console.log(`📦 Environment: ${config.environment}`);
  console.log(`🧠 Node Options: ${config.nodeOptions}`);
  console.log('');

  try {
    // Step 1: Clean (unless skipped)
    if (!config.skipClean) {
      clean();
      console.log('');
    }

    // Step 2: Build frontend (unless backend-only)
    if (!config.backendOnly) {
      await buildFrontend();
      console.log('');
    }

    // Step 3: Build backend (unless frontend-only)
    if (!config.frontendOnly) {
      await buildBackend();
      console.log('');
    }

    const duration = ((Date.now() - startTime) / 1000).toFixed(2);
    console.log(`🎉 Build completed successfully in ${duration}s!`);
    return true;

  } catch (error) {
    const duration = ((Date.now() - startTime) / 1000).toFixed(2);
    console.error('💥 Build failed:', error.message);
    console.error(`⏱️  Build failed after ${duration}s`);
    console.error('');
    console.error('🔧 Troubleshooting tips:');
    console.error('   1. Check TypeScript errors above');
    console.error('   2. Run: npm run build --verbose for more details');
    console.error('   3. Try: npm run build --frontend-only to test frontend only');
    console.error('   4. Check your .env file configuration');
    console.error('');

    if (config.noExit) {
      console.log('⚠️  Build failed but keeping process open (--no-exit flag)');
      return false;
    } else {
      console.log('💡 Use --no-exit flag to keep the process open after failure');
      process.exit(1);
    }
  }
}

/**
 * Show help information
 */
function showHelp() {
  console.log(`
🏗️  Trading Platform Build Script

Usage: node scripts/build.js [options]

Options:
  --help, -h          Show this help message
  --verbose, -v       Show verbose output
  --skip-clean        Skip the cleanup step
  --frontend-only     Build only the frontend (Next.js)
  --backend-only      Build only the backend (TypeScript)
  --no-exit           Keep process open after build failure
  --keep-open         Same as --no-exit

Environment Variables:
  NODE_ENV           Set build environment (default: production)

Examples:
  node scripts/build.js                    # Full build
  node scripts/build.js --frontend-only    # Frontend only
  node scripts/build.js --verbose          # Verbose output
  node scripts/build.js --skip-clean       # Skip cleanup
`);
}

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  showHelp();
  process.exit(0);
}

// Run the build if this script is executed directly
if (require.main === module) {
  build();
}

module.exports = { build, buildFrontend, buildBackend };
