const mongoose = require('mongoose');
require('dotenv').config();

async function checkAngelUsers() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.TESTLIST || 'mongodb://localhost:27017/trading-platform';
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');

    // Get all collections
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log('\n📋 Available collections:');
    collections.forEach(col => console.log(`  - ${col.name}`));

    // Check Angel users collection
    const AngelUser = mongoose.model('AngelUser', new mongoose.Schema({}, { strict: false }));
    const angelUsers = await AngelUser.find({}).limit(10);
    
    console.log(`\n👥 Angel Users found: ${angelUsers.length}`);
    if (angelUsers.length > 0) {
      console.log('\n📄 Sample Angel User data:');
      angelUsers.forEach((user, index) => {
        console.log(`\n${index + 1}. Angel User:`);
        console.log(`   ID: ${user._id}`);
        console.log(`   User ID: ${user.userId}`);
        console.log(`   Client Name: ${user.clientName}`);
        console.log(`   Email: ${user.email}`);
        console.log(`   Owner: ${user.owner}`);
        console.log(`   State: ${user.state}`);
        console.log(`   Created: ${user.createdAt}`);
      });
    } else {
      console.log('❌ No Angel users found in database');
    }

    // Check regular users
    const User = mongoose.model('User', new mongoose.Schema({}, { strict: false }));
    const users = await User.find({}).limit(10);
    console.log(`\n👤 Regular Users found: ${users.length}`);
    if (users.length > 0) {
      console.log('\n📄 Sample User data:');
      users.forEach((user, index) => {
        console.log(`\n${index + 1}. User:`);
        console.log(`   ID: ${user._id}`);
        console.log(`   Name: ${user.name}`);
        console.log(`   Email: ${user.email}`);
        console.log(`   Role: ${user.role}`);
        console.log(`   Admin ID: ${user.adminId}`);
        console.log(`   Super Admin ID: ${user.superAdminId}`);
        console.log(`   Active: ${user.isActive}`);
      });
    }

    // Check specifically for super admin
    const superAdmin = await User.findOne({ role: 'super_admin' });
    console.log(`\n👑 Super Admin found: ${superAdmin ? 'YES' : 'NO'}`);
    if (superAdmin) {
      console.log('\n📄 Super Admin details:');
      console.log(`   ID: ${superAdmin._id}`);
      console.log(`   Name: ${superAdmin.name}`);
      console.log(`   Email: ${superAdmin.email}`);
      console.log(`   Role: ${superAdmin.role}`);
      console.log(`   Active: ${superAdmin.isActive}`);
      console.log(`   Company: ${superAdmin.companyName}`);
    }

    // Check if there are any angelusers (lowercase) collections
    const angelUsersLower = collections.find(col => col.name.toLowerCase().includes('angel'));
    if (angelUsersLower) {
      console.log(`\n🔍 Found Angel-related collection: ${angelUsersLower.name}`);
      const AngelUserLower = mongoose.model('AngelUserLower', new mongoose.Schema({}, { strict: false }), angelUsersLower.name);
      const angelUsersLowerData = await AngelUserLower.find({}).limit(5);
      console.log(`   Records: ${angelUsersLowerData.length}`);
      if (angelUsersLowerData.length > 0) {
        console.log('   Sample data:', angelUsersLowerData[0]);
      }
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

checkAngelUsers();
