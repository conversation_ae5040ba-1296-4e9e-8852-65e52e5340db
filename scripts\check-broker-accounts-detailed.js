const mongoose = require('mongoose');
require('dotenv').config();

async function checkBrokerAccountsDetailed() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.TESTLIST || 'mongodb://localhost:27017/trading-platform';
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');

    // Define models
    const User = mongoose.model('User', new mongoose.Schema({}, { strict: false }));
    const AngelUser = mongoose.model('AngelUser', new mongoose.Schema({}, { strict: false }));
    const MotilalUser = mongoose.model('MotilalUser', new mongoose.Schema({}, { strict: false }));
    
    console.log('\n🔍 DETAILED BROKER ACCOUNT ANALYSIS');
    console.log('==================================================');

    // Get all users
    const allUsers = await User.find({});
    console.log(`\n👥 ALL USERS (${allUsers.length}):`);
    allUsers.forEach((user, index) => {
      console.log(`${index + 1}. ${user.name} (${user.email})`);
      console.log(`   Role: ${user.role}`);
      console.log(`   User ID: ${user._id}`);
      console.log(`   Admin ID: ${user.adminId || 'None'}`);
      console.log('');
    });

    // Get all Angel users
    console.log('\n📊 ANGEL USERS DETAILED:');
    const angelUsers = await AngelUser.find({});
    console.log(`Found ${angelUsers.length} Angel users:`);
    
    for (let i = 0; i < angelUsers.length; i++) {
      const angelUser = angelUsers[i];
      console.log(`\n${i + 1}. Angel Account:`);
      console.log(`   Client Name: ${angelUser.clientName}`);
      console.log(`   Broker User ID: ${angelUser.userId}`); // This is the broker account ID
      console.log(`   Email: ${angelUser.email}`);
      console.log(`   Capital: ${angelUser.capital}`);
      console.log(`   State: ${angelUser.state}`);
      console.log(`   Owner Field: ${angelUser.owner}`); // This should be the database user ID
      
      if (angelUser.owner) {
        try {
          const owner = await User.findById(angelUser.owner);
          if (owner) {
            console.log(`   ✅ Owner Found: ${owner.name} (${owner.email})`);
            console.log(`   Owner Role: ${owner.role}`);
            console.log(`   Owner Admin ID: ${owner.adminId || 'None'}`);
            
            // Find which admin manages this owner
            if (owner.adminId) {
              const admin = await User.findById(owner.adminId);
              if (admin) {
                console.log(`   👨‍💼 Managed by Admin: ${admin.name} (${admin.email})`);
              }
            }
          } else {
            console.log(`   ❌ Owner not found in database!`);
          }
        } catch (error) {
          console.log(`   ❌ Error fetching owner: ${error.message}`);
        }
      } else {
        console.log(`   ❌ No owner assigned!`);
      }
    }

    // Get all Motilal users
    console.log('\n📈 MOTILAL USERS DETAILED:');
    const motilalUsers = await MotilalUser.find({});
    console.log(`Found ${motilalUsers.length} Motilal users:`);
    
    for (let i = 0; i < motilalUsers.length; i++) {
      const motilalUser = motilalUsers[i];
      console.log(`\n${i + 1}. Motilal Account:`);
      console.log(`   Client Name: ${motilalUser.clientName}`);
      console.log(`   Broker User ID: ${motilalUser.userId}`); // This is the broker account ID
      console.log(`   Email: ${motilalUser.email}`);
      console.log(`   Capital: ${motilalUser.capital}`);
      console.log(`   Status: ${motilalUser.status}`);
      console.log(`   Owner Field: ${motilalUser.owner}`); // This should be the database user ID
      
      if (motilalUser.owner) {
        try {
          const owner = await User.findById(motilalUser.owner);
          if (owner) {
            console.log(`   ✅ Owner Found: ${owner.name} (${owner.email})`);
            console.log(`   Owner Role: ${owner.role}`);
            console.log(`   Owner Admin ID: ${owner.adminId || 'None'}`);
            
            // Find which admin manages this owner
            if (owner.adminId) {
              const admin = await User.findById(owner.adminId);
              if (admin) {
                console.log(`   👨‍💼 Managed by Admin: ${admin.name} (${admin.email})`);
              }
            }
          } else {
            console.log(`   ❌ Owner not found in database!`);
          }
        } catch (error) {
          console.log(`   ❌ Error fetching owner: ${error.message}`);
        }
      } else {
        console.log(`   ❌ No owner assigned!`);
      }
    }

    // Admin visibility analysis
    console.log('\n🔒 ADMIN VISIBILITY ANALYSIS:');
    console.log('==================================================');
    
    const admins = allUsers.filter(user => user.role === 'admin');
    
    for (const admin of admins) {
      console.log(`\n👨‍💼 Admin: ${admin.name} (${admin.email})`);
      console.log(`   Admin ID: ${admin._id}`);
      
      // Find users under this admin
      const usersUnderAdmin = allUsers.filter(user => 
        user.role === 'user' && user.adminId?.toString() === admin._id.toString()
      );
      
      console.log(`   Manages ${usersUnderAdmin.length} users:`);
      usersUnderAdmin.forEach(user => {
        console.log(`     - ${user.name} (${user.email})`);
      });
      
      // Find broker accounts this admin should see
      const userIds = [admin._id.toString(), ...usersUnderAdmin.map(u => u._id.toString())];
      
      const adminAngelAccounts = angelUsers.filter(account => 
        userIds.includes(account.owner?.toString())
      );
      
      const adminMotilalAccounts = motilalUsers.filter(account => 
        userIds.includes(account.owner?.toString())
      );
      
      console.log(`   Should see ${adminAngelAccounts.length} Angel accounts:`);
      adminAngelAccounts.forEach(account => {
        console.log(`     - ${account.clientName} (${account.userId})`);
      });
      
      console.log(`   Should see ${adminMotilalAccounts.length} Motilal accounts:`);
      adminMotilalAccounts.forEach(account => {
        console.log(`     - ${account.clientName} (${account.userId})`);
      });
    }

    console.log('\n📋 SUMMARY:');
    console.log(`- Total Users: ${allUsers.length}`);
    console.log(`- Total Admins: ${admins.length}`);
    console.log(`- Total Angel Accounts: ${angelUsers.length}`);
    console.log(`- Total Motilal Accounts: ${motilalUsers.length}`);

    // Check for issues
    const orphanedAngelAccounts = angelUsers.filter(account => !account.owner);
    const orphanedMotilalAccounts = motilalUsers.filter(account => !account.owner);
    
    if (orphanedAngelAccounts.length > 0) {
      console.log(`⚠️ ${orphanedAngelAccounts.length} Angel accounts without owners`);
    }
    if (orphanedMotilalAccounts.length > 0) {
      console.log(`⚠️ ${orphanedMotilalAccounts.length} Motilal accounts without owners`);
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

checkBrokerAccountsDetailed();
