const mongoose = require('mongoose');
require('dotenv').config();

async function checkBrokerUsers() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.TESTLIST || 'mongodb://localhost:27017/trading-platform';
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');

    // Define models
    const User = mongoose.model('User', new mongoose.Schema({}, { strict: false }));
    const AngelUser = mongoose.model('AngelUser', new mongoose.Schema({}, { strict: false }));
    const MotilalUser = mongoose.model('MotilalUser', new mongoose.Schema({}, { strict: false }));
    
    console.log('\n🔍 CHECKING BROKER USERS');
    console.log('=' .repeat(50));
    
    // Check all users
    console.log('\n👥 ALL USERS:');
    const allUsers = await User.find({}).select('name email role adminId');
    allUsers.forEach((user, index) => {
      console.log(`${index + 1}. ${user.name} (${user.email}) - Role: ${user.role}, AdminId: ${user.adminId || 'None'}`);
    });

    // Check Angel users
    console.log('\n📊 ANGEL USERS:');
    const angelUsers = await AngelUser.find({});
    console.log(`Found ${angelUsers.length} Angel users:`);

    for (let i = 0; i < angelUsers.length; i++) {
      const angelUser = angelUsers[i];
      console.log(`${i + 1}. Client: ${angelUser.clientName || angelUser.userId}`);
      console.log(`   User ID: ${angelUser.userId}`);

      // Manually get the owner
      const owner = await User.findById(angelUser.userId);
      if (owner) {
        console.log(`   Owner: ${owner.name} (${owner.email})`);
        console.log(`   Owner Role: ${owner.role}`);
        console.log(`   Owner AdminId: ${owner.adminId || 'None'}`);
      } else {
        console.log(`   Owner: Not found`);
      }

      console.log(`   API Key: ${angelUser.apiKey ? 'Set' : 'Not set'}`);
      console.log(`   Client Code: ${angelUser.clientCode || 'Not set'}`);
      console.log('');
    }

    // Check Motilal users
    console.log('\n📈 MOTILAL USERS:');
    const motilalUsers = await MotilalUser.find({});
    console.log(`Found ${motilalUsers.length} Motilal users:`);

    for (let i = 0; i < motilalUsers.length; i++) {
      const motilalUser = motilalUsers[i];
      console.log(`${i + 1}. Client: ${motilalUser.clientName || motilalUser.userId}`);
      console.log(`   User ID: ${motilalUser.userId}`);

      // Manually get the owner
      const owner = await User.findById(motilalUser.userId);
      if (owner) {
        console.log(`   Owner: ${owner.name} (${owner.email})`);
        console.log(`   Owner Role: ${owner.role}`);
        console.log(`   Owner AdminId: ${owner.adminId || 'None'}`);
      } else {
        console.log(`   Owner: Not found`);
      }

      console.log(`   Client Code: ${motilalUser.clientCode || 'Not set'}`);
      console.log('');
    }

    // Check for potential issues
    console.log('\n🔍 POTENTIAL ISSUES:');

    let angelUsersWithoutOwners = 0;
    let motilalUsersWithoutOwners = 0;
    let angelUsersWithoutAdminId = 0;
    let motilalUsersWithoutAdminId = 0;

    // Check Angel users for issues
    for (const angelUser of angelUsers) {
      const owner = await User.findById(angelUser.userId);
      if (!owner) {
        angelUsersWithoutOwners++;
        console.log(`❌ Angel user ${angelUser.clientName || angelUser.userId} has no owner (User ID: ${angelUser.userId})`);
      } else if (!owner.adminId) {
        angelUsersWithoutAdminId++;
        console.log(`❌ Angel user ${angelUser.clientName || angelUser.userId} owner has no adminId`);
      }
    }

    // Check Motilal users for issues
    for (const motilalUser of motilalUsers) {
      const owner = await User.findById(motilalUser.userId);
      if (!owner) {
        motilalUsersWithoutOwners++;
        console.log(`❌ Motilal user ${motilalUser.clientName || motilalUser.userId} has no owner (User ID: ${motilalUser.userId})`);
      } else if (!owner.adminId) {
        motilalUsersWithoutAdminId++;
        console.log(`❌ Motilal user ${motilalUser.clientName || motilalUser.userId} owner has no adminId`);
      }
    }

    // Issue 4: Check if any broker users point to non-existent user IDs
    console.log('\n🔍 CHECKING USER ID REFERENCES:');
    
    for (const angelUser of angelUsers) {
      const userExists = await User.findById(angelUser.userId);
      if (!userExists) {
        console.log(`❌ Angel user ${angelUser.clientName} references non-existent user ID: ${angelUser.userId}`);
      }
    }
    
    for (const motilalUser of motilalUsers) {
      const userExists = await User.findById(motilalUser.userId);
      if (!userExists) {
        console.log(`❌ Motilal user ${motilalUser.clientName} references non-existent user ID: ${motilalUser.userId}`);
      }
    }

    console.log('\n📋 SUMMARY:');
    console.log(`- Total Users: ${allUsers.length}`);
    console.log(`- Angel Users: ${angelUsers.length}`);
    console.log(`- Motilal Users: ${motilalUsers.length}`);
    console.log(`- Angel users without owners: ${angelUsersWithoutOwners}`);
    console.log(`- Motilal users without owners: ${motilalUsersWithoutOwners}`);
    console.log(`- Angel users without adminId: ${angelUsersWithoutAdminId}`);
    console.log(`- Motilal users without adminId: ${motilalUsersWithoutAdminId}`);

    if (angelUsers.length === 0 && motilalUsers.length === 0) {
      console.log('\n⚠️  NO BROKER USERS FOUND!');
      console.log('This explains why billing is not working - there are no broker accounts to place orders with.');
      console.log('You need to add Angel or Motilal broker accounts first.');
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

checkBrokerUsers();
