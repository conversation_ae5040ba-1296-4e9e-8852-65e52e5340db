const mongoose = require('mongoose');
require('dotenv').config();

async function checkSuperAdmin() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.TESTLIST || 'mongodb://localhost:27017/trading-platform';
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');

    // Check for super admin
    const User = mongoose.model('User', new mongoose.Schema({}, { strict: false }));
    
    const superAdmin = await User.findOne({ role: 'super_admin' });
    console.log(`\n👑 Super Admin found: ${superAdmin ? 'YES' : 'NO'}`);
    
    if (superAdmin) {
      console.log('\n📄 Super Admin details:');
      console.log(`   ID: ${superAdmin._id}`);
      console.log(`   Name: ${superAdmin.name}`);
      console.log(`   Email: ${superAdmin.email}`);
      console.log(`   Role: ${superAdmin.role}`);
      console.log(`   Active: ${superAdmin.isActive}`);
      console.log(`   Company: ${superAdmin.companyName}`);
      console.log(`   Created: ${superAdmin.createdAt}`);
    } else {
      console.log('\n❌ No super admin found. Creating one...');
      
      // Create super admin
      const bcrypt = require('bcryptjs');
      const salt = await bcrypt.genSalt(12);
      const hashedPassword = await bcrypt.hash('SuperAdmin123!', salt);
      
      const newSuperAdmin = new User({
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Super Administrator',
        role: 'super_admin',
        companyName: 'Trading Platform SaaS',
        phoneNumber: '+91-9999999999',
        address: 'Head Office, Trading Platform',
        isActive: true,
        isInvitationAccepted: true,
        orderRate: 0,
        totalOrders: 0,
        totalBilling: 0,
        lastBillingDate: new Date(),
      });
      
      await newSuperAdmin.save();
      console.log('✅ Super admin created successfully!');
      console.log(`   Email: ${newSuperAdmin.email}`);
      console.log(`   Password: SuperAdmin123!`);
      console.log(`   ID: ${newSuperAdmin._id}`);
    }

    // Check all users
    const allUsers = await User.find({}).select('name email role isActive');
    console.log(`\n👥 Total users in database: ${allUsers.length}`);
    allUsers.forEach((user, index) => {
      console.log(`${index + 1}. ${user.name} (${user.email}) - ${user.role} - ${user.isActive ? 'Active' : 'Inactive'}`);
    });

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

checkSuperAdmin();
