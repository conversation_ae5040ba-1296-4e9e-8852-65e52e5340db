/**
 * Quick script to remove CDS symbols from Angel_<PERSON>pi database
 * Run this to clean up the database and eliminate symbol lookup issues
 */

const { MongoClient } = require('mongodb');
require('dotenv').config();

const MONGODB_URI = process.env.TESTLIST || process.env.MONGODB_URI;

async function cleanDatabase() {
  const client = new MongoClient(MONGODB_URI);
  
  try {
    await client.connect();
    console.log('✅ Connected to MongoDB');

    const db = client.db("Angel_api");
    const collection = db.collection("totalscript");

    // Check current state
    const totalBefore = await collection.countDocuments();
    const cdsBefore = await collection.countDocuments({ exch_seg: "CDS" });
    const nseBefore = await collection.countDocuments({ exch_seg: "NSE" });

    console.log(`📊 Before cleanup:`);
    console.log(`   Total: ${totalBefore}`);
    console.log(`   NSE: ${nseBefore}`);
    console.log(`   CDS: ${cdsBefore}`);

    // Remove CDS symbols
    const result = await collection.deleteMany({ exch_seg: "CDS" });
    console.log(`🗑️  Deleted ${result.deletedCount} CDS symbols`);

    // Check after cleanup
    const totalAfter = await collection.countDocuments();
    const cdsAfter = await collection.countDocuments({ exch_seg: "CDS" });
    const nseAfter = await collection.countDocuments({ exch_seg: "NSE" });

    console.log(`📊 After cleanup:`);
    console.log(`   Total: ${totalAfter}`);
    console.log(`   NSE: ${nseAfter}`);
    console.log(`   CDS: ${cdsAfter}`);

    console.log('🎉 Database cleaned successfully!');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await client.close();
  }
}

cleanDatabase();
