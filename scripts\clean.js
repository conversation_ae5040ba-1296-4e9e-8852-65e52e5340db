#!/usr/bin/env node

/**
 * Cross-platform clean script for the trading platform
 * Removes build artifacts and cache directories
 */

const fs = require('fs');
const path = require('path');

// Directories and files to clean
const pathsToClean = [
  'dist',
  '.next',
  'node_modules/.cache',
  '.vercel',
  'out'
];

/**
 * Recursively remove directory and all its contents
 * @param {string} dirPath - Path to directory to remove
 */
function removeDirectory(dirPath) {
  if (fs.existsSync(dirPath)) {
    try {
      fs.rmSync(dirPath, { recursive: true, force: true });
      console.log(`✅ Removed: ${dirPath}`);
    } catch (error) {
      console.log(`⚠️  Could not remove ${dirPath}: ${error.message}`);
    }
  } else {
    console.log(`ℹ️  Path does not exist: ${dirPath}`);
  }
}

/**
 * Main clean function
 */
function clean() {
  console.log('🧹 Starting cleanup...');
  console.log('');

  pathsToClean.forEach(cleanPath => {
    const fullPath = path.resolve(process.cwd(), cleanPath);
    removeDirectory(fullPath);
  });

  console.log('');
  console.log('✨ Cleanup completed!');
}

// Run the clean function
if (require.main === module) {
  clean();
}

module.exports = { clean, removeDirectory };
