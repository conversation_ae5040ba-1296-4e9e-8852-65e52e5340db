const mongoose = require('mongoose');
require('dotenv').config();

async function comprehensiveFix() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.TESTLIST || 'mongodb://localhost:27017/trading-platform';
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');

    // Define models
    const User = mongoose.model('User', new mongoose.Schema({}, { strict: false }));
    const AngelUser = mongoose.model('AngelUser', new mongoose.Schema({}, { strict: false }));
    const MotilalUser = mongoose.model('MotilalUser', new mongoose.Schema({}, { strict: false }));
    const BillingRecord = mongoose.model('BillingRecord', new mongoose.Schema({}, { strict: false }));
    
    console.log('\n🔧 COMPREHENSIVE FIX - ALL ISSUES');
    console.log('=' .repeat(60));
    
    // 1. Fix user codes
    console.log('\n1️⃣ FIXING USER CODES:');
    const usersWithoutCodes = await User.find({
      $or: [
        { userCode: { $exists: false } },
        { userCode: null },
        { userCode: '' }
      ]
    });
    
    for (const user of usersWithoutCodes) {
      const code = `USR${Date.now().toString().slice(-5)}${Math.random().toString(36).substr(2, 2).toUpperCase()}`;
      await User.findByIdAndUpdate(user._id, { $set: { userCode: code } });
      console.log(`   ✅ Set code ${code} for ${user.name || user.email}`);
    }
    
    // 2. Ensure all users have proper admin relationships
    console.log('\n2️⃣ FIXING ADMIN RELATIONSHIPS:');
    const admin = await User.findOne({ role: 'admin' });
    const users = await User.find({ role: 'user' });
    
    if (admin) {
      console.log(`👨‍💼 Admin: ${admin.userCode} - ${admin.name}`);
      
      for (const user of users) {
        if (!user.adminId) {
          await User.findByIdAndUpdate(user._id, { $set: { adminId: admin._id } });
          console.log(`   ✅ Linked user ${user.userCode} to admin`);
        }
      }
      
      // Set default order rate for admin
      if (!admin.orderRate || admin.orderRate === 0) {
        await User.findByIdAndUpdate(admin._id, { $set: { orderRate: 5 } });
        console.log(`   ✅ Set default order rate ₹5 for admin`);
      }
    }
    
    // 3. Fix broker account ownership
    console.log('\n3️⃣ FIXING BROKER OWNERSHIP:');
    const targetUser = await User.findOne({ role: 'user', adminId: { $exists: true } });
    
    if (targetUser) {
      console.log(`👤 Target user: ${targetUser.userCode} - ${targetUser.name}`);
      
      // Fix Angel users
      const angelUsers = await AngelUser.find({});
      for (const angelUser of angelUsers) {
        await AngelUser.findByIdAndUpdate(angelUser._id, { $set: { owner: targetUser._id } });
        console.log(`   ✅ Fixed Angel user: ${angelUser.clientName}`);
      }
      
      // Fix Motilal users
      const motilalUsers = await MotilalUser.find({});
      for (const motilalUser of motilalUsers) {
        await MotilalUser.findByIdAndUpdate(motilalUser._id, { $set: { owner: targetUser._id } });
        console.log(`   ✅ Fixed Motilal user: ${motilalUser.clientName || motilalUser.userId}`);
      }
      
      // Set default OTP
      if (!targetUser.userOtp) {
        const defaultOtp = '123456';
        await User.findByIdAndUpdate(targetUser._id, { $set: { userOtp: defaultOtp } });
        console.log(`   ✅ Set default OTP ${defaultOtp} for ${targetUser.userCode}`);
      }
    }
    
    // 4. Create test billing data
    console.log('\n4️⃣ CREATING TEST BILLING DATA:');
    if (targetUser && admin) {
      // Clear existing test billing records
      await BillingRecord.deleteMany({ orderId: { $regex: /^TEST_ORDER_/ } });
      
      const testBillingRecords = [
        {
          userId: targetUser._id,
          adminId: admin._id,
          orderId: `TEST_ORDER_${Date.now()}_1`,
          amount: 5.00,
          description: 'Test order 1 - Buy RELIANCE',
          status: 'completed',
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          userId: targetUser._id,
          adminId: admin._id,
          orderId: `TEST_ORDER_${Date.now()}_2`,
          amount: 5.00,
          description: 'Test order 2 - Sell TCS',
          status: 'completed',
          createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
          updatedAt: new Date(Date.now() - 24 * 60 * 60 * 1000)
        },
        {
          userId: targetUser._id,
          adminId: admin._id,
          orderId: `TEST_ORDER_${Date.now()}_3`,
          amount: 5.00,
          description: 'Test order 3 - Buy INFY',
          status: 'completed',
          createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
          updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
        }
      ];
      
      const insertedRecords = await BillingRecord.insertMany(testBillingRecords);
      console.log(`   ✅ Created ${insertedRecords.length} test billing records`);
      
      // Update admin's total billing
      const totalBilling = testBillingRecords.reduce((sum, record) => sum + record.amount, 0);
      await User.findByIdAndUpdate(admin._id, { 
        $inc: { totalBilling: totalBilling }
      });
      console.log(`   ✅ Updated admin total billing: +₹${totalBilling}`);
    }
    
    // 5. Final verification
    console.log('\n5️⃣ FINAL VERIFICATION:');
    const allUsers = await User.find({}).select('userCode name email role adminId orderRate userOtp totalBilling');
    const billingCount = await BillingRecord.countDocuments({});
    
    console.log('\n📋 ALL USERS:');
    allUsers.forEach((user, index) => {
      console.log(`${index + 1}. ${user.userCode} - ${user.name} (${user.role})`);
      if (user.role === 'user') {
        console.log(`   AdminId: ${user.adminId ? 'Set' : 'Missing'}`);
        console.log(`   OTP: ${user.userOtp || 'Not Set'}`);
      }
      if (user.role === 'admin') {
        console.log(`   Order Rate: ₹${user.orderRate || 0}`);
        console.log(`   Total Billing: ₹${user.totalBilling || 0}`);
      }
    });
    
    console.log(`\n📊 Total billing records: ${billingCount}`);
    
    // 6. Login credentials
    console.log('\n6️⃣ LOGIN CREDENTIALS:');
    if (targetUser) {
      console.log(`🔑 User Login:`);
      console.log(`   URL: http://localhost:3002/user-login`);
      console.log(`   User ID: ${targetUser.userCode}`);
      console.log(`   OTP: ${targetUser.userOtp || 'Ask admin to set'}`);
    }

    if (admin) {
      console.log(`\n👤 Admin Login:`);
      console.log(`   URL: http://localhost:3002/admin`);
      console.log(`   Email: ${admin.email}`);
      console.log(`   Password: Use existing password`);
    }

    console.log('\n✅ ALL ISSUES FIXED SUCCESSFULLY!');
    console.log('\n🔄 Please refresh the admin dashboard to see the changes');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

comprehensiveFix();
