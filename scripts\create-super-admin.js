const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

// MongoDB connection string - update this with your actual connection string
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb+srv://doadmin:<EMAIL>/admin?tls=true&authSource=admin';

// User schema (simplified version)
const userSchema = new mongoose.Schema({
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
  },
  password: {
    type: String,
    required: true,
  },
  name: {
    type: String,
    required: true,
    trim: true,
  },
  role: {
    type: String,
    enum: ['super_admin', 'admin', 'user'],
    default: 'user',
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  // Multi-tenant fields
  adminId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  superAdminId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  // Billing fields (for admins)
  orderRate: {
    type: Number,
    default: 0,
  },
  totalOrders: {
    type: Number,
    default: 0,
  },
  totalBilling: {
    type: Number,
    default: 0,
  },
  lastBillingDate: {
    type: Date,
    default: Date.now,
  },
  // Company/Organization info
  companyName: String,
  phoneNumber: String,
  address: String,
  // Invitation system
  invitedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  invitationToken: {
    type: String,
    unique: true,
    sparse: true,
  },
  invitationExpiry: Date,
  isInvitationAccepted: {
    type: Boolean,
    default: false,
  },
}, {
  timestamps: true,
});

// Hash password before saving
userSchema.pre('save', async function (next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

const User = mongoose.model('User', userSchema);

async function createSuperAdmin() {
  try {
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Check if super admin already exists
    const existingSuperAdmin = await User.findOne({ role: 'super_admin' });
    
    if (existingSuperAdmin) {
      console.log('⚠️ Super Admin already exists:');
      console.log(`   Email: ${existingSuperAdmin.email}`);
      console.log(`   Name: ${existingSuperAdmin.name}`);
      console.log(`   ID: ${existingSuperAdmin._id}`);
      return;
    }

    // Create super admin
    const superAdminData = {
      email: '<EMAIL>',
      password: 'SuperAdmin123!', // Change this to a secure password
      name: 'Super Administrator',
      role: 'super_admin',
      companyName: 'Trading Platform SaaS',
      phoneNumber: '+91-9999999999',
      address: 'Head Office, Trading Platform',
      isActive: true,
      isInvitationAccepted: true, // Super admin doesn't need invitation
    };

    const superAdmin = new User(superAdminData);
    await superAdmin.save();

    console.log('🎉 Super Admin created successfully!');
    console.log('📧 Login Credentials:');
    console.log(`   Email: ${superAdmin.email}`);
    console.log(`   Password: SuperAdmin123!`);
    console.log(`   Role: ${superAdmin.role}`);
    console.log(`   ID: ${superAdmin._id}`);
    console.log('');
    console.log('🔐 IMPORTANT: Please change the password after first login!');
    console.log('');
    console.log('🚀 You can now:');
    console.log('   1. Login at: http://localhost:3000/login');
    console.log('   2. Access Super Admin Dashboard: http://localhost:3000/super-admin');
    console.log('   3. Invite Admins: http://localhost:3000/super-admin/invite-admin');

  } catch (error) {
    console.error('❌ Error creating Super Admin:', error);
    
    if (error.code === 11000) {
      console.log('⚠️ User with this email already exists');
    }
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the script
createSuperAdmin();
