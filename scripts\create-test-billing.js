const mongoose = require('mongoose');
require('dotenv').config();

async function createTestBilling() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.TESTLIST || 'mongodb://localhost:27017/trading-platform';
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');

    // Define models
    const User = mongoose.model('User', new mongoose.Schema({}, { strict: false }));
    const BillingRecord = mongoose.model('BillingRecord', new mongoose.Schema({}, { strict: false }));
    
    console.log('\n💰 CREATING TEST BILLING DATA');
    console.log('=' .repeat(50));
    
    // Get a user and admin
    const user = await User.findOne({ role: 'user' });
    const admin = await User.findOne({ role: 'admin' });
    
    if (!user || !admin) {
      console.log('❌ Need both user and admin to create billing data');
      return;
    }
    
    console.log(`👤 User: ${user.userCode} - ${user.name}`);
    console.log(`👨‍💼 Admin: ${admin.userCode} - ${admin.name}`);
    
    // Create some test billing records
    const testBillingRecords = [
      {
        userId: user._id,
        adminId: admin._id,
        orderId: `TEST_ORDER_${Date.now()}_1`,
        amount: 5.00,
        description: 'Test order 1 - Buy RELIANCE',
        status: 'completed',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        userId: user._id,
        adminId: admin._id,
        orderId: `TEST_ORDER_${Date.now()}_2`,
        amount: 5.00,
        description: 'Test order 2 - Sell TCS',
        status: 'completed',
        createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // Yesterday
        updatedAt: new Date(Date.now() - 24 * 60 * 60 * 1000)
      },
      {
        userId: user._id,
        adminId: admin._id,
        orderId: `TEST_ORDER_${Date.now()}_3`,
        amount: 5.00,
        description: 'Test order 3 - Buy INFY',
        status: 'completed',
        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
        updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
      }
    ];
    
    // Clear existing test billing records
    await BillingRecord.deleteMany({ orderId: { $regex: /^TEST_ORDER_/ } });
    console.log('🗑️ Cleared existing test billing records');
    
    // Insert new test records
    const insertedRecords = await BillingRecord.insertMany(testBillingRecords);
    console.log(`✅ Created ${insertedRecords.length} test billing records`);
    
    // Update admin's total billing
    const totalBilling = testBillingRecords.reduce((sum, record) => sum + record.amount, 0);
    await User.findByIdAndUpdate(admin._id, { 
      $inc: { totalBilling: totalBilling }
    });
    console.log(`💰 Updated admin total billing: +₹${totalBilling}`);
    
    // Verify the data
    console.log('\n📊 VERIFICATION:');
    const billingCount = await BillingRecord.countDocuments({ adminId: admin._id });
    const totalAmount = await BillingRecord.aggregate([
      { $match: { adminId: admin._id } },
      { $group: { _id: null, total: { $sum: '$amount' } } }
    ]);
    
    console.log(`✅ Total billing records for admin: ${billingCount}`);
    console.log(`✅ Total billing amount: ₹${totalAmount[0]?.total || 0}`);
    
    // Show admin's updated info
    const updatedAdmin = await User.findById(admin._id);
    console.log(`✅ Admin total billing: ₹${updatedAdmin.totalBilling || 0}`);
    
    console.log('\n🎉 TEST BILLING DATA CREATED SUCCESSFULLY!');
    console.log('\n🔗 Now refresh the admin dashboard to see the billing data');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

createTestBilling();
