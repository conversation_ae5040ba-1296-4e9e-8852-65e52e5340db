const mongoose = require('mongoose');
require('dotenv').config();

async function createTestUser() {
  try {
    const mongoUri = process.env.TESTLIST || 'mongodb://localhost:27017/trading-platform';
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');

    // Define models
    const User = mongoose.model('User', new mongoose.Schema({}, { strict: false }));
    const AngelUser = mongoose.model('AngelUser', new mongoose.Schema({}, { strict: false }));
    const MotilalUser = mongoose.model('MotilalUser', new mongoose.Schema({}, { strict: false }));
    
    console.log('\n🔧 CREATING TEST USER AND FIXING ACCOUNT OWNERSHIP');
    console.log('=' .repeat(60));
    
    // Get the first admin to link the user to
    const admin = await User.findOne({ role: 'admin' });
    if (!admin) {
      console.log('❌ No admin found');
      return;
    }
    
    console.log(`👨‍💼 Found admin: ${admin.name} (${admin.email})`);
    
    // Check if test user already exists
    let testUser = await User.findOne({ email: '<EMAIL>' });
    
    if (!testUser) {
      // Create test user
      const bcrypt = require('bcryptjs');
      const salt = await bcrypt.genSalt(12);
      const hashedPassword = await bcrypt.hash('test123', salt);
      
      testUser = new User({
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Test Trading User',
        role: 'user',
        userCode: 'USR001',
        adminId: admin._id,
        userOtp: '123456',
        isActive: true,
        companyName: 'Test Trading Company',
        phoneNumber: '+91-*********9',
        address: 'Test Address',
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      await testUser.save();
      console.log('✅ Created test user:');
      console.log(`   Email: ${testUser.email}`);
      console.log(`   Password: test123`);
      console.log(`   User Code: ${testUser.userCode}`);
      console.log(`   ID: ${testUser._id}`);
    } else {
      console.log(`✅ Test user already exists: ${testUser.email}`);
    }
    
    // Get existing broker accounts
    const angelAccounts = await AngelUser.find({});
    const motilalAccounts = await MotilalUser.find({});
    
    console.log(`\n📱 Found ${angelAccounts.length} Angel accounts and ${motilalAccounts.length} Motilal accounts`);
    
    // Assign all broker accounts to the test user
    for (const account of angelAccounts) {
      await AngelUser.findByIdAndUpdate(account._id, {
        $set: { owner: testUser._id }
      });
      console.log(`✅ Assigned Angel account ${account.userId} (${account.clientName}) to test user`);
    }
    
    for (const account of motilalAccounts) {
      await MotilalUser.findByIdAndUpdate(account._id, {
        $set: { owner: testUser._id }
      });
      console.log(`✅ Assigned Motilal account ${account.userId} (${account.clientName}) to test user`);
    }
    
    // Create additional test accounts if needed
    const existingAngelCount = angelAccounts.length;
    const existingMotilalCount = motilalAccounts.length;
    
    if (existingAngelCount < 2) {
      console.log('\n📱 Creating additional Angel test accounts...');
      const additionalAngel = [];
      
      for (let i = existingAngelCount; i < 2; i++) {
        const angelAccount = {
          userId: `ANG00${i + 1}`,
          password: 'test123',
          apiKey: `test-angel-api-key-${i + 1}`,
          totpKey: 'JBSWY3DPEHPK3PXP',
          clientName: `Test Angel Client ${i + 1}`,
          email: testUser.email,
          phoneNumber: `*********${i + 1}`,
          state: 'live',
          capital: 100000 + (i * 50000),
          owner: testUser._id
        };
        additionalAngel.push(angelAccount);
      }
      
      if (additionalAngel.length > 0) {
        const createdAngel = await AngelUser.insertMany(additionalAngel);
        console.log(`✅ Created ${createdAngel.length} additional Angel accounts`);
      }
    }
    
    if (existingMotilalCount < 2) {
      console.log('\n📱 Creating additional Motilal test accounts...');
      const additionalMotilal = [];
      
      for (let i = existingMotilalCount; i < 2; i++) {
        const motilalAccount = {
          userId: `MOT00${i + 1}`,
          password: 'test123',
          apiKey: `test-motilal-api-key-${i + 1}`,
          twoFA: `test2fa${i + 1}`,
          totpKey: 'JBSWY3DPEHPK3PXP',
          clientName: `Test Motilal Client ${i + 1}`,
          email: testUser.email,
          phoneNumber: `*********${i + 3}`,
          status: 'active',
          capital: 120000 + (i * 60000),
          owner: testUser._id
        };
        additionalMotilal.push(motilalAccount);
      }
      
      if (additionalMotilal.length > 0) {
        const createdMotilal = await MotilalUser.insertMany(additionalMotilal);
        console.log(`✅ Created ${createdMotilal.length} additional Motilal accounts`);
      }
    }
    
    // Final verification
    console.log('\n✅ FINAL VERIFICATION:');
    const finalAngel = await AngelUser.find({ owner: testUser._id });
    const finalMotilal = await MotilalUser.find({ owner: testUser._id });
    
    console.log(`📱 Test user now owns ${finalAngel.length} Angel accounts and ${finalMotilal.length} Motilal accounts`);
    
    console.log('\n🎉 SETUP COMPLETE!');
    console.log('🔐 Login Credentials:');
    console.log(`   Email: ${testUser.email}`);
    console.log(`   Password: test123`);
    console.log(`   User OTP: 123456`);
    console.log('');
    console.log('🚀 You can now:');
    console.log('   1. Login at: http://localhost:3000/login');
    console.log('   2. Access Trading Dashboard: http://localhost:3000/trading');
    console.log('   3. Create batches and place orders');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

createTestUser();
