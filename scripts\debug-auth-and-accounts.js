const mongoose = require('mongoose');
require('dotenv').config();

async function debugAuthAndAccounts() {
  try {
    const mongoUri = process.env.TESTLIST || 'mongodb://localhost:27017/trading-platform';
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');

    // Define models
    const User = mongoose.model('User', new mongoose.Schema({}, { strict: false }));
    const AngelUser = mongoose.model('AngelUser', new mongoose.Schema({}, { strict: false }));
    const MotilalUser = mongoose.model('MotilalUser', new mongoose.Schema({}, { strict: false }));
    
    console.log('\n🔍 DEBUGGING AUTHENTICATION AND ACCOUNTS');
    console.log('=' .repeat(60));
    
    // Get all users
    const allUsers = await User.find({}).select('name email role userCode adminId isActive');
    console.log(`\n👥 ALL USERS (${allUsers.length}):`);
    allUsers.forEach((user, i) => {
      console.log(`   ${i + 1}. ${user.userCode || 'NO_CODE'} - ${user.name} (${user.email})`);
      console.log(`      Role: ${user.role}, Admin ID: ${user.adminId || 'None'}, Active: ${user.isActive}`);
      console.log(`      User ID: ${user._id}`);
    });
    
    // Get all Angel accounts
    const allAngel = await AngelUser.find({});
    console.log(`\n📱 ALL ANGEL ACCOUNTS (${allAngel.length}):`);
    for (let i = 0; i < allAngel.length; i++) {
      const acc = allAngel[i];
      const owner = acc.owner ? await User.findById(acc.owner) : null;
      console.log(`   ${i + 1}. ${acc.userId} - ${acc.clientName} (${acc.state})`);
      console.log(`      Owner: ${owner ? `${owner.userCode || 'NO_CODE'} - ${owner.name} (${owner.email})` : 'NO OWNER'}`);
      console.log(`      Owner ID: ${acc.owner || 'None'}`);
    }

    // Get all Motilal accounts
    const allMotilal = await MotilalUser.find({});
    console.log(`\n📱 ALL MOTILAL ACCOUNTS (${allMotilal.length}):`);
    for (let i = 0; i < allMotilal.length; i++) {
      const acc = allMotilal[i];
      const owner = acc.owner ? await User.findById(acc.owner) : null;
      console.log(`   ${i + 1}. ${acc.userId} - ${acc.clientName} (${acc.status})`);
      console.log(`      Owner: ${owner ? `${owner.userCode || 'NO_CODE'} - ${owner.name} (${owner.email})` : 'NO OWNER'}`);
      console.log(`      Owner ID: ${acc.owner || 'None'}`);
    }
    
    // Check admin relationships
    console.log(`\n👨‍💼 ADMIN RELATIONSHIPS:`);
    const admins = allUsers.filter(u => u.role === 'admin');
    for (const admin of admins) {
      const adminUsers = allUsers.filter(u => u.adminId && u.adminId.toString() === admin._id.toString());
      console.log(`   Admin: ${admin.name} (${admin.email})`);
      console.log(`   Manages ${adminUsers.length} users:`);
      adminUsers.forEach(user => {
        console.log(`     - ${user.userCode} - ${user.name} (${user.email})`);
      });
    }
    
    // Check which accounts each user should see
    console.log(`\n🔍 ACCOUNT VISIBILITY BY USER:`);
    for (const user of allUsers) {
      if (user.role === 'user') {
        const userAngel = allAngel.filter(acc => acc.owner && acc.owner.toString() === user._id.toString());
        const userMotilal = allMotilal.filter(acc => acc.owner && acc.owner.toString() === user._id.toString());
        console.log(`   User: ${user.userCode || 'NO_CODE'} - ${user.name}`);
        console.log(`     Angel accounts: ${userAngel.length}`);
        console.log(`     Motilal accounts: ${userMotilal.length}`);
      } else if (user.role === 'admin') {
        // Admin should see accounts of users under their management
        const managedUsers = allUsers.filter(u => u.adminId && u.adminId.toString() === user._id.toString());
        const managedUserIds = managedUsers.map(u => u._id.toString());
        const adminAngel = allAngel.filter(acc => acc.owner && managedUserIds.includes(acc.owner.toString()));
        const adminMotilal = allMotilal.filter(acc => acc.owner && managedUserIds.includes(acc.owner.toString()));
        console.log(`   Admin: ${user.userCode || 'NO_CODE'} - ${user.name}`);
        console.log(`     Manages ${managedUsers.length} users`);
        console.log(`     Can see ${adminAngel.length} Angel accounts`);
        console.log(`     Can see ${adminMotilal.length} Motilal accounts`);
      }
    }
    
    // Test API authentication simulation
    console.log(`\n🧪 SIMULATING API CALLS:`);
    
    // Test for each user what they should see
    for (const user of allUsers.filter(u => u.role !== 'super_admin')) {
      console.log(`\n   Testing for user: ${user.name} (${user.role})`);
      
      if (user.role === 'user') {
        const userAngel = await AngelUser.find({ owner: user._id }).select('-password -totpKey');
        const userMotilal = await MotilalUser.find({ owner: user._id }).select('-password -totpKey');
        console.log(`     /api/users/angel should return: ${userAngel.length} accounts`);
        console.log(`     /api/users/motilal should return: ${userMotilal.length} accounts`);
      } else if (user.role === 'admin') {
        // Get users under this admin
        const managedUsers = await User.find({ adminId: user._id, role: 'user' }).select('_id');
        const managedUserIds = managedUsers.map(u => u._id);
        const allOwnerIds = [...managedUserIds, user._id];
        
        const adminAngel = await AngelUser.find({ owner: { $in: allOwnerIds } }).select('-password -totpKey');
        const adminMotilal = await MotilalUser.find({ owner: { $in: allOwnerIds } }).select('-password -totpKey');
        console.log(`     /api/users/angel should return: ${adminAngel.length} accounts`);
        console.log(`     /api/users/motilal should return: ${adminMotilal.length} accounts`);
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

debugAuthAndAccounts();
