const mongoose = require('mongoose');
require('dotenv').config();

async function debugBillingIssue() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.TESTLIST || 'mongodb://localhost:27017/trading-platform';
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');

    // Define models
    const User = mongoose.model('User', new mongoose.Schema({}, { strict: false }));
    const BillingRecord = mongoose.model('BillingRecord', new mongoose.Schema({}, { strict: false }));
    
    console.log('\n🔍 DEBUGGING BILLING ISSUE');
    console.log('=' .repeat(50));
    
    // 1. Check all users and their relationships
    console.log('\n👥 ALL USERS:');
    const allUsers = await User.find({}).select('name email role adminId superAdminId orderRate totalOrders totalBilling isActive');
    allUsers.forEach((user, index) => {
      console.log(`${index + 1}. ${user.name} (${user.email})`);
      console.log(`   Role: ${user.role}`);
      console.log(`   Admin ID: ${user.adminId || 'NOT SET'}`);
      console.log(`   Super Admin ID: ${user.superAdminId || 'NOT SET'}`);
      console.log(`   Order Rate: ₹${user.orderRate || 0}`);
      console.log(`   Total Orders: ${user.totalOrders || 0}`);
      console.log(`   Total Billing: ₹${user.totalBilling || 0}`);
      console.log(`   Active: ${user.isActive}`);
      console.log('');
    });

    // 2. Check billing records
    console.log('\n💰 BILLING RECORDS:');
    const billingRecords = await BillingRecord.find({}).sort({ createdAt: -1 }).limit(10);
    console.log(`Found ${billingRecords.length} billing records (showing last 10):`);
    billingRecords.forEach((record, index) => {
      console.log(`${index + 1}. Order ID: ${record.orderId}`);
      console.log(`   Admin ID: ${record.adminId}`);
      console.log(`   User ID: ${record.userId}`);
      console.log(`   Amount: ₹${record.amount}`);
      console.log(`   Status: ${record.status}`);
      console.log(`   Broker: ${record.broker}`);
      console.log(`   Created: ${record.createdAt}`);
      console.log('');
    });

    // 3. Check specific user who placed orders
    console.log('\n🔍 CHECKING USER WHO PLACED ORDERS:');
    const regularUsers = await User.find({ role: 'user' });
    
    for (const user of regularUsers) {
      console.log(`\nUser: ${user.name} (${user.email})`);
      console.log(`Admin ID: ${user.adminId}`);
      
      if (user.adminId) {
        const admin = await User.findById(user.adminId);
        if (admin) {
          console.log(`✅ Admin found: ${admin.name} (${admin.email})`);
          console.log(`   Admin Order Rate: ₹${admin.orderRate || 0}`);
          console.log(`   Admin Total Orders: ${admin.totalOrders || 0}`);
          console.log(`   Admin Total Billing: ₹${admin.totalBilling || 0}`);
          console.log(`   Admin Super Admin ID: ${admin.superAdminId}`);
          
          // Check if admin has proper super admin link
          if (admin.superAdminId) {
            const superAdmin = await User.findById(admin.superAdminId);
            if (superAdmin) {
              console.log(`   ✅ Super Admin found: ${superAdmin.name}`);
            } else {
              console.log(`   ❌ Super Admin NOT found for ID: ${admin.superAdminId}`);
            }
          } else {
            console.log(`   ❌ Admin has no Super Admin ID`);
          }
        } else {
          console.log(`❌ Admin NOT found for ID: ${user.adminId}`);
        }
      } else {
        console.log(`❌ User has no Admin ID`);
      }
    }

    // 4. Check for any recent orders in billing records
    console.log('\n📊 RECENT BILLING ACTIVITY:');
    const recentBilling = await BillingRecord.find({}).sort({ createdAt: -1 }).limit(5);
    if (recentBilling.length === 0) {
      console.log('❌ NO BILLING RECORDS FOUND!');
      console.log('This means orders are not creating billing records.');
      
      // Check if the issue is in the order placement logic
      console.log('\n🔧 POTENTIAL ISSUES:');
      console.log('1. User placing orders might not have adminId set');
      console.log('2. Admin might not have orderRate set');
      console.log('3. Admin might not have superAdminId set');
      console.log('4. Order placement API might have errors in billing logic');
      
    } else {
      console.log(`✅ Found ${recentBilling.length} recent billing records`);
      recentBilling.forEach((record, index) => {
        console.log(`${index + 1}. ${record.orderId} - ₹${record.amount} (${record.status})`);
      });
    }

    // 5. Fix any issues found
    console.log('\n🔧 FIXING ISSUES:');
    
    // Fix 1: Ensure all admins have order rates
    const adminsWithoutRates = await User.find({ 
      role: 'admin', 
      $or: [
        { orderRate: { $exists: false } },
        { orderRate: 0 },
        { orderRate: null }
      ]
    });
    
    if (adminsWithoutRates.length > 0) {
      console.log(`\n🔧 Fixing ${adminsWithoutRates.length} admins without order rates...`);
      for (const admin of adminsWithoutRates) {
        await User.findByIdAndUpdate(admin._id, {
          $set: { 
            orderRate: 5,
            totalOrders: admin.totalOrders || 0,
            totalBilling: admin.totalBilling || 0
          }
        });
        console.log(`   ✅ Set order rate ₹5 for ${admin.name}`);
      }
    }

    // Fix 2: Ensure all users have admin assignments
    const usersWithoutAdmins = await User.find({ 
      role: 'user', 
      $or: [
        { adminId: { $exists: false } },
        { adminId: null }
      ]
    });
    
    if (usersWithoutAdmins.length > 0) {
      const firstAdmin = await User.findOne({ role: 'admin' });
      if (firstAdmin) {
        console.log(`\n🔧 Assigning ${usersWithoutAdmins.length} users to admin ${firstAdmin.name}...`);
        for (const user of usersWithoutAdmins) {
          await User.findByIdAndUpdate(user._id, {
            $set: { adminId: firstAdmin._id }
          });
          console.log(`   ✅ Assigned ${user.name} to ${firstAdmin.name}`);
        }
      }
    }

    console.log('\n✅ Debugging completed!');
    console.log('\n📋 SUMMARY:');
    console.log(`- Total Users: ${allUsers.length}`);
    console.log(`- Admins: ${allUsers.filter(u => u.role === 'admin').length}`);
    console.log(`- Regular Users: ${allUsers.filter(u => u.role === 'user').length}`);
    console.log(`- Billing Records: ${billingRecords.length}`);
    console.log(`- Admins without rates fixed: ${adminsWithoutRates.length}`);
    console.log(`- Users without admins fixed: ${usersWithoutAdmins.length}`);

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

debugBillingIssue();
