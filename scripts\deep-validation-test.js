#!/usr/bin/env node

/**
 * Deep Validation Testing Script
 * Comprehensive testing for all deep fixes and improvements
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  total: 0,
  details: [],
  performance: {
    buildTime: 0,
    startupTime: 0,
    apiResponseTimes: {},
    memoryUsage: {}
  }
};

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Execute command and return promise
function executeCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    const process = spawn(command, args, {
      stdio: 'pipe',
      shell: true,
      ...options
    });

    let stdout = '';
    let stderr = '';

    process.stdout?.on('data', (data) => {
      stdout += data.toString();
    });

    process.stderr?.on('data', (data) => {
      stderr += data.toString();
    });

    process.on('close', (code) => {
      if (code === 0) {
        resolve({ stdout, stderr, code });
      } else {
        reject(new Error(`Command failed with code ${code}: ${stderr || stdout}`));
      }
    });

    process.on('error', (error) => {
      reject(error);
    });
  });
}

// Test runner function
async function runTest(testName, testFunction) {
  testResults.total++;
  log(`\n🧪 Running: ${testName}`, 'cyan');
  
  const startTime = Date.now();
  
  try {
    await testFunction();
    const duration = Date.now() - startTime;
    testResults.passed++;
    testResults.details.push({ 
      name: testName, 
      status: 'PASSED', 
      duration,
      message: 'Test completed successfully' 
    });
    logSuccess(`${testName} - PASSED (${duration}ms)`);
  } catch (error) {
    const duration = Date.now() - startTime;
    testResults.failed++;
    testResults.details.push({ 
      name: testName, 
      status: 'FAILED', 
      duration,
      message: error.message 
    });
    logError(`${testName} - FAILED (${duration}ms): ${error.message}`);
  }
}

// Deep validation tests
async function testNextJSConfiguration() {
  logInfo('Testing Next.js configuration fixes...');
  
  // Check if next.config.js has the fixes
  const configPath = path.join(process.cwd(), 'next.config.js');
  const configContent = fs.readFileSync(configPath, 'utf8');
  
  if (!configContent.includes('serverExternalPackages')) {
    throw new Error('Next.js configuration missing serverExternalPackages fix');
  }
  
  if (configContent.includes('reactStrictMode: true')) {
    throw new Error('React strict mode should be disabled to prevent hydration issues');
  }
  
  logSuccess('Next.js configuration properly optimized');
}

async function testPerformanceOptimizations() {
  logInfo('Testing performance optimization implementations...');
  
  // Check if performance monitoring exists
  const perfFile = path.join(process.cwd(), 'src/lib/performance.ts');
  if (!fs.existsSync(perfFile)) {
    throw new Error('Performance monitoring system not found');
  }
  
  // Check if caching system exists
  const cacheFile = path.join(process.cwd(), 'src/lib/cache.ts');
  if (!fs.existsSync(cacheFile)) {
    throw new Error('Caching system not found');
  }
  
  // Check if memory manager exists
  const memoryFile = path.join(process.cwd(), 'src/lib/memoryManager.ts');
  if (!fs.existsSync(memoryFile)) {
    throw new Error('Memory manager not found');
  }
  
  logSuccess('Performance optimization systems implemented');
}

async function testDatabaseOptimizations() {
  logInfo('Testing database optimization implementations...');
  
  // Check enhanced MongoDB connection
  const mongoFile = path.join(process.cwd(), 'src/lib/mongodb.ts');
  const mongoContent = fs.readFileSync(mongoFile, 'utf8');
  
  if (!mongoContent.includes('getConnectionHealth')) {
    throw new Error('Database health monitoring not implemented');
  }
  
  if (!mongoContent.includes('maxPoolSize: 15')) {
    throw new Error('Database connection pool not optimized');
  }
  
  // Check if index creation script exists
  const indexScript = path.join(process.cwd(), 'scripts/create-indexes.js');
  if (!fs.existsSync(indexScript)) {
    throw new Error('Database index creation script not found');
  }
  
  logSuccess('Database optimizations implemented');
}

async function testSecurityEnhancements() {
  logInfo('Testing security enhancement implementations...');
  
  // Check validation utilities
  const validationFile = path.join(process.cwd(), 'src/lib/validation.ts');
  if (!fs.existsSync(validationFile)) {
    throw new Error('Validation utilities not found');
  }
  
  // Check rate limiting
  const rateLimitFile = path.join(process.cwd(), 'src/middleware/rateLimit.ts');
  if (!fs.existsSync(rateLimitFile)) {
    throw new Error('Rate limiting middleware not found');
  }
  
  // Check API optimization middleware
  const apiOptFile = path.join(process.cwd(), 'src/middleware/apiOptimization.ts');
  if (!fs.existsSync(apiOptFile)) {
    throw new Error('API optimization middleware not found');
  }
  
  logSuccess('Security enhancements implemented');
}

async function testErrorHandlingImprovements() {
  logInfo('Testing error handling improvements...');
  
  // Check enhanced error boundary
  const errorBoundaryFile = path.join(process.cwd(), 'src/components/ErrorBoundary.tsx');
  const errorBoundaryContent = fs.readFileSync(errorBoundaryFile, 'utf8');
  
  if (!errorBoundaryContent.includes('logErrorToService')) {
    throw new Error('Enhanced error boundary not implemented');
  }
  
  // Check error logging API
  const errorLogFile = path.join(process.cwd(), 'src/app/api/log-error/route.ts');
  if (!fs.existsSync(errorLogFile)) {
    throw new Error('Error logging API not found');
  }
  
  // Check error handler utilities
  const errorHandlerFile = path.join(process.cwd(), 'src/lib/errorHandler.ts');
  if (!fs.existsSync(errorHandlerFile)) {
    throw new Error('Error handler utilities not found');
  }
  
  logSuccess('Error handling improvements implemented');
}

async function testHydrationFixes() {
  logInfo('Testing hydration and SSR fixes...');
  
  // Check ClientOnly component
  const clientOnlyFile = path.join(process.cwd(), 'src/components/ClientOnly.tsx');
  if (!fs.existsSync(clientOnlyFile)) {
    throw new Error('ClientOnly component not found');
  }
  
  // Check if trading page uses hydration-safe components
  const tradingPageFile = path.join(process.cwd(), 'src/app/trading/page.tsx');
  const tradingPageContent = fs.readFileSync(tradingPageFile, 'utf8');
  
  if (!tradingPageContent.includes('ClientOnly')) {
    throw new Error('Trading page not using hydration-safe components');
  }
  
  if (!tradingPageContent.includes('HydrationSafe')) {
    throw new Error('Trading page not using HydrationSafe wrapper');
  }
  
  logSuccess('Hydration fixes implemented');
}

async function testBuildPerformance() {
  logInfo('Testing build performance...');
  
  const buildStartTime = Date.now();
  
  try {
    await executeCommand('npm', ['run', 'build'], { 
      cwd: process.cwd(),
      timeout: 120000 // 2 minute timeout
    });
    
    const buildTime = Date.now() - buildStartTime;
    testResults.performance.buildTime = buildTime;
    
    if (buildTime > 120000) { // 2 minutes
      logWarning(`Build took ${Math.round(buildTime/1000)}s - consider further optimization`);
    } else {
      logSuccess(`Build completed in ${Math.round(buildTime/1000)}s`);
    }
    
  } catch (error) {
    throw new Error(`Build failed: ${error.message}`);
  }
}

async function testEnhancedHealthCheck() {
  logInfo('Testing enhanced health check endpoint...');
  
  const healthFile = path.join(process.cwd(), 'src/app/api/health/route.ts');
  const healthContent = fs.readFileSync(healthFile, 'utf8');
  
  if (!healthContent.includes('getConnectionHealth')) {
    throw new Error('Enhanced health check not implemented');
  }
  
  if (!healthContent.includes('generatePerformanceReport')) {
    throw new Error('Performance reporting not integrated in health check');
  }
  
  if (!healthContent.includes('withFullOptimization')) {
    throw new Error('Health check not using API optimization');
  }
  
  logSuccess('Enhanced health check implemented');
}

// Main test execution
async function runDeepValidation() {
  log('\n🚀 Starting Deep Validation Testing Suite', 'bold');
  log('==============================================', 'cyan');
  
  // Core fixes validation
  await runTest('Next.js Configuration Fixes', testNextJSConfiguration);
  await runTest('Performance Optimizations', testPerformanceOptimizations);
  await runTest('Database Optimizations', testDatabaseOptimizations);
  await runTest('Security Enhancements', testSecurityEnhancements);
  await runTest('Error Handling Improvements', testErrorHandlingImprovements);
  await runTest('Hydration Fixes', testHydrationFixes);
  await runTest('Enhanced Health Check', testEnhancedHealthCheck);
  
  // Performance tests
  await runTest('Build Performance', testBuildPerformance);

  // Print final results
  log('\n📊 DEEP VALIDATION RESULTS', 'bold');
  log('============================', 'cyan');
  log(`✅ Passed: ${testResults.passed}`, 'green');
  log(`❌ Failed: ${testResults.failed}`, 'red');
  log(`📈 Total:  ${testResults.total}`, 'blue');
  log(`🎯 Success Rate: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`, 'magenta');
  
  // Performance summary
  log('\n⚡ PERFORMANCE METRICS', 'bold');
  log('=====================', 'cyan');
  log(`🏗️  Build Time: ${Math.round(testResults.performance.buildTime/1000)}s`, 'blue');
  
  if (testResults.failed > 0) {
    log('\n❌ FAILED TESTS:', 'red');
    testResults.details
      .filter(test => test.status === 'FAILED')
      .forEach(test => {
        log(`   • ${test.name}: ${test.message}`, 'red');
      });
  }
  
  log('\n🎉 Deep Validation Complete!', 'bold');
  
  if (testResults.passed === testResults.total) {
    log('🌟 ALL DEEP FIXES VALIDATED! The project is fully optimized and ready for production.', 'green');
    process.exit(0);
  } else {
    log('⚠️  Some validations failed. Please review the issues above.', 'yellow');
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runDeepValidation().catch(error => {
    logError(`💥 Deep validation crashed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { runDeepValidation, testResults };
