#!/usr/bin/env node

/**
 * Deployment script for the trading platform
 * Handles pre-deployment checks and build process
 */

const { build } = require('./build');
const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Deployment configuration
const config = {
  environment: process.env.NODE_ENV || 'production',
  skipTests: process.argv.includes('--skip-tests'),
  skipLint: process.argv.includes('--skip-lint'),
  verbose: process.argv.includes('--verbose') || process.argv.includes('-v')
};

/**
 * Execute a command and return a promise
 */
function executeCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    if (config.verbose) {
      console.log(`🔧 Executing: ${command} ${args.join(' ')}`);
    }

    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      env: { ...process.env, ...options.env },
      ...options
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
}

/**
 * Check if required files exist
 */
function checkRequiredFiles() {
  console.log('📋 Checking required files...');
  
  const requiredFiles = [
    'package.json',
    'next.config.js',
    'src/app/layout.tsx',
    'src/server/index.ts'
  ];

  const missingFiles = requiredFiles.filter(file => !fs.existsSync(file));
  
  if (missingFiles.length > 0) {
    console.error('❌ Missing required files:');
    missingFiles.forEach(file => console.error(`   - ${file}`));
    throw new Error('Required files are missing');
  }
  
  console.log('✅ All required files present');
}

/**
 * Check environment variables
 */
function checkEnvironment() {
  console.log('🌍 Checking environment variables...');
  
  const requiredEnvVars = [
    'MONGODB_URI',
    'JWT_SECRET'
  ];

  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.warn('⚠️  Missing environment variables:');
    missingVars.forEach(varName => console.warn(`   - ${varName}`));
    console.warn('   Make sure to set these in your deployment environment');
  } else {
    console.log('✅ Environment variables configured');
  }
}

/**
 * Run linting
 */
async function runLint() {
  if (config.skipLint) {
    console.log('⏭️  Skipping linting');
    return;
  }

  console.log('🔍 Running linting...');
  
  try {
    await executeCommand('npm', ['run', 'lint']);
    console.log('✅ Linting passed');
  } catch (error) {
    console.error('❌ Linting failed:', error.message);
    throw error;
  }
}

/**
 * Run type checking
 */
async function runTypeCheck() {
  console.log('🔍 Running type checking...');
  
  try {
    await executeCommand('npx', ['tsc', '--noEmit']);
    console.log('✅ Type checking passed');
  } catch (error) {
    console.error('❌ Type checking failed:', error.message);
    throw error;
  }
}

/**
 * Main deployment function
 */
async function deploy() {
  const startTime = Date.now();
  
  console.log('🚀 Starting deployment process...');
  console.log(`📦 Environment: ${config.environment}`);
  console.log('');

  try {
    // Step 1: Pre-deployment checks
    checkRequiredFiles();
    checkEnvironment();
    console.log('');

    // Step 2: Code quality checks
    await runLint();
    await runTypeCheck();
    console.log('');

    // Step 3: Build the application
    console.log('🏗️  Building application...');
    await build();
    console.log('');

    const duration = ((Date.now() - startTime) / 1000).toFixed(2);
    console.log(`🎉 Deployment preparation completed successfully in ${duration}s!`);
    console.log('');
    console.log('📝 Next steps:');
    console.log('   1. Upload the built files to your server');
    console.log('   2. Set environment variables on the server');
    console.log('   3. Run: npm run start:production');
    
  } catch (error) {
    console.error('💥 Deployment preparation failed:', error.message);
    process.exit(1);
  }
}

/**
 * Show help information
 */
function showHelp() {
  console.log(`
🚀 Trading Platform Deployment Script

Usage: node scripts/deploy.js [options]

Options:
  --help, -h          Show this help message
  --verbose, -v       Show verbose output
  --skip-tests        Skip running tests
  --skip-lint         Skip linting

Environment Variables:
  NODE_ENV           Set deployment environment (default: production)

Examples:
  node scripts/deploy.js                   # Full deployment preparation
  node scripts/deploy.js --verbose         # Verbose output
  node scripts/deploy.js --skip-lint       # Skip linting
`);
}

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  showHelp();
  process.exit(0);
}

// Run the deployment if this script is executed directly
if (require.main === module) {
  deploy();
}

module.exports = { deploy };
