const mongoose = require('mongoose');
require('dotenv').config();

// Import encryption functions (we'll use crypto directly since this is a Node.js script)
const crypto = require('crypto');

// Encryption configuration (same as in encryption.ts)
const ALGORITHM = 'aes-256-gcm';
const SECRET_KEY = process.env.ENCRYPTION_SECRET || 'trading-platform-encryption-key-32-chars-long-secure-2025';
const IV_LENGTH = 16;
const TAG_LENGTH = 16;

// Get encryption key
const getKey = () => {
  return crypto.createHash('sha256').update(SECRET_KEY).digest();
};

// Encrypt function
function encrypt(text) {
  if (!text) return '';

  try {
    const key = getKey();
    const iv = crypto.randomBytes(IV_LENGTH);
    const cipher = crypto.createCipherGCM(ALGORITHM, key, iv);

    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    const tag = cipher.getAuthTag();

    // Return format: iv:tag:encrypted
    return `${iv.toString('hex')}:${tag.toString('hex')}:${encrypted}`;
  } catch (error) {
    console.error('Encryption error:', error);
    return text; // Return original if encryption fails
  }
}

// Check if data is already encrypted
function isEncrypted(data) {
  if (!data) return false;
  const parts = data.split(':');
  return parts.length === 3 && 
         parts[0].length === IV_LENGTH * 2 && 
         parts[1].length === TAG_LENGTH * 2 && 
         parts[2].length > 0;
}

// Mask sensitive data for logging
function maskData(data, visibleChars = 4) {
  if (!data || data.length <= visibleChars * 2) {
    return '*'.repeat(data?.length || 8);
  }
  
  const start = data.substring(0, visibleChars);
  const end = data.substring(data.length - visibleChars);
  const middle = '*'.repeat(Math.max(4, data.length - visibleChars * 2));
  
  return `${start}${middle}${end}`;
}

async function encryptSensitiveData() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.TESTLIST?.trim() || 'mongodb://localhost:27017/trading-platform';
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');

    // Define models
    const AngelUser = mongoose.model('AngelUser', new mongoose.Schema({}, { strict: false }));
    const MotilalUser = mongoose.model('MotilalUser', new mongoose.Schema({}, { strict: false }));
    
    console.log('\n🔐 ENCRYPTING SENSITIVE DATA IN DATABASE');
    console.log('=' .repeat(60));
    
    // 1. Encrypt Angel User sensitive data
    console.log('\n1️⃣ ENCRYPTING ANGEL USER DATA:');
    
    const angelUsers = await AngelUser.find({});
    console.log(`Found ${angelUsers.length} Angel users`);
    
    let angelEncrypted = 0;
    let angelSkipped = 0;
    
    for (const user of angelUsers) {
      console.log(`\nProcessing Angel user: ${user.clientName} (${user.userId})`);
      
      const updates = {};
      let hasUpdates = false;
      
      // Encrypt API Key
      if (user.apiKey && !isEncrypted(user.apiKey)) {
        updates.apiKey = encrypt(user.apiKey);
        hasUpdates = true;
        console.log(`  ✅ Encrypted API Key: ${maskData(user.apiKey)} → ${maskData(updates.apiKey)}`);
      } else if (user.apiKey && isEncrypted(user.apiKey)) {
        console.log(`  ⏭️ API Key already encrypted: ${maskData(user.apiKey)}`);
      }
      
      // Encrypt JWT Token
      if (user.jwtToken && !isEncrypted(user.jwtToken)) {
        updates.jwtToken = encrypt(user.jwtToken);
        hasUpdates = true;
        console.log(`  ✅ Encrypted JWT Token: ${maskData(user.jwtToken)} → ${maskData(updates.jwtToken)}`);
      } else if (user.jwtToken && isEncrypted(user.jwtToken)) {
        console.log(`  ⏭️ JWT Token already encrypted: ${maskData(user.jwtToken)}`);
      }
      
      // Encrypt Refresh Token
      if (user.refreshToken && !isEncrypted(user.refreshToken)) {
        updates.refreshToken = encrypt(user.refreshToken);
        hasUpdates = true;
        console.log(`  ✅ Encrypted Refresh Token: ${maskData(user.refreshToken)} → ${maskData(updates.refreshToken)}`);
      } else if (user.refreshToken && isEncrypted(user.refreshToken)) {
        console.log(`  ⏭️ Refresh Token already encrypted: ${maskData(user.refreshToken)}`);
      }
      
      // Encrypt Feed Token
      if (user.feedToken && !isEncrypted(user.feedToken)) {
        updates.feedToken = encrypt(user.feedToken);
        hasUpdates = true;
        console.log(`  ✅ Encrypted Feed Token: ${maskData(user.feedToken)} → ${maskData(updates.feedToken)}`);
      } else if (user.feedToken && isEncrypted(user.feedToken)) {
        console.log(`  ⏭️ Feed Token already encrypted: ${maskData(user.feedToken)}`);
      }
      
      // Encrypt TOTP Key
      if (user.totpKey && !isEncrypted(user.totpKey)) {
        updates.totpKey = encrypt(user.totpKey);
        hasUpdates = true;
        console.log(`  ✅ Encrypted TOTP Key: ${maskData(user.totpKey)} → ${maskData(updates.totpKey)}`);
      } else if (user.totpKey && isEncrypted(user.totpKey)) {
        console.log(`  ⏭️ TOTP Key already encrypted: ${maskData(user.totpKey)}`);
      }
      
      // Update database if there are changes
      if (hasUpdates) {
        await AngelUser.findByIdAndUpdate(user._id, { $set: updates });
        angelEncrypted++;
        console.log(`  💾 Updated Angel user: ${user.clientName}`);
      } else {
        angelSkipped++;
        console.log(`  ⏭️ No updates needed for: ${user.clientName}`);
      }
    }
    
    // 2. Encrypt Motilal User sensitive data
    console.log('\n2️⃣ ENCRYPTING MOTILAL USER DATA:');
    
    const motilalUsers = await MotilalUser.find({});
    console.log(`Found ${motilalUsers.length} Motilal users`);
    
    let motilalEncrypted = 0;
    let motilalSkipped = 0;
    
    for (const user of motilalUsers) {
      console.log(`\nProcessing Motilal user: ${user.clientName} (${user.userId})`);
      
      const updates = {};
      let hasUpdates = false;
      
      // Encrypt API Key
      if (user.apiKey && !isEncrypted(user.apiKey)) {
        updates.apiKey = encrypt(user.apiKey);
        hasUpdates = true;
        console.log(`  ✅ Encrypted API Key: ${maskData(user.apiKey)} → ${maskData(updates.apiKey)}`);
      } else if (user.apiKey && isEncrypted(user.apiKey)) {
        console.log(`  ⏭️ API Key already encrypted: ${maskData(user.apiKey)}`);
      }
      
      // Encrypt Auth Token
      if (user.authToken && !isEncrypted(user.authToken)) {
        updates.authToken = encrypt(user.authToken);
        hasUpdates = true;
        console.log(`  ✅ Encrypted Auth Token: ${maskData(user.authToken)} → ${maskData(updates.authToken)}`);
      } else if (user.authToken && isEncrypted(user.authToken)) {
        console.log(`  ⏭️ Auth Token already encrypted: ${maskData(user.authToken)}`);
      }
      
      // Encrypt TOTP Key
      if (user.totpKey && !isEncrypted(user.totpKey)) {
        updates.totpKey = encrypt(user.totpKey);
        hasUpdates = true;
        console.log(`  ✅ Encrypted TOTP Key: ${maskData(user.totpKey)} → ${maskData(updates.totpKey)}`);
      } else if (user.totpKey && isEncrypted(user.totpKey)) {
        console.log(`  ⏭️ TOTP Key already encrypted: ${maskData(user.totpKey)}`);
      }
      
      // Update database if there are changes
      if (hasUpdates) {
        await MotilalUser.findByIdAndUpdate(user._id, { $set: updates });
        motilalEncrypted++;
        console.log(`  💾 Updated Motilal user: ${user.clientName}`);
      } else {
        motilalSkipped++;
        console.log(`  ⏭️ No updates needed for: ${user.clientName}`);
      }
    }
    
    // 3. Summary
    console.log('\n3️⃣ ENCRYPTION SUMMARY:');
    console.log(`📱 Angel Users:`);
    console.log(`   Encrypted: ${angelEncrypted}`);
    console.log(`   Skipped: ${angelSkipped}`);
    console.log(`   Total: ${angelUsers.length}`);
    
    console.log(`📱 Motilal Users:`);
    console.log(`   Encrypted: ${motilalEncrypted}`);
    console.log(`   Skipped: ${motilalSkipped}`);
    console.log(`   Total: ${motilalUsers.length}`);
    
    console.log('\n✅ SENSITIVE DATA ENCRYPTION COMPLETED!');
    console.log('\n🔐 SECURITY IMPROVEMENTS:');
    console.log('   ✅ API keys encrypted with AES-256-GCM');
    console.log('   ✅ JWT tokens encrypted');
    console.log('   ✅ TOTP keys encrypted');
    console.log('   ✅ All sensitive data protected');
    console.log('   ✅ Only system can decrypt for use');
    
    console.log('\n⚠️ IMPORTANT NOTES:');
    console.log('   • Keep ENCRYPTION_SECRET secure and backed up');
    console.log('   • Never commit encryption keys to version control');
    console.log('   • Encrypted data is only accessible by this system');
    console.log('   • Admin dashboard will show masked versions');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

encryptSensitiveData();
