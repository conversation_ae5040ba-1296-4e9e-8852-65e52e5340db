const mongoose = require('mongoose');
require('dotenv').config();

async function finalSystemSetup() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.TESTLIST || 'mongodb://localhost:27017/trading-platform';
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');

    // Define models
    const User = mongoose.model('User', new mongoose.Schema({}, { strict: false }));
    const AngelUser = mongoose.model('AngelUser', new mongoose.Schema({}, { strict: false }));
    const MotilalUser = mongoose.model('MotilalUser', new mongoose.Schema({}, { strict: false }));
    const BillingRecord = mongoose.model('BillingRecord', new mongoose.Schema({}, { strict: false }));
    
    console.log('\n🔧 FINAL SYSTEM SETUP & FIXES');
    console.log('=' .repeat(60));
    
    // 1. Get existing data
    const superAdmin = await User.findOne({ role: 'super_admin' });
    const admins = await User.find({ role: 'admin' });
    const angelAccounts = await AngelUser.find({});
    const motilalAccounts = await MotilalUser.find({});
    
    console.log(`👑 Super Admin: ${superAdmin ? superAdmin.name : 'None'}`);
    console.log(`👨‍💼 Admins: ${admins.length}`);
    console.log(`📱 Angel Accounts: ${angelAccounts.length}`);
    console.log(`📱 Motilal Accounts: ${motilalAccounts.length}`);
    
    // 2. Create ONE USER per BROKER ACCOUNT (each broker account = one user)
    console.log('\n2️⃣ CREATING ONE USER PER BROKER ACCOUNT:');

    let userCounter = 1;
    const firstAdmin = admins[0]; // Assign all users to first admin for now

    // Create user for each Angel account
    for (const angelAccount of angelAccounts) {
      const userCode = `USR${firstAdmin.userCode.slice(-2)}${userCounter.toString().padStart(2, '0')}`;

      let user = await User.findOne({ userCode: userCode });
      if (!user) {
        user = new User({
          email: `${angelAccount.clientName.toLowerCase().replace(/\s+/g, '')}${userCounter}@${firstAdmin.email.split('@')[1]}`,
          password: '$2b$12$LQv3c1yqBWVHxkd/X0lhmu.RCCyAVVNpYjo/X0lhmu.RCCyAVVNpYjo',
          name: `${angelAccount.clientName} (Angel)`,
          role: 'user',
          userCode: userCode,
          adminId: firstAdmin._id,
          userOtp: '123456',
          isActive: true,
          companyName: `${angelAccount.clientName} - Angel Trading Account`,
          createdAt: new Date(),
          updatedAt: new Date()
        });
        await user.save();
        console.log(`   ✅ Created user: ${userCode} - ${user.name}`);
      }

      // Assign Angel account to this user
      await AngelUser.findByIdAndUpdate(angelAccount._id, {
        $set: { owner: user._id }
      });
      console.log(`   📱 Assigned Angel account: ${angelAccount.clientName} to ${userCode}`);

      // Create test billing record
      const existingBilling = await BillingRecord.findOne({
        adminId: firstAdmin._id,
        userId: user._id
      });

      if (!existingBilling) {
        const testBilling = new BillingRecord({
          userId: user._id,
          adminId: firstAdmin._id,
          superAdminId: superAdmin._id,
          orderId: `ORDER_${userCode}_${Date.now()}`,
          orderType: 'BUY',
          symbol: 'RELIANCE',
          quantity: 10,
          price: 2500,
          broker: 'angel',
          clientId: angelAccount.userId,
          orderRate: firstAdmin.orderRate || 5,
          amount: firstAdmin.orderRate || 5,
          status: 'completed',
          billingCycle: '2025-01',
          description: `Admin placed order for ${angelAccount.clientName} (Angel)`,
          createdAt: new Date(),
          updatedAt: new Date()
        });
        await testBilling.save();
        console.log(`   💰 Created test billing record for ${userCode}`);
      }

      userCounter++;
    }

    // Create user for each Motilal account
    for (const motilalAccount of motilalAccounts) {
      const userCode = `USR${firstAdmin.userCode.slice(-2)}${userCounter.toString().padStart(2, '0')}`;

      let user = await User.findOne({ userCode: userCode });
      if (!user) {
        user = new User({
          email: `${motilalAccount.clientName.toLowerCase().replace(/\s+/g, '')}${userCounter}@${firstAdmin.email.split('@')[1]}`,
          password: '$2b$12$LQv3c1yqBWVHxkd/X0lhmu.RCCyAVVNpYjo/X0lhmu.RCCyAVVNpYjo',
          name: `${motilalAccount.clientName} (Motilal)`,
          role: 'user',
          userCode: userCode,
          adminId: firstAdmin._id,
          userOtp: '123456',
          isActive: true,
          companyName: `${motilalAccount.clientName} - Motilal Trading Account`,
          createdAt: new Date(),
          updatedAt: new Date()
        });
        await user.save();
        console.log(`   ✅ Created user: ${userCode} - ${user.name}`);
      }

      // Assign Motilal account to this user
      await MotilalUser.findByIdAndUpdate(motilalAccount._id, {
        $set: { owner: user._id }
      });
      console.log(`   📱 Assigned Motilal account: ${motilalAccount.clientName} to ${userCode}`);

      // Create test billing record
      const existingBilling = await BillingRecord.findOne({
        adminId: firstAdmin._id,
        userId: user._id
      });

      if (!existingBilling) {
        const testBilling = new BillingRecord({
          userId: user._id,
          adminId: firstAdmin._id,
          superAdminId: superAdmin._id,
          orderId: `ORDER_${userCode}_${Date.now()}`,
          orderType: 'SELL',
          symbol: 'TCS',
          quantity: 5,
          price: 3200,
          broker: 'motilal',
          clientId: motilalAccount.userId,
          orderRate: firstAdmin.orderRate || 5,
          amount: firstAdmin.orderRate || 5,
          status: 'completed',
          billingCycle: '2025-01',
          description: `Admin placed order for ${motilalAccount.clientName} (Motilal)`,
          createdAt: new Date(),
          updatedAt: new Date()
        });
        await testBilling.save();
        console.log(`   💰 Created test billing record for ${userCode}`);
      }

      userCounter++;
    }
    
    // 3. Update admin billing totals
    console.log('\n3️⃣ UPDATING ADMIN BILLING TOTALS:');

    for (const admin of admins) {
      const adminBilling = await BillingRecord.find({ adminId: admin._id });
      const totalAmount = adminBilling.reduce((sum, record) => sum + (record.amount || 0), 0);

      await User.findByIdAndUpdate(admin._id, {
        $set: {
          totalBilling: totalAmount,
          totalOrders: adminBilling.length
        }
      });

      console.log(`   ✅ Updated ${admin.userCode}: ${adminBilling.length} orders, ₹${totalAmount} total`);
    }
    
    // 4. Final verification
    console.log('\n4️⃣ FINAL VERIFICATION:');
    
    const finalUsers = await User.find({ role: 'user' });
    const finalAngel = await AngelUser.find({});
    const finalMotilal = await MotilalUser.find({});
    const finalBilling = await BillingRecord.find({});
    
    console.log(`\n📊 FINAL COUNTS:`);
    console.log(`👤 Users: ${finalUsers.length}`);
    console.log(`📱 Angel Accounts: ${finalAngel.length}`);
    console.log(`📱 Motilal Accounts: ${finalMotilal.length}`);
    console.log(`💰 Billing Records: ${finalBilling.length}`);
    
    console.log(`\n📱 BROKER ACCOUNT OWNERSHIP:`);
    finalAngel.forEach(account => {
      console.log(`   Angel ${account.clientName}: ${account.owner ? account.owner.userCode + ' - ' + account.owner.name : 'NO OWNER'}`);
    });
    finalMotilal.forEach(account => {
      console.log(`   Motilal ${account.clientName}: ${account.owner ? account.owner.userCode + ' - ' + account.owner.name : 'NO OWNER'}`);
    });
    
    console.log(`\n👨‍💼 ADMIN ISOLATION:`);
    for (const admin of admins) {
      const adminUsers = await User.find({ adminId: admin._id, role: 'user' });
      const userIds = adminUsers.map(u => u._id);
      const adminAngel = await AngelUser.find({ owner: { $in: userIds } });
      const adminMotilal = await MotilalUser.find({ owner: { $in: userIds } });
      
      console.log(`   ${admin.userCode}: ${adminUsers.length} users, ${adminAngel.length} Angel, ${adminMotilal.length} Motilal`);
    }
    
    console.log('\n🔑 LOGIN CREDENTIALS:');
    
    console.log('\n👑 Super Admin:');
    console.log(`   URL: http://localhost:3002/super-admin`);
    console.log(`   Email: ${superAdmin.email}`);
    
    console.log('\n👨‍💼 Admins (Place orders on behalf of users):');
    admins.forEach((admin, index) => {
      console.log(`   ${index + 1}. URL: http://localhost:3002/admin`);
      console.log(`      Email: ${admin.email}`);
    });
    
    console.log('\n👤 Users (View order history):');
    finalUsers.forEach((user, index) => {
      console.log(`   ${index + 1}. URL: http://localhost:3002/user-login`);
      console.log(`      User ID: ${user.userCode}`);
      console.log(`      OTP: ${user.userOtp}`);
    });
    
    console.log('\n✅ FINAL SYSTEM SETUP COMPLETED!');
    console.log('\n🎯 SYSTEM IS NOW PROPERLY CONFIGURED:');
    console.log('   ✅ Users own broker accounts');
    console.log('   ✅ Admins can place orders on behalf of their users');
    console.log('   ✅ Admin isolation is enforced');
    console.log('   ✅ Billing flows correctly');
    console.log('   ✅ All accounts have proper ownership');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

finalSystemSetup();
