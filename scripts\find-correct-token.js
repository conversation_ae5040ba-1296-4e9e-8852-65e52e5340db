/**
 * <PERSON><PERSON><PERSON> to find the correct security ID (token) for 20MICRONS
 */

const { MongoClient } = require('mongodb');
require('dotenv').config();

const MONGODB_URI = process.env.TESTLIST || process.env.MONGODB_URI;

async function findCorrectToken() {
  const client = new MongoClient(MONGODB_URI);
  
  try {
    await client.connect();
    console.log('✅ Connected to MongoDB');

    const db = client.db("Angel_api");
    const collection = db.collection("totalscript");

    // Search for 20MICRONS by name
    console.log('🔍 Searching for 20MICRONS by name...');
    const byName = await collection.find({ 
      name: { $regex: "20MICRONS", $options: 'i' } 
    }).toArray();
    
    console.log(`Found ${byName.length} results by name:`);
    byName.forEach(doc => {
      console.log(`  Token: ${doc.token}, Symbol: ${doc.symbol}, Name: ${doc.name}, Exchange: ${doc.exch_seg}`);
    });

    // Search for 20MICRONS by symbol
    console.log('\n🔍 Searching for 20MICRONS by symbol...');
    const bySymbol = await collection.find({ 
      symbol: { $regex: "20MICRONS", $options: 'i' } 
    }).toArray();
    
    console.log(`Found ${bySymbol.length} results by symbol:`);
    bySymbol.forEach(doc => {
      console.log(`  Token: ${doc.token}, Symbol: ${doc.symbol}, Name: ${doc.name}, Exchange: ${doc.exch_seg}`);
    });

    // Search for MICRONS (partial match)
    console.log('\n🔍 Searching for MICRONS (partial)...');
    const byPartial = await collection.find({ 
      $or: [
        { name: { $regex: "MICRONS", $options: 'i' } },
        { symbol: { $regex: "MICRONS", $options: 'i' } }
      ]
    }).limit(10).toArray();
    
    console.log(`Found ${byPartial.length} results with MICRONS:`);
    byPartial.forEach(doc => {
      console.log(`  Token: ${doc.token}, Symbol: ${doc.symbol}, Name: ${doc.name}, Exchange: ${doc.exch_seg}`);
    });

    // Check what token 16921 was (should be null now)
    console.log('\n🔍 Checking what token 16921 is...');
    const token16921 = await collection.findOne({ token: "16921" });
    if (token16921) {
      console.log(`Token 16921: ${token16921.symbol} (${token16921.name}) on ${token16921.exch_seg}`);
    } else {
      console.log('Token 16921: Not found (successfully removed)');
    }

    // Show some sample NSE equity tokens
    console.log('\n📋 Sample NSE equity tokens:');
    const sampleEquities = await collection.find({ 
      exch_seg: "NSE",
      symbol: { $regex: "-EQ$" }
    }).limit(5).toArray();
    
    sampleEquities.forEach(doc => {
      console.log(`  Token: ${doc.token}, Symbol: ${doc.symbol}, Name: ${doc.name}`);
    });

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await client.close();
  }
}

findCorrectToken();
