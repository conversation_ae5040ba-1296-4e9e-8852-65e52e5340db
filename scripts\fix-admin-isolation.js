const mongoose = require('mongoose');
require('dotenv').config();

async function fixAdminIsolation() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.TESTLIST || 'mongodb://localhost:27017/trading-platform';
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');

    // Define models
    const User = mongoose.model('User', new mongoose.Schema({}, { strict: false }));
    const AngelUser = mongoose.model('AngelUser', new mongoose.Schema({}, { strict: false }));
    const MotilalUser = mongoose.model('MotilalUser', new mongoose.Schema({}, { strict: false }));
    const BillingRecord = mongoose.model('BillingRecord', new mongoose.Schema({}, { strict: false }));
    const OrderResponse = mongoose.model('OrderResponse', new mongoose.Schema({}, { strict: false }));
    
    console.log('\n🔧 FIXING ADMIN ISOLATION & PROPER MANAGEMENT');
    console.log('=' .repeat(60));
    
    // 1. Analyze current structure
    console.log('\n1️⃣ ANALYZING CURRENT STRUCTURE:');
    
    const superAdmins = await User.find({ role: 'super_admin' });
    const admins = await User.find({ role: 'admin' });
    const users = await User.find({ role: 'user' });
    const angelAccounts = await AngelUser.find({});
    const motilalAccounts = await MotilalUser.find({});
    
    console.log(`👑 Super Admins: ${superAdmins.length}`);
    console.log(`👨‍💼 Admins: ${admins.length}`);
    console.log(`👤 Users (Broker Accounts): ${users.length}`);
    console.log(`📱 Angel Accounts: ${angelAccounts.length}`);
    console.log(`📱 Motilal Accounts: ${motilalAccounts.length}`);
    
    // 2. Fix admin-user relationships
    console.log('\n2️⃣ FIXING ADMIN-USER RELATIONSHIPS:');
    
    for (const admin of admins) {
      console.log(`\n👨‍💼 Admin: ${admin.userCode || admin.name} (${admin.email})`);
      
      // Get users under this admin
      const adminUsers = await User.find({ adminId: admin._id, role: 'user' });
      console.log(`   Users under this admin: ${adminUsers.length}`);
      
      adminUsers.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.userCode || user.name} - ${user.email}`);
      });
      
      // Ensure admin has proper codes and settings
      if (!admin.userCode) {
        const newCode = `ADM${Date.now().toString().slice(-5)}`;
        await User.findByIdAndUpdate(admin._id, { $set: { userCode: newCode } });
        console.log(`   ✅ Set admin code: ${newCode}`);
      }
      
      if (!admin.orderRate || admin.orderRate === 0) {
        await User.findByIdAndUpdate(admin._id, { $set: { orderRate: 5 } });
        console.log(`   ✅ Set default order rate: ₹5`);
      }
    }
    
    // 3. Fix broker account ownership (Users own broker accounts)
    console.log('\n3️⃣ FIXING BROKER ACCOUNT OWNERSHIP:');
    
    // Fix Angel accounts
    for (const angelAccount of angelAccounts) {
      const owner = await User.findById(angelAccount.owner);
      console.log(`📱 Angel Account: ${angelAccount.clientName || angelAccount.userId}`);
      
      if (!owner) {
        // Find a user to assign this account to
        const availableUser = await User.findOne({ role: 'user' });
        if (availableUser) {
          await AngelUser.findByIdAndUpdate(angelAccount._id, { 
            $set: { owner: availableUser._id } 
          });
          console.log(`   ✅ Assigned to user: ${availableUser.userCode || availableUser.name}`);
        }
      } else if (owner.role !== 'user') {
        // If owned by admin, find a user under that admin
        const userUnderAdmin = await User.findOne({ 
          adminId: owner.role === 'admin' ? owner._id : owner.adminId,
          role: 'user' 
        });
        if (userUnderAdmin) {
          await AngelUser.findByIdAndUpdate(angelAccount._id, { 
            $set: { owner: userUnderAdmin._id } 
          });
          console.log(`   ✅ Reassigned to user: ${userUnderAdmin.userCode || userUnderAdmin.name}`);
        }
      } else {
        console.log(`   ✅ Already owned by user: ${owner.userCode || owner.name}`);
      }
    }
    
    // Fix Motilal accounts
    for (const motilalAccount of motilalAccounts) {
      const owner = await User.findById(motilalAccount.owner);
      console.log(`📱 Motilal Account: ${motilalAccount.clientName || motilalAccount.userId}`);
      
      if (!owner) {
        // Find a user to assign this account to
        const availableUser = await User.findOne({ role: 'user' });
        if (availableUser) {
          await MotilalUser.findByIdAndUpdate(motilalAccount._id, { 
            $set: { owner: availableUser._id } 
          });
          console.log(`   ✅ Assigned to user: ${availableUser.userCode || availableUser.name}`);
        }
      } else if (owner.role !== 'user') {
        // If owned by admin, find a user under that admin
        const userUnderAdmin = await User.findOne({ 
          adminId: owner.role === 'admin' ? owner._id : owner.adminId,
          role: 'user' 
        });
        if (userUnderAdmin) {
          await MotilalUser.findByIdAndUpdate(motilalAccount._id, { 
            $set: { owner: userUnderAdmin._id } 
          });
          console.log(`   ✅ Reassigned to user: ${userUnderAdmin.userCode || userUnderAdmin.name}`);
        }
      } else {
        console.log(`   ✅ Already owned by user: ${owner.userCode || owner.name}`);
      }
    }
    
    // 4. Ensure all users have proper codes and OTPs
    console.log('\n4️⃣ FIXING USER CODES AND OTPs:');
    
    for (const user of users) {
      let needsUpdate = false;
      const updateData = {};
      
      if (!user.userCode) {
        updateData.userCode = `USR${Date.now().toString().slice(-5)}${Math.random().toString(36).substr(2, 2).toUpperCase()}`;
        needsUpdate = true;
      }
      
      if (!user.userOtp) {
        updateData.userOtp = '123456';
        needsUpdate = true;
      }
      
      if (!user.adminId && admins.length > 0) {
        updateData.adminId = admins[0]._id;
        needsUpdate = true;
      }
      
      if (needsUpdate) {
        await User.findByIdAndUpdate(user._id, { $set: updateData });
        console.log(`✅ Updated user: ${updateData.userCode || user.userCode} - ${user.name}`);
      }
    }
    
    // 5. Create test billing data to show proper flow
    console.log('\n5️⃣ CREATING TEST BILLING DATA:');
    
    // Clear existing test data
    await BillingRecord.deleteMany({ orderId: { $regex: /^TEST_/ } });
    
    for (const admin of admins) {
      const adminUsers = await User.find({ adminId: admin._id, role: 'user' });
      
      if (adminUsers.length > 0) {
        const testUser = adminUsers[0];
        
        const testBilling = {
          userId: testUser._id, // Order belongs to USER
          adminId: admin._id, // Billing goes to ADMIN
          superAdminId: superAdmins[0]?._id,
          orderId: `TEST_${admin.userCode}_${Date.now()}`,
          orderType: 'BUY',
          symbol: 'RELIANCE',
          quantity: 10,
          price: 2500,
          broker: 'angel',
          clientId: testUser.userCode,
          orderRate: admin.orderRate || 5,
          amount: admin.orderRate || 5,
          status: 'completed',
          billingCycle: '2025-01',
          description: `Admin ${admin.userCode} placed order for user ${testUser.userCode}`,
          createdAt: new Date(),
          updatedAt: new Date()
        };
        
        await BillingRecord.create(testBilling);
        console.log(`✅ Created test billing for admin ${admin.userCode} -> user ${testUser.userCode}`);
      }
    }
    
    // 6. Final verification
    console.log('\n6️⃣ FINAL VERIFICATION:');
    
    console.log('\n📊 ADMIN ISOLATION CHECK:');
    for (const admin of admins) {
      const adminUsers = await User.find({ adminId: admin._id, role: 'user' });
      const adminBilling = await BillingRecord.find({ adminId: admin._id });
      
      console.log(`👨‍💼 ${admin.userCode} - ${admin.name}:`);
      console.log(`   Users: ${adminUsers.length}`);
      console.log(`   Billing Records: ${adminBilling.length}`);
      console.log(`   Order Rate: ₹${admin.orderRate || 0}`);
      
      adminUsers.forEach((user, index) => {
        console.log(`   ${index + 1}. User: ${user.userCode} - ${user.name}`);
      });
    }
    
    console.log('\n🔑 LOGIN CREDENTIALS:');
    
    // Super Admin
    if (superAdmins.length > 0) {
      console.log(`👑 Super Admin: ${superAdmins[0].email}`);
    }
    
    // Admins
    admins.forEach((admin, index) => {
      console.log(`👨‍💼 Admin ${index + 1}: ${admin.email} (manages ${users.filter(u => u.adminId?.toString() === admin._id.toString()).length} users)`);
    });
    
    // Users
    users.forEach((user, index) => {
      console.log(`👤 User ${index + 1}: ${user.userCode} - OTP: ${user.userOtp || '123456'}`);
    });
    
    console.log('\n✅ ADMIN ISOLATION & MANAGEMENT FIXED!');
    console.log('\n🎯 SYSTEM RULES:');
    console.log('   • Each admin sees ONLY their users');
    console.log('   • Users = Broker accounts (owned by users)');
    console.log('   • Admins place orders on behalf of their users');
    console.log('   • Billing goes to admin, attribution to user');
    console.log('   • Super admin sees everything');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

fixAdminIsolation();
