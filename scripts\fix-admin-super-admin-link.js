const mongoose = require('mongoose');
require('dotenv').config();

async function fixAdminSuperAdminLink() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.TESTLIST || 'mongodb://localhost:27017/trading-platform';
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');

    // Define models
    const User = mongoose.model('User', new mongoose.Schema({}, { strict: false }));
    
    console.log('\n🔧 FIXING ADMIN-SUPER ADMIN LINKS');
    console.log('=' .repeat(50));
    
    // Get super admin
    const superAdmin = await User.findOne({ role: 'super_admin' });
    if (!superAdmin) {
      console.log('❌ No super admin found');
      return;
    }
    
    console.log(`✅ Super Admin: ${superAdmin.name} (${superAdmin.email})`);
    console.log(`   Super Admin ID: ${superAdmin._id}`);

    // Get all admins
    const allAdmins = await User.find({ role: 'admin' });
    console.log(`\nFound ${allAdmins.length} admins:`);
    
    for (let i = 0; i < allAdmins.length; i++) {
      const admin = allAdmins[i];
      console.log(`\n${i + 1}. Admin: ${admin.name} (${admin.email})`);
      console.log(`   Current superAdminId: ${admin.superAdminId || 'NOT SET'}`);
      console.log(`   Order Rate: ₹${admin.orderRate || 0}`);
      
      let needsUpdate = false;
      const updates = {};
      
      // Check if superAdminId is missing or incorrect
      if (!admin.superAdminId || admin.superAdminId.toString() !== superAdmin._id.toString()) {
        console.log(`   ❌ SuperAdminId needs fixing`);
        updates.superAdminId = superAdmin._id;
        needsUpdate = true;
      }
      
      // Ensure admin has order rate
      if (!admin.orderRate || admin.orderRate === 0) {
        console.log(`   ❌ Order rate needs setting`);
        updates.orderRate = 5; // Default ₹5 per order
        needsUpdate = true;
      }
      
      // Ensure admin has billing fields
      if (admin.totalOrders === undefined) {
        updates.totalOrders = 0;
        needsUpdate = true;
      }
      
      if (admin.totalBilling === undefined) {
        updates.totalBilling = 0;
        needsUpdate = true;
      }
      
      if (!admin.lastBillingDate) {
        updates.lastBillingDate = new Date();
        needsUpdate = true;
      }
      
      if (needsUpdate) {
        await User.findByIdAndUpdate(admin._id, { $set: updates });
        console.log(`   ✅ Updated admin with:`, updates);
      } else {
        console.log(`   ✅ Admin already properly configured`);
      }
    }

    // Verify all users
    console.log('\n✅ FINAL VERIFICATION:');
    
    const allUsers = await User.find({}).select('name email role adminId superAdminId orderRate totalOrders totalBilling');
    
    console.log('\n👑 Super Admin:');
    const verifiedSuperAdmin = allUsers.find(u => u.role === 'super_admin');
    if (verifiedSuperAdmin) {
      console.log(`- ${verifiedSuperAdmin.name} (${verifiedSuperAdmin.email})`);
    }
    
    console.log('\n👥 Admins:');
    const verifiedAdmins = allUsers.filter(u => u.role === 'admin');
    verifiedAdmins.forEach((admin, index) => {
      console.log(`${index + 1}. ${admin.name} (${admin.email})`);
      console.log(`   SuperAdminId: ${admin.superAdminId ? '✅ Set' : '❌ Missing'}`);
      console.log(`   Order Rate: ₹${admin.orderRate || 0}`);
      console.log(`   Total Orders: ${admin.totalOrders || 0}`);
      console.log(`   Total Billing: ₹${admin.totalBilling || 0}`);
    });
    
    console.log('\n👤 Users:');
    const verifiedUsers = allUsers.filter(u => u.role === 'user');
    verifiedUsers.forEach((user, index) => {
      console.log(`${index + 1}. ${user.name} (${user.email})`);
      console.log(`   AdminId: ${user.adminId ? '✅ Set' : '❌ Missing'}`);
    });

    console.log('\n📋 SUMMARY:');
    console.log(`- Super Admin: 1`);
    console.log(`- Admins: ${verifiedAdmins.length}`);
    console.log(`- Users: ${verifiedUsers.length}`);
    console.log(`- Admins with superAdminId: ${verifiedAdmins.filter(a => a.superAdminId).length}`);
    console.log(`- Admins with order rate: ${verifiedAdmins.filter(a => a.orderRate && a.orderRate > 0).length}`);
    console.log(`- Users with adminId: ${verifiedUsers.filter(u => u.adminId).length}`);
    
    const allProperlyLinked = verifiedAdmins.every(a => a.superAdminId && a.orderRate > 0) && 
                             verifiedUsers.every(u => u.adminId);
    
    if (allProperlyLinked) {
      console.log('✅ All users are properly linked! Billing should work now.');
    } else {
      console.log('⚠️  Some users still need fixing.');
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

fixAdminSuperAdminLink();
