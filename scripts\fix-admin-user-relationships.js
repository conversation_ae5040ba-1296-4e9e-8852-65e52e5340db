const mongoose = require('mongoose');
require('dotenv').config();

async function fixAdminUserRelationships() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.TESTLIST || 'mongodb://localhost:27017/trading-platform';
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');

    // Define User model
    const User = mongoose.model('User', new mongoose.Schema({}, { strict: false }));
    
    console.log('\n📊 Current Database State:');
    
    // Get super admin
    const superAdmin = await User.findOne({ role: 'super_admin' });
    console.log(`👑 Super Admin: ${superAdmin ? superAdmin.name + ' (' + superAdmin.email + ')' : 'Not found'}`);
    
    if (!superAdmin) {
      console.log('❌ No super admin found. Please create one first.');
      return;
    }

    // Get all admins
    const allAdmins = await User.find({ role: 'admin' });
    console.log(`\n👥 All Admins (${allAdmins.length}):`);
    allAdmins.forEach((admin, index) => {
      console.log(`${index + 1}. ${admin.name} (${admin.email})`);
      console.log(`   - Super Admin ID: ${admin.superAdminId || 'NOT SET'}`);
      console.log(`   - Order Rate: ${admin.orderRate || 'NOT SET'}`);
      console.log(`   - Total Orders: ${admin.totalOrders || 0}`);
      console.log(`   - Total Billing: ${admin.totalBilling || 0}`);
    });

    // Get all users
    const allUsers = await User.find({ role: 'user' });
    console.log(`\n👤 All Users (${allUsers.length}):`);
    allUsers.forEach((user, index) => {
      console.log(`${index + 1}. ${user.name} (${user.email})`);
      console.log(`   - Admin ID: ${user.adminId || 'NOT SET'}`);
      console.log(`   - Super Admin ID: ${user.superAdminId || 'NOT SET'}`);
    });

    console.log('\n🔧 Fixing relationships...');

    // Fix 1: Link all admins to super admin if not already linked
    const orphanedAdmins = await User.find({ 
      role: 'admin', 
      $or: [
        { superAdminId: { $exists: false } },
        { superAdminId: null }
      ]
    });

    if (orphanedAdmins.length > 0) {
      console.log(`\n🔗 Linking ${orphanedAdmins.length} orphaned admins to super admin...`);
      
      for (const admin of orphanedAdmins) {
        await User.findByIdAndUpdate(admin._id, {
          $set: {
            superAdminId: superAdmin._id,
            orderRate: admin.orderRate || 5, // Default rate
            totalOrders: admin.totalOrders || 0,
            totalBilling: admin.totalBilling || 0,
            lastBillingDate: admin.lastBillingDate || new Date()
          }
        });
        console.log(`   ✅ Linked ${admin.name} to super admin`);
      }
    }

    // Fix 2: Find users without adminId and try to assign them to an admin
    const orphanedUsers = await User.find({ 
      role: 'user', 
      $or: [
        { adminId: { $exists: false } },
        { adminId: null }
      ]
    });

    if (orphanedUsers.length > 0) {
      console.log(`\n👤 Found ${orphanedUsers.length} orphaned users`);
      
      // Get the first admin to assign orphaned users to
      const firstAdmin = await User.findOne({ 
        role: 'admin', 
        superAdminId: superAdmin._id 
      });

      if (firstAdmin) {
        console.log(`🔗 Assigning orphaned users to admin: ${firstAdmin.name}`);
        
        for (const user of orphanedUsers) {
          await User.findByIdAndUpdate(user._id, {
            $set: { adminId: firstAdmin._id }
          });
          console.log(`   ✅ Assigned ${user.name} to ${firstAdmin.name}`);
        }
      } else {
        console.log('⚠️ No admin found to assign orphaned users to');
      }
    }

    // Fix 3: Ensure all users have proper hierarchy
    const usersWithoutProperHierarchy = await User.find({ 
      role: 'user',
      adminId: { $exists: true, $ne: null }
    });

    console.log(`\n🔍 Checking hierarchy for ${usersWithoutProperHierarchy.length} users...`);
    
    for (const user of usersWithoutProperHierarchy) {
      const admin = await User.findById(user.adminId);
      if (admin && admin.superAdminId?.toString() !== superAdmin._id.toString()) {
        console.log(`⚠️ User ${user.name} is assigned to admin ${admin.name} who is not under the super admin`);
      }
    }

    console.log('\n📊 Final State:');
    
    // Show final counts
    const finalAdmins = await User.find({ 
      role: 'admin', 
      superAdminId: superAdmin._id 
    });
    
    const finalUsers = await User.find({ 
      role: 'user',
      adminId: { $in: finalAdmins.map(a => a._id) }
    });

    const finalOrphanedUsers = await User.find({ 
      role: 'user',
      $or: [
        { adminId: { $exists: false } },
        { adminId: null }
      ]
    });

    console.log(`👑 Super Admin: ${superAdmin.name}`);
    console.log(`👥 Admins under Super Admin: ${finalAdmins.length}`);
    console.log(`👤 Users under Admins: ${finalUsers.length}`);
    console.log(`🔄 Orphaned Users: ${finalOrphanedUsers.length}`);

    console.log('\n✅ Relationship fixing completed!');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

fixAdminUserRelationships();
