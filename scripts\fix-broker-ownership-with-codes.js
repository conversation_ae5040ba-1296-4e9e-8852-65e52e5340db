const mongoose = require('mongoose');
require('dotenv').config();

async function fixBrokerOwnershipWithCodes() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.TESTLIST || 'mongodb://localhost:27017/trading-platform';
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');

    // Define models
    const User = mongoose.model('User', new mongoose.Schema({}, { strict: false }));
    const AngelUser = mongoose.model('AngelUser', new mongoose.Schema({}, { strict: false }));
    const MotilalUser = mongoose.model('MotilalUser', new mongoose.Schema({}, { strict: false }));
    
    console.log('\n🔧 FIXING BROKER OWNERSHIP WITH USER CODES');
    console.log('=' .repeat(50));
    
    // Get all users with their codes
    const allUsers = await User.find({}).select('_id name email role adminId userCode');
    console.log(`Found ${allUsers.length} users in the system`);
    
    allUsers.forEach((user, index) => {
      console.log(`${index + 1}. ${user.userCode} - ${user.name} (${user.email}) - Role: ${user.role}, AdminId: ${user.adminId || 'None'}`);
    });

    // Find the correct user (the one with role 'user' and has adminId)
    const correctUser = allUsers.find(u => u.role === 'user' && u.adminId);
    
    if (!correctUser) {
      console.log('❌ No user with role "user" and adminId found');
      return;
    }
    
    console.log(`\n✅ Correct user identified: ${correctUser.userCode} - ${correctUser.name} (${correctUser.email})`);
    console.log(`   User ID: ${correctUser._id}`);
    console.log(`   Admin ID: ${correctUser.adminId}`);

    // Get the admin to verify
    const admin = await User.findById(correctUser.adminId);
    if (admin) {
      console.log(`✅ Admin found: ${admin.userCode} - ${admin.name} (Rate: ₹${admin.orderRate || 0})`);
    }

    // Fix Angel users
    console.log('\n📊 FIXING ANGEL USERS:');
    const angelUsers = await AngelUser.find({});
    console.log(`Found ${angelUsers.length} Angel users`);
    
    for (let i = 0; i < angelUsers.length; i++) {
      const angelUser = angelUsers[i];
      console.log(`\n${i + 1}. Angel User: ${angelUser.clientName || angelUser.userId}`);
      
      // Get current owner
      const currentOwner = await User.findById(angelUser.owner);
      console.log(`   Current owner: ${currentOwner ? currentOwner.userCode + ' - ' + currentOwner.name + ' (' + currentOwner.role + ')' : 'None'}`);
      
      if (!currentOwner || currentOwner._id.toString() !== correctUser._id.toString()) {
        await AngelUser.findByIdAndUpdate(angelUser._id, {
          $set: { owner: correctUser._id }
        });
        console.log(`   ✅ Updated owner to: ${correctUser.userCode} - ${correctUser.name} (user with adminId)`);
      } else {
        console.log(`   ✅ Owner already correct`);
      }
    }

    // Fix Motilal users
    console.log('\n📈 FIXING MOTILAL USERS:');
    const motilalUsers = await MotilalUser.find({});
    console.log(`Found ${motilalUsers.length} Motilal users`);
    
    for (let i = 0; i < motilalUsers.length; i++) {
      const motilalUser = motilalUsers[i];
      console.log(`\n${i + 1}. Motilal User: ${motilalUser.clientName || motilalUser.userId}`);
      
      // Get current owner
      const currentOwner = await User.findById(motilalUser.owner);
      console.log(`   Current owner: ${currentOwner ? currentOwner.userCode + ' - ' + currentOwner.name + ' (' + currentOwner.role + ')' : 'None'}`);
      
      if (!currentOwner || currentOwner._id.toString() !== correctUser._id.toString()) {
        await MotilalUser.findByIdAndUpdate(motilalUser._id, {
          $set: { owner: correctUser._id }
        });
        console.log(`   ✅ Updated owner to: ${correctUser.userCode} - ${correctUser.name} (user with adminId)`);
      } else {
        console.log(`   ✅ Owner already correct`);
      }
    }

    // Verify the fixes
    console.log('\n✅ FINAL VERIFICATION:');
    
    const fixedAngelUsers = await AngelUser.find({});
    const fixedMotilalUsers = await MotilalUser.find({});
    
    console.log('\n📊 Angel Users after fix:');
    for (const angelUser of fixedAngelUsers) {
      const owner = await User.findById(angelUser.owner);
      console.log(`- ${angelUser.clientName}: Owner = ${owner ? owner.userCode + ' - ' + owner.name + ' (' + owner.role + ', adminId: ' + (owner.adminId || 'None') + ')' : 'NOT FOUND'}`);
    }
    
    console.log('\n📈 Motilal Users after fix:');
    for (const motilalUser of fixedMotilalUsers) {
      const owner = await User.findById(motilalUser.owner);
      console.log(`- ${motilalUser.clientName || motilalUser.userId}: Owner = ${owner ? owner.userCode + ' - ' + owner.name + ' (' + owner.role + ', adminId: ' + (owner.adminId || 'None') + ')' : 'NOT FOUND'}`);
    }

    console.log('\n📋 SUMMARY:');
    console.log(`- Angel users fixed: ${angelUsers.length}`);
    console.log(`- Motilal users fixed: ${motilalUsers.length}`);
    console.log(`- Correct owner: ${correctUser.userCode} - ${correctUser.name} (${correctUser.email})`);
    console.log(`- Owner role: ${correctUser.role}`);
    console.log(`- Owner has adminId: ${correctUser.adminId ? 'Yes' : 'No'}`);
    
    if (correctUser.adminId && admin) {
      console.log(`- Admin: ${admin.userCode} - ${admin.name} (Rate: ₹${admin.orderRate || 0})`);
      console.log('✅ Billing should now work when orders are placed!');
    } else {
      console.log('⚠️  Owner has no admin - billing may not work');
    }

    // Test the billing logic path
    console.log('\n🧪 TESTING BILLING LOGIC PATH:');
    const testAngelUser = await AngelUser.findOne({});
    if (testAngelUser) {
      const testOwner = await User.findById(testAngelUser.owner);
      console.log(`Angel user owner: ${testOwner ? testOwner.userCode + ' - ' + testOwner.name : 'Not found'}`);
      console.log(`Owner adminId: ${testOwner?.adminId || 'None'}`);
      
      if (testOwner && testOwner.adminId) {
        const testAdmin = await User.findById(testOwner.adminId);
        console.log(`Admin: ${testAdmin ? testAdmin.userCode + ' - ' + testAdmin.name : 'Not found'}`);
        console.log(`Admin orderRate: ${testAdmin?.orderRate || 0}`);
        
        if (testAdmin && testAdmin.orderRate && testAdmin.orderRate > 0) {
          console.log('✅ Billing logic should work - all conditions met!');
          console.log(`   Flow: Order → ${testOwner.userCode} (user) → ${testAdmin.userCode} (admin) → ₹${testAdmin.orderRate} billing`);
        } else {
          console.log('❌ Admin has no order rate');
        }
      } else {
        console.log('❌ Owner has no adminId');
      }
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

fixBrokerOwnershipWithCodes();
