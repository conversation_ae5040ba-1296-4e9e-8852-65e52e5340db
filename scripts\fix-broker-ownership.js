const mongoose = require('mongoose');
require('dotenv').config();

async function fixBrokerOwnership() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.TESTLIST || 'mongodb://localhost:27017/trading-platform';
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');

    // Define models
    const User = mongoose.model('User', new mongoose.Schema({}, { strict: false }));
    const AngelUser = mongoose.model('AngelUser', new mongoose.Schema({}, { strict: false }));
    const MotilalUser = mongoose.model('MotilalUser', new mongoose.Schema({}, { strict: false }));
    
    console.log('\n🔧 FIXING BROKER OWNERSHIP');
    console.log('=' .repeat(50));
    
    // Get all users
    const allUsers = await User.find({}).select('_id name email role adminId');
    console.log(`Found ${allUsers.length} users in the system`);
    
    // Find a user who can own broker accounts (preferably the one with adminId)
    const userWithAdmin = allUsers.find(u => u.role === 'user' && u.adminId);
    const adminUser = allUsers.find(u => u.role === 'admin');
    const anyUser = allUsers.find(u => u.role === 'user');
    
    // Choose the best owner
    const ownerUser = userWithAdmin || anyUser || adminUser;
    
    if (!ownerUser) {
      console.log('❌ No suitable user found to own broker accounts');
      return;
    }
    
    console.log(`✅ Selected owner: ${ownerUser.name} (${ownerUser.email}) - Role: ${ownerUser.role}`);
    console.log(`   Owner ID: ${ownerUser._id}`);
    console.log(`   Admin ID: ${ownerUser.adminId || 'None'}`);

    // Fix Angel users
    console.log('\n📊 FIXING ANGEL USERS:');
    const angelUsers = await AngelUser.find({});
    console.log(`Found ${angelUsers.length} Angel users`);
    
    for (let i = 0; i < angelUsers.length; i++) {
      const angelUser = angelUsers[i];
      console.log(`\n${i + 1}. Angel User: ${angelUser.clientName || angelUser.userId}`);
      console.log(`   Current owner: ${angelUser.owner || 'None'}`);
      
      // Check if owner exists and is valid
      let needsUpdate = false;
      if (!angelUser.owner) {
        console.log('   ❌ No owner set');
        needsUpdate = true;
      } else {
        try {
          const existingOwner = await User.findById(angelUser.owner);
          if (!existingOwner) {
            console.log('   ❌ Owner not found in database');
            needsUpdate = true;
          } else {
            console.log(`   ✅ Owner exists: ${existingOwner.name}`);
          }
        } catch (error) {
          console.log('   ❌ Invalid owner ID format');
          needsUpdate = true;
        }
      }
      
      if (needsUpdate) {
        await AngelUser.findByIdAndUpdate(angelUser._id, {
          $set: { owner: ownerUser._id }
        });
        console.log(`   ✅ Updated owner to: ${ownerUser.name}`);
      }
    }

    // Fix Motilal users
    console.log('\n📈 FIXING MOTILAL USERS:');
    const motilalUsers = await MotilalUser.find({});
    console.log(`Found ${motilalUsers.length} Motilal users`);
    
    for (let i = 0; i < motilalUsers.length; i++) {
      const motilalUser = motilalUsers[i];
      console.log(`\n${i + 1}. Motilal User: ${motilalUser.clientName || motilalUser.userId}`);
      console.log(`   Current owner: ${motilalUser.owner || 'None'}`);
      
      // Check if owner exists and is valid
      let needsUpdate = false;
      if (!motilalUser.owner) {
        console.log('   ❌ No owner set');
        needsUpdate = true;
      } else {
        try {
          const existingOwner = await User.findById(motilalUser.owner);
          if (!existingOwner) {
            console.log('   ❌ Owner not found in database');
            needsUpdate = true;
          } else {
            console.log(`   ✅ Owner exists: ${existingOwner.name}`);
          }
        } catch (error) {
          console.log('   ❌ Invalid owner ID format');
          needsUpdate = true;
        }
      }
      
      if (needsUpdate) {
        await MotilalUser.findByIdAndUpdate(motilalUser._id, {
          $set: { owner: ownerUser._id }
        });
        console.log(`   ✅ Updated owner to: ${ownerUser.name}`);
      }
    }

    // Verify the fixes
    console.log('\n✅ VERIFICATION:');
    
    const fixedAngelUsers = await AngelUser.find({});
    const fixedMotilalUsers = await MotilalUser.find({});
    
    console.log('\n📊 Angel Users after fix:');
    for (const angelUser of fixedAngelUsers) {
      const owner = await User.findById(angelUser.owner);
      console.log(`- ${angelUser.clientName}: Owner = ${owner ? owner.name : 'NOT FOUND'}`);
    }
    
    console.log('\n📈 Motilal Users after fix:');
    for (const motilalUser of fixedMotilalUsers) {
      const owner = await User.findById(motilalUser.owner);
      console.log(`- ${motilalUser.clientName || motilalUser.userId}: Owner = ${owner ? owner.name : 'NOT FOUND'}`);
    }

    console.log('\n📋 SUMMARY:');
    console.log(`- Angel users fixed: ${angelUsers.length}`);
    console.log(`- Motilal users fixed: ${motilalUsers.length}`);
    console.log(`- Owner assigned: ${ownerUser.name} (${ownerUser.email})`);
    console.log(`- Owner has adminId: ${ownerUser.adminId ? 'Yes' : 'No'}`);
    
    if (ownerUser.adminId) {
      const admin = await User.findById(ownerUser.adminId);
      if (admin) {
        console.log(`- Admin: ${admin.name} (Rate: ₹${admin.orderRate || 0})`);
        console.log('✅ Billing should now work when orders are placed!');
      }
    } else {
      console.log('⚠️  Owner has no admin - billing may not work');
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

fixBrokerOwnership();
