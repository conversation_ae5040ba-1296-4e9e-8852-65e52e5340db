const mongoose = require('mongoose');
require('dotenv').config();

async function fixCompleteArchitecture() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.TESTLIST || 'mongodb://localhost:27017/trading-platform';
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');

    // Define models
    const User = mongoose.model('User', new mongoose.Schema({}, { strict: false }));
    const AngelUser = mongoose.model('AngelUser', new mongoose.Schema({}, { strict: false }));
    const MotilalUser = mongoose.model('MotilalUser', new mongoose.Schema({}, { strict: false }));
    const BillingRecord = mongoose.model('BillingRecord', new mongoose.Schema({}, { strict: false }));
    
    console.log('\n🏗️ FIXING COMPLETE SYSTEM ARCHITECTURE');
    console.log('=' .repeat(60));
    
    // 1. Ensure we have the correct role structure
    console.log('\n1️⃣ SETTING UP CORRECT ROLE STRUCTURE:');
    
    // Find or create super admin
    let superAdmin = await User.findOne({ role: 'super_admin' });
    if (!superAdmin) {
      console.log('❌ No super admin found - creating one...');
      superAdmin = new User({
        email: '<EMAIL>',
        password: '$2b$12$hashedpassword', // You'll need to set this properly
        name: 'Super Admin',
        role: 'super_admin',
        userCode: 'SUPER001',
        isActive: true,
        orderRate: 0, // Super admin doesn't place orders
        totalOrders: 0,
        totalBilling: 0
      });
      await superAdmin.save();
      console.log('✅ Created super admin');
    } else {
      console.log(`✅ Super admin exists: ${superAdmin.name} (${superAdmin.email})`);
    }
    
    // Find or create admin
    let admin = await User.findOne({ role: 'admin' });
    if (!admin) {
      console.log('❌ No admin found - creating one...');
      admin = new User({
        email: '<EMAIL>',
        password: '$2b$12$hashedpassword', // You'll need to set this properly
        name: 'Trading Admin',
        role: 'admin',
        userCode: 'ADM001',
        superAdminId: superAdmin._id,
        isActive: true,
        orderRate: 5, // ₹5 per order
        totalOrders: 0,
        totalBilling: 0
      });
      await admin.save();
      console.log('✅ Created admin');
    } else {
      // Update admin to have super admin reference
      if (!admin.superAdminId) {
        await User.findByIdAndUpdate(admin._id, { 
          $set: { 
            superAdminId: superAdmin._id,
            orderRate: admin.orderRate || 5
          } 
        });
        console.log('✅ Updated admin with super admin reference');
      }
      console.log(`✅ Admin exists: ${admin.name} (${admin.email})`);
    }
    
    // 2. Ensure users are properly linked to admin and have user codes
    console.log('\n2️⃣ FIXING USER STRUCTURE:');
    const users = await User.find({ role: 'user' });
    
    for (const user of users) {
      let needsUpdate = false;
      const updateData = {};
      
      // Ensure user has admin ID
      if (!user.adminId) {
        updateData.adminId = admin._id;
        needsUpdate = true;
        console.log(`   ✅ Linking user ${user.name} to admin`);
      }
      
      // Ensure user has user code
      if (!user.userCode) {
        updateData.userCode = `USR${Date.now().toString().slice(-5)}${Math.random().toString(36).substr(2, 2).toUpperCase()}`;
        needsUpdate = true;
        console.log(`   ✅ Setting user code ${updateData.userCode} for ${user.name}`);
      }
      
      // Set default OTP for user login
      if (!user.userOtp) {
        updateData.userOtp = '123456';
        needsUpdate = true;
        console.log(`   ✅ Setting default OTP for ${user.name}`);
      }
      
      if (needsUpdate) {
        await User.findByIdAndUpdate(user._id, { $set: updateData });
      }
    }
    
    // Create a test user if none exist
    if (users.length === 0) {
      console.log('❌ No users found - creating test user...');
      const testUser = new User({
        email: '<EMAIL>',
        password: '$2b$12$hashedpassword',
        name: 'Test User',
        role: 'user',
        userCode: 'USR001',
        adminId: admin._id,
        userOtp: '123456',
        isActive: true
      });
      await testUser.save();
      console.log('✅ Created test user');
    }
    
    // 3. FIX BROKER ACCOUNT OWNERSHIP - USERS OWN BROKER ACCOUNTS
    console.log('\n3️⃣ FIXING BROKER ACCOUNT OWNERSHIP:');
    console.log('🎯 RULE: Users own broker accounts, Admins place orders on behalf of users');
    
    // Get a user to assign broker accounts to
    const targetUser = await User.findOne({ role: 'user', adminId: admin._id });
    
    if (targetUser) {
      console.log(`👤 Target user for broker accounts: ${targetUser.userCode} - ${targetUser.name}`);
      
      // Fix Angel broker accounts
      const angelUsers = await AngelUser.find({});
      console.log(`\n📱 Fixing ${angelUsers.length} Angel broker accounts:`);
      
      for (const angelUser of angelUsers) {
        const currentOwner = await User.findById(angelUser.owner);
        console.log(`   Angel Account: ${angelUser.clientName || angelUser.userId}`);
        console.log(`   Current Owner: ${currentOwner ? currentOwner.name + ' (' + currentOwner.role + ')' : 'None'}`);
        
        // Ensure owner is a USER (not admin)
        if (!currentOwner || currentOwner.role !== 'user') {
          await AngelUser.findByIdAndUpdate(angelUser._id, { 
            $set: { owner: targetUser._id } 
          });
          console.log(`   ✅ Fixed: Now owned by USER ${targetUser.userCode} - ${targetUser.name}`);
        } else {
          console.log(`   ✅ Already correct: Owned by USER ${currentOwner.userCode} - ${currentOwner.name}`);
        }
      }
      
      // Fix Motilal broker accounts
      const motilalUsers = await MotilalUser.find({});
      console.log(`\n📱 Fixing ${motilalUsers.length} Motilal broker accounts:`);
      
      for (const motilalUser of motilalUsers) {
        const currentOwner = await User.findById(motilalUser.owner);
        console.log(`   Motilal Account: ${motilalUser.clientName || motilalUser.userId}`);
        console.log(`   Current Owner: ${currentOwner ? currentOwner.name + ' (' + currentOwner.role + ')' : 'None'}`);
        
        // Ensure owner is a USER (not admin)
        if (!currentOwner || currentOwner.role !== 'user') {
          await MotilalUser.findByIdAndUpdate(motilalUser._id, { 
            $set: { owner: targetUser._id } 
          });
          console.log(`   ✅ Fixed: Now owned by USER ${targetUser.userCode} - ${targetUser.name}`);
        } else {
          console.log(`   ✅ Already correct: Owned by USER ${currentOwner.userCode} - ${currentOwner.name}`);
        }
      }
    } else {
      console.log('❌ No users found to assign broker accounts to');
    }
    
    // 4. Create test billing data to show the flow
    console.log('\n4️⃣ CREATING TEST BILLING DATA:');
    
    if (targetUser && admin) {
      // Clear existing test data
      await BillingRecord.deleteMany({ orderId: { $regex: /^TEST_ORDER_/ } });
      
      const testBillingRecords = [
        {
          userId: targetUser._id, // Order attributed to USER
          adminId: admin._id, // Billing goes to ADMIN
          superAdminId: superAdmin._id, // Super admin gets the money
          orderId: `TEST_ORDER_${Date.now()}_1`,
          orderType: 'BUY',
          symbol: 'RELIANCE',
          quantity: 10,
          price: 2500,
          broker: 'angel',
          clientId: 'test-client-1',
          orderRate: admin.orderRate || 5,
          amount: admin.orderRate || 5,
          status: 'completed',
          billingCycle: '2025-01',
          description: 'Admin placed BUY order for user using user\'s Angel account',
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          userId: targetUser._id, // Order attributed to USER
          adminId: admin._id, // Billing goes to ADMIN
          superAdminId: superAdmin._id, // Super admin gets the money
          orderId: `TEST_ORDER_${Date.now()}_2`,
          orderType: 'SELL',
          symbol: 'TCS',
          quantity: 5,
          price: 3200,
          broker: 'motilal',
          clientId: 'test-client-2',
          orderRate: admin.orderRate || 5,
          amount: admin.orderRate || 5,
          status: 'completed',
          billingCycle: '2025-01',
          description: 'Admin placed SELL order for user using user\'s Motilal account',
          createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
          updatedAt: new Date(Date.now() - 24 * 60 * 60 * 1000)
        }
      ];
      
      const insertedRecords = await BillingRecord.insertMany(testBillingRecords);
      console.log(`✅ Created ${insertedRecords.length} test billing records`);
      
      // Update admin's billing totals
      const totalBilling = testBillingRecords.reduce((sum, record) => sum + record.amount, 0);
      await User.findByIdAndUpdate(admin._id, { 
        $inc: { 
          totalBilling: totalBilling,
          totalOrders: testBillingRecords.length
        }
      });
      console.log(`✅ Updated admin billing: +₹${totalBilling}, +${testBillingRecords.length} orders`);
    }
    
    // 5. Final verification and summary
    console.log('\n5️⃣ FINAL VERIFICATION:');
    
    const allUsers = await User.find({}).select('userCode name email role adminId superAdminId orderRate totalBilling userOtp');
    const angelCount = await AngelUser.countDocuments({});
    const motilalCount = await MotilalUser.countDocuments({});
    const billingCount = await BillingRecord.countDocuments({});
    
    console.log('\n📊 SYSTEM SUMMARY:');
    console.log(`👑 Super Admin: 1`);
    console.log(`👨‍💼 Admins: ${allUsers.filter(u => u.role === 'admin').length}`);
    console.log(`👤 Users: ${allUsers.filter(u => u.role === 'user').length}`);
    console.log(`📱 Angel Accounts: ${angelCount}`);
    console.log(`📱 Motilal Accounts: ${motilalCount}`);
    console.log(`💰 Billing Records: ${billingCount}`);
    
    console.log('\n👥 ALL USERS:');
    allUsers.forEach((user, index) => {
      console.log(`${index + 1}. ${user.userCode} - ${user.name} (${user.role})`);
      if (user.role === 'user') {
        console.log(`   Admin: ${user.adminId ? 'Linked' : 'Missing'}`);
        console.log(`   OTP: ${user.userOtp || 'Not Set'}`);
      }
      if (user.role === 'admin') {
        console.log(`   Super Admin: ${user.superAdminId ? 'Linked' : 'Missing'}`);
        console.log(`   Order Rate: ₹${user.orderRate || 0}`);
        console.log(`   Total Billing: ₹${user.totalBilling || 0}`);
      }
    });
    
    console.log('\n🔑 LOGIN CREDENTIALS:');
    if (targetUser) {
      console.log(`👤 User Login (view orders placed by admin):`);
      console.log(`   URL: http://localhost:3002/user-login`);
      console.log(`   User ID: ${targetUser.userCode}`);
      console.log(`   OTP: ${targetUser.userOtp}`);
    }
    
    console.log(`\n👨‍💼 Admin Login (place orders on behalf of users):`);
    console.log(`   URL: http://localhost:3002/admin`);
    console.log(`   Email: ${admin.email}`);
    console.log(`   Password: [Use existing password]`);
    
    console.log(`\n👑 Super Admin Login (manage everything):`);
    console.log(`   URL: http://localhost:3002/super-admin`);
    console.log(`   Email: ${superAdmin.email}`);
    console.log(`   Password: [Use existing password]`);
    
    console.log('\n✅ COMPLETE ARCHITECTURE FIXED!');
    console.log('\n🎯 REMEMBER:');
    console.log('   • Users OWN broker accounts');
    console.log('   • Admins PLACE ORDERS on behalf of users');
    console.log('   • Billing goes to ADMIN');
    console.log('   • Order attribution goes to USER');
    console.log('   • Super Admin manages everything');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

fixCompleteArchitecture();
