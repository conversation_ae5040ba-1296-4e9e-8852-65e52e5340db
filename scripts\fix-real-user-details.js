const mongoose = require('mongoose');
require('dotenv').config();

async function fixRealUserDetails() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.TESTLIST || 'mongodb://localhost:27017/trading-platform';
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');

    // Define models
    const User = mongoose.model('User', new mongoose.Schema({}, { strict: false }));
    const AngelUser = mongoose.model('AngelUser', new mongoose.Schema({}, { strict: false }));
    const MotilalUser = mongoose.model('MotilalUser', new mongoose.Schema({}, { strict: false }));
    
    console.log('\n🔧 FIXING USER DETAILS TO MATCH REAL BROKER ACCOUNTS');
    console.log('=' .repeat(60));
    
    // 1. Get actual broker accounts
    const angelAccounts = await AngelUser.find({});
    const motilalAccounts = await MotilalUser.find({});
    const admin = await User.findOne({ role: 'admin', email: '<EMAIL>' });
    
    console.log(`📱 Found ${angelAccounts.length} Angel accounts`);
    console.log(`📱 Found ${motilalAccounts.length} Motilal accounts`);
    console.log(`👨‍💼 Admin: ${admin ? admin.name : 'Not found'}`);
    
    // 2. Process Angel accounts
    console.log('\n📱 PROCESSING ANGEL ACCOUNTS:');
    for (const angelAccount of angelAccounts) {
      console.log(`\nAngel Account: ${angelAccount.clientName} (${angelAccount.userId})`);
      console.log(`Email: ${angelAccount.email}`);
      console.log(`Owner ID: ${angelAccount.owner}`);
      
      // Find or create user for this Angel account
      let user = await User.findById(angelAccount.owner);
      
      if (!user) {
        // Create new user with real details
        const userCode = `ANG${angelAccount.userId.slice(-3)}`;
        user = new User({
          email: angelAccount.email,
          password: '$2b$12$LQv3c1yqBWVHxkd/X0lhmu.RCCyAVVNpYjo/X0lhmu.RCCyAVVNpYjo',
          name: angelAccount.clientName,
          role: 'user',
          userCode: userCode,
          adminId: admin._id,
          userOtp: '123456',
          isActive: true,
          companyName: `${angelAccount.clientName} - Angel Trading`,
          phoneNumber: angelAccount.phoneNumber,
          createdAt: new Date(),
          updatedAt: new Date()
        });
        await user.save();
        console.log(`✅ Created new user: ${userCode} - ${user.name}`);
      } else {
        // Update existing user with real details
        const userCode = `ANG${angelAccount.userId.slice(-3)}`;
        await User.findByIdAndUpdate(user._id, {
          $set: {
            name: angelAccount.clientName,
            email: angelAccount.email,
            userCode: userCode,
            adminId: admin._id,
            companyName: `${angelAccount.clientName} - Angel Trading`,
            phoneNumber: angelAccount.phoneNumber,
            userOtp: '123456'
          }
        });
        console.log(`✅ Updated existing user: ${userCode} - ${angelAccount.clientName}`);
      }
    }
    
    // 3. Process Motilal accounts
    console.log('\n📱 PROCESSING MOTILAL ACCOUNTS:');
    for (const motilalAccount of motilalAccounts) {
      console.log(`\nMotilal Account: ${motilalAccount.clientName} (${motilalAccount.userId})`);
      console.log(`Email: ${motilalAccount.email}`);
      console.log(`Owner ID: ${motilalAccount.owner}`);
      
      // Find or create user for this Motilal account
      let user = await User.findById(motilalAccount.owner);
      
      if (!user) {
        // Create new user with real details
        const userCode = `MOT${motilalAccount.userId.slice(-3)}`;
        user = new User({
          email: motilalAccount.email,
          password: '$2b$12$LQv3c1yqBWVHxkd/X0lhmu.RCCyAVVNpYjo/X0lhmu.RCCyAVVNpYjo',
          name: motilalAccount.clientName,
          role: 'user',
          userCode: userCode,
          adminId: admin._id,
          userOtp: '123456',
          isActive: true,
          companyName: `${motilalAccount.clientName} - Motilal Trading`,
          phoneNumber: motilalAccount.phoneNumber,
          createdAt: new Date(),
          updatedAt: new Date()
        });
        await user.save();
        console.log(`✅ Created new user: ${userCode} - ${user.name}`);
      } else {
        // Update existing user with real details
        const userCode = `MOT${motilalAccount.userId.slice(-3)}`;
        await User.findByIdAndUpdate(user._id, {
          $set: {
            name: motilalAccount.clientName,
            email: motilalAccount.email,
            userCode: userCode,
            adminId: admin._id,
            companyName: `${motilalAccount.clientName} - Motilal Trading`,
            phoneNumber: motilalAccount.phoneNumber,
            userOtp: '123456'
          }
        });
        console.log(`✅ Updated existing user: ${userCode} - ${motilalAccount.clientName}`);
      }
    }
    
    // 4. Clean up any orphaned users (users without broker accounts)
    console.log('\n🧹 CLEANING UP ORPHANED USERS:');
    const allUsers = await User.find({ role: 'user' });
    
    for (const user of allUsers) {
      const hasAngel = await AngelUser.findOne({ owner: user._id });
      const hasMotilal = await MotilalUser.findOne({ owner: user._id });
      
      if (!hasAngel && !hasMotilal) {
        console.log(`🗑️ Removing orphaned user: ${user.userCode} - ${user.name}`);
        await User.findByIdAndDelete(user._id);
      }
    }
    
    // 5. Final verification
    console.log('\n5️⃣ FINAL VERIFICATION:');
    
    const finalUsers = await User.find({ role: 'user' });
    console.log(`\n👤 FINAL USERS (${finalUsers.length}):`);
    
    for (const user of finalUsers) {
      console.log(`${user.userCode} - ${user.name}`);
      console.log(`   Email: ${user.email}`);
      console.log(`   Company: ${user.companyName}`);
      console.log(`   Phone: ${user.phoneNumber || 'Not set'}`);
      console.log(`   OTP: ${user.userOtp}`);
      
      // Check broker accounts
      const userAngel = await AngelUser.find({ owner: user._id });
      const userMotilal = await MotilalUser.find({ owner: user._id });
      
      if (userAngel.length > 0) {
        userAngel.forEach(acc => console.log(`   📱 Angel: ${acc.clientName} (${acc.userId})`));
      }
      if (userMotilal.length > 0) {
        userMotilal.forEach(acc => console.log(`   📱 Motilal: ${acc.clientName} (${acc.userId})`));
      }
    }
    
    console.log('\n🔑 UPDATED LOGIN CREDENTIALS:');
    finalUsers.forEach((user, index) => {
      console.log(`👤 User ${index + 1}:`);
      console.log(`   URL: http://localhost:3002/user-login`);
      console.log(`   User ID: ${user.userCode}`);
      console.log(`   OTP: ${user.userOtp}`);
      console.log(`   Real Name: ${user.name}`);
    });
    
    console.log('\n✅ USER DETAILS FIXED TO MATCH REAL BROKER ACCOUNTS!');
    console.log('\n🎯 NOW THE ADMIN DASHBOARD WILL SHOW:');
    console.log('   • Real client names from broker accounts');
    console.log('   • Actual email addresses');
    console.log('   • Proper user codes (ANG### for Angel, MOT### for Motilal)');
    console.log('   • Correct phone numbers');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

fixRealUserDetails();
