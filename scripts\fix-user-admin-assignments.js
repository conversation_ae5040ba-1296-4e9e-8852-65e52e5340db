const mongoose = require('mongoose');
require('dotenv').config();

async function fixUserAdminAssignments() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.TESTLIST || 'mongodb://localhost:27017/trading-platform';
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');

    // Define User model
    const User = mongoose.model('User', new mongoose.Schema({}, { strict: false }));
    
    console.log('\n🔍 CURRENT ADMIN-USER ASSIGNMENTS:');
    console.log('==================================================');

    // Get all admins
    const admins = await User.find({ role: 'admin' });
    console.log(`\n👥 Found ${admins.length} admins:`);
    admins.forEach((admin, index) => {
      console.log(`${index + 1}. ${admin.name} (${admin.email}) - ID: ${admin._id}`);
    });

    // Get all users
    const users = await User.find({ role: 'user' });
    console.log(`\n👤 Found ${users.length} users:`);
    users.forEach((user, index) => {
      console.log(`${index + 1}. ${user.name} (${user.email})`);
      console.log(`   Current Admin ID: ${user.adminId}`);
      
      // Find which admin this user is assigned to
      const assignedAdmin = admins.find(admin => admin._id.toString() === user.adminId?.toString());
      if (assignedAdmin) {
        console.log(`   Currently assigned to: ${assignedAdmin.name}`);
      } else {
        console.log(`   ❌ Invalid admin assignment!`);
      }
      console.log('');
    });

    console.log('\n🔧 FIXING ASSIGNMENTS BASED ON EMAIL PATTERNS:');
    console.log('==================================================');

    // Find specific admins
    const avisekh = admins.find(admin => admin.email.includes('avisekh'));
    const rahul = admins.find(admin => admin.email.includes('support'));

    if (!avisekh || !rahul) {
      console.log('❌ Could not find both Avisekh and Rahul admins');
      return;
    }

    console.log(`\n👨‍💼 Avisekh Admin: ${avisekh.name} (${avisekh._id})`);
    console.log(`👨‍💼 Rahul Admin: ${rahul.name} (${rahul._id})`);

    // Fix User 1 - should belong to Avisekh
    const user1 = users.find(user => user.email.includes('<EMAIL>'));
    if (user1) {
      console.log(`\n🔧 Fixing User 1 assignment...`);
      console.log(`   User: ${user1.name} (${user1.email})`);
      console.log(`   Current Admin ID: ${user1.adminId}`);
      console.log(`   Should be assigned to: ${avisekh.name} (${avisekh._id})`);
      
      if (user1.adminId?.toString() !== avisekh._id.toString()) {
        await User.findByIdAndUpdate(user1._id, { adminId: avisekh._id });
        console.log(`   ✅ Updated User 1 to be assigned to Avisekh`);
      } else {
        console.log(`   ✅ User 1 is already correctly assigned to Avisekh`);
      }
    }

    // Fix User 2 - should belong to Rahul
    const user2 = users.find(user => user.email.includes('<EMAIL>'));
    if (user2) {
      console.log(`\n🔧 Fixing User 2 assignment...`);
      console.log(`   User: ${user2.name} (${user2.email})`);
      console.log(`   Current Admin ID: ${user2.adminId}`);
      console.log(`   Should be assigned to: ${rahul.name} (${rahul._id})`);
      
      if (user2.adminId?.toString() !== rahul._id.toString()) {
        await User.findByIdAndUpdate(user2._id, { adminId: rahul._id });
        console.log(`   ✅ Updated User 2 to be assigned to Rahul`);
      } else {
        console.log(`   ✅ User 2 is already correctly assigned to Rahul`);
      }
    }

    // Fix SAHIL BALANI - should belong to one of the admins (let's assign to Avisekh for now)
    const sahil = users.find(user => user.email.includes('<EMAIL>'));
    if (sahil) {
      console.log(`\n🔧 Fixing SAHIL BALANI assignment...`);
      console.log(`   User: ${sahil.name} (${sahil.email})`);
      console.log(`   Current Admin ID: ${sahil.adminId}`);
      console.log(`   Assigning to: ${avisekh.name} (${avisekh._id})`);
      
      if (sahil.adminId?.toString() !== avisekh._id.toString()) {
        await User.findByIdAndUpdate(sahil._id, { adminId: avisekh._id });
        console.log(`   ✅ Updated SAHIL BALANI to be assigned to Avisekh`);
      } else {
        console.log(`   ✅ SAHIL BALANI is already correctly assigned to Avisekh`);
      }
    }

    console.log('\n📊 VERIFICATION - FINAL ASSIGNMENTS:');
    console.log('==================================================');

    // Verify the changes
    const updatedUsers = await User.find({ role: 'user' });
    updatedUsers.forEach((user, index) => {
      console.log(`${index + 1}. ${user.name} (${user.email})`);
      
      const assignedAdmin = admins.find(admin => admin._id.toString() === user.adminId?.toString());
      if (assignedAdmin) {
        console.log(`   ✅ Assigned to: ${assignedAdmin.name} (${assignedAdmin.email})`);
      } else {
        console.log(`   ❌ Invalid admin assignment: ${user.adminId}`);
      }
      console.log('');
    });

    // Show admin user counts
    console.log('\n👥 ADMIN USER COUNTS:');
    for (const admin of admins) {
      const adminUsers = updatedUsers.filter(user => user.adminId?.toString() === admin._id.toString());
      console.log(`${admin.name}: ${adminUsers.length} users`);
      adminUsers.forEach(user => {
        console.log(`  - ${user.name} (${user.email})`);
      });
    }

    console.log('\n✅ User-Admin assignment fixing completed!');
    console.log('\n🔒 SECURITY IMPACT:');
    console.log('- Each admin now only manages their own users');
    console.log('- Cross-admin visibility should be eliminated');
    console.log('- Broker accounts will be properly isolated by admin');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

fixUserAdminAssignments();
