const mongoose = require('mongoose');
require('dotenv').config();

async function generateUserCodes() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.TESTLIST || 'mongodb://localhost:27017/trading-platform';
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');

    // Define User model
    const User = mongoose.model('User', new mongoose.Schema({}, { strict: false }));
    
    console.log('\n🔧 GENERATING UNIQUE USER CODES');
    console.log('=' .repeat(50));
    
    // Get all users without user codes
    const usersWithoutCodes = await User.find({
      $or: [
        { userCode: { $exists: false } },
        { userCode: null },
        { userCode: '' }
      ]
    });

    console.log(`Found ${usersWithoutCodes.length} users without user codes`);

    // Generate unique codes for each user
    const usedCodes = new Set();
    
    // Get existing codes to avoid duplicates
    const existingCodes = await User.find({ 
      userCode: { $exists: true, $ne: null, $ne: '' } 
    }).select('userCode');
    
    existingCodes.forEach(user => {
      if (user.userCode) {
        usedCodes.add(user.userCode.toUpperCase());
      }
    });

    console.log(`Found ${usedCodes.size} existing user codes`);

    // Function to generate user code
    function generateUserCode(name, email, role) {
      // Method 1: Use initials + random numbers
      const initials = name.split(' ')
        .map(word => word.charAt(0).toUpperCase())
        .join('')
        .substring(0, 3);
      
      // Method 2: Use role prefix
      const rolePrefix = role === 'super_admin' ? 'SA' : 
                        role === 'admin' ? 'AD' : 'US';
      
      // Method 3: Use email prefix
      const emailPrefix = email.split('@')[0].substring(0, 3).toUpperCase();
      
      // Try different combinations
      const attempts = [
        `${rolePrefix}${initials}${Math.floor(Math.random() * 100).toString().padStart(2, '0')}`,
        `${initials}${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
        `${emailPrefix}${Math.floor(Math.random() * 100).toString().padStart(2, '0')}`,
        `${rolePrefix}${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`,
        `USR${Math.floor(Math.random() * 100000).toString().padStart(5, '0')}`
      ];
      
      for (const code of attempts) {
        if (!usedCodes.has(code)) {
          usedCodes.add(code);
          return code;
        }
      }
      
      // Fallback: timestamp-based
      const timestamp = Date.now().toString().slice(-6);
      const fallbackCode = `USR${timestamp}`;
      usedCodes.add(fallbackCode);
      return fallbackCode;
    }

    // Update users with generated codes
    for (let i = 0; i < usersWithoutCodes.length; i++) {
      const user = usersWithoutCodes[i];
      const userCode = generateUserCode(user.name, user.email, user.role);
      
      await User.findByIdAndUpdate(user._id, {
        $set: { userCode: userCode }
      });
      
      console.log(`${i + 1}. ${user.name} (${user.email}) → ${userCode}`);
    }

    // Verify all users now have codes
    console.log('\n✅ VERIFICATION:');
    const allUsers = await User.find({}).select('name email role userCode');
    
    console.log('\n📋 ALL USERS WITH CODES:');
    allUsers.forEach((user, index) => {
      console.log(`${index + 1}. ${user.userCode} - ${user.name} (${user.email}) [${user.role}]`);
    });

    // Check for duplicates
    const codes = allUsers.map(u => u.userCode).filter(Boolean);
    const uniqueCodes = new Set(codes);
    
    if (codes.length !== uniqueCodes.size) {
      console.log('❌ DUPLICATE CODES FOUND!');
      const duplicates = codes.filter((code, index) => codes.indexOf(code) !== index);
      console.log('Duplicates:', duplicates);
    } else {
      console.log('✅ All user codes are unique!');
    }

    console.log('\n📊 SUMMARY:');
    console.log(`- Total Users: ${allUsers.length}`);
    console.log(`- Users with Codes: ${codes.length}`);
    console.log(`- Unique Codes: ${uniqueCodes.size}`);
    console.log(`- Super Admins: ${allUsers.filter(u => u.role === 'super_admin').length}`);
    console.log(`- Admins: ${allUsers.filter(u => u.role === 'admin').length}`);
    console.log(`- Users: ${allUsers.filter(u => u.role === 'user').length}`);

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

generateUserCodes();
