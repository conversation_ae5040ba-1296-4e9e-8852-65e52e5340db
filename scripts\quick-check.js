const mongoose = require('mongoose');
require('dotenv').config();

async function quickCheck() {
  try {
    const mongoUri = process.env.TESTLIST || 'mongodb://localhost:27017/trading-platform';
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');

    const User = mongoose.model('User', new mongoose.Schema({}, { strict: false }));
    const AngelUser = mongoose.model('AngelUser', new mongoose.Schema({}, { strict: false }));
    const MotilalUser = mongoose.model('MotilalUser', new mongoose.Schema({}, { strict: false }));
    
    console.log('\n📊 QUICK DATABASE CHECK');
    console.log('=' .repeat(40));
    
    const users = await User.find({ role: 'user' });
    const angelAccounts = await AngelUser.find({});
    const motilalAccounts = await MotilalUser.find({});
    
    console.log(`👤 Users: ${users.length}`);
    console.log(`📱 Angel: ${angelAccounts.length}`);
    console.log(`📱 Motilal: ${motilalAccounts.length}`);
    
    console.log('\n👤 USERS:');
    users.forEach(user => {
      console.log(`${user.userCode || 'NO_CODE'} - ${user.name} (${user.email})`);
    });
    
    console.log('\n📱 ANGEL ACCOUNTS:');
    angelAccounts.forEach(acc => {
      console.log(`${acc.clientName} (${acc.userId}) - Owner: ${acc.owner}`);
    });
    
    console.log('\n📱 MOTILAL ACCOUNTS:');
    motilalAccounts.forEach(acc => {
      console.log(`${acc.clientName} (${acc.userId}) - Owner: ${acc.owner}`);
    });

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
  }
}

quickCheck();
