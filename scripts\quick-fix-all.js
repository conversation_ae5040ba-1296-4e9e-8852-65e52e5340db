const mongoose = require('mongoose');
require('dotenv').config();

async function quickFixAll() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.TESTLIST || 'mongodb://localhost:27017/trading-platform';
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');

    // Define models
    const User = mongoose.model('User', new mongoose.Schema({}, { strict: false }));
    const AngelUser = mongoose.model('AngelUser', new mongoose.Schema({}, { strict: false }));
    const MotilalUser = mongoose.model('MotilalUser', new mongoose.Schema({}, { strict: false }));
    
    console.log('\n🔧 QUICK FIX ALL ISSUES');
    console.log('=' .repeat(50));
    
    // 1. Ensure all users have user codes
    console.log('\n1️⃣ CHECKING USER CODES:');
    const usersWithoutCodes = await User.find({
      $or: [
        { userCode: { $exists: false } },
        { userCode: null },
        { userCode: '' }
      ]
    });
    
    if (usersWithoutCodes.length > 0) {
      console.log(`❌ Found ${usersWithoutCodes.length} users without codes - fixing...`);
      // Generate codes for users without them
      for (const user of usersWithoutCodes) {
        const code = `USR${Date.now().toString().slice(-5)}`;
        await User.findByIdAndUpdate(user._id, { $set: { userCode: code } });
        console.log(`   ✅ Set code ${code} for ${user.name}`);
      }
    } else {
      console.log('✅ All users have codes');
    }

    // 2. Fix broker ownership
    console.log('\n2️⃣ FIXING BROKER OWNERSHIP:');
    const targetUser = await User.findOne({ role: 'user', adminId: { $exists: true } });
    
    if (targetUser) {
      console.log(`✅ Target user: ${targetUser.userCode} - ${targetUser.name}`);
      
      // Fix Angel users
      const angelUsers = await AngelUser.find({});
      for (const angelUser of angelUsers) {
        await AngelUser.findByIdAndUpdate(angelUser._id, { $set: { owner: targetUser._id } });
        console.log(`   ✅ Fixed Angel user: ${angelUser.clientName}`);
      }
      
      // Fix Motilal users
      const motilalUsers = await MotilalUser.find({});
      for (const motilalUser of motilalUsers) {
        await MotilalUser.findByIdAndUpdate(motilalUser._id, { $set: { owner: targetUser._id } });
        console.log(`   ✅ Fixed Motilal user: ${motilalUser.clientName || motilalUser.userId}`);
      }
    } else {
      console.log('❌ No target user found');
    }

    // 3. Set default OTP for user if not set
    console.log('\n3️⃣ SETTING DEFAULT OTP:');
    if (targetUser && !targetUser.userOtp) {
      const defaultOtp = '123456';
      await User.findByIdAndUpdate(targetUser._id, { $set: { userOtp: defaultOtp } });
      console.log(`✅ Set default OTP ${defaultOtp} for ${targetUser.userCode}`);
    } else if (targetUser) {
      console.log(`✅ User ${targetUser.userCode} already has OTP: ${targetUser.userOtp}`);
    }

    // 4. Verify admin setup
    console.log('\n4️⃣ VERIFYING ADMIN SETUP:');
    const admin = await User.findById(targetUser?.adminId);
    if (admin) {
      console.log(`✅ Admin: ${admin.userCode} - ${admin.name}`);
      console.log(`   Order Rate: ₹${admin.orderRate || 0}`);
      console.log(`   Super Admin ID: ${admin.superAdminId ? 'Set' : 'Missing'}`);
      
      if (!admin.orderRate || admin.orderRate === 0) {
        await User.findByIdAndUpdate(admin._id, { $set: { orderRate: 5 } });
        console.log(`   ✅ Set default order rate ₹5 for admin`);
      }
    }

    // 5. Final verification
    console.log('\n5️⃣ FINAL VERIFICATION:');
    const allUsers = await User.find({}).select('userCode name email role adminId orderRate userOtp');
    
    console.log('\n📋 ALL USERS:');
    allUsers.forEach((user, index) => {
      console.log(`${index + 1}. ${user.userCode} - ${user.name} (${user.role})`);
      if (user.role === 'user') {
        console.log(`   AdminId: ${user.adminId ? 'Set' : 'Missing'}`);
        console.log(`   OTP: ${user.userOtp || 'Not Set'}`);
      }
      if (user.role === 'admin') {
        console.log(`   Order Rate: ₹${user.orderRate || 0}`);
      }
    });

    // 6. Test login credentials
    console.log('\n6️⃣ LOGIN CREDENTIALS:');
    if (targetUser) {
      console.log(`🔑 User Login:`);
      console.log(`   URL: http://localhost:3002/user-login`);
      console.log(`   User ID: ${targetUser.userCode}`);
      console.log(`   OTP: ${targetUser.userOtp || 'Ask admin to set'}`);
    }

    const adminUser = await User.findOne({ role: 'admin' });
    if (adminUser) {
      console.log(`\n👤 Admin Login:`);
      console.log(`   URL: http://localhost:3002/admin`);
      console.log(`   Email: ${adminUser.email}`);
      console.log(`   Password: Use existing password`);
    }

    console.log('\n✅ ALL FIXES COMPLETED!');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

quickFixAll();
