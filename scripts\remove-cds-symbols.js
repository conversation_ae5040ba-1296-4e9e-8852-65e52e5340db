/**
 * <PERSON><PERSON><PERSON> to remove all CDS (Currency Derivative Segment) symbols from Angel_api database
 * This will clean up the database to only contain NSE equity symbols
 * 
 * Usage: node scripts/remove-cds-symbols.js
 */

const { MongoClient } = require('mongodb');
require('dotenv').config();

// MongoDB connection string
const MONGODB_URI = process.env.TESTLIST || process.env.MONGODB_URI || 'mongodb://localhost:27017';

async function removeCDSSymbols() {
  let client;
  
  try {
    console.log('🔌 Connecting to MongoDB...');
    client = new MongoClient(MONGODB_URI);
    await client.connect();
    console.log('✅ Connected to MongoDB successfully');

    const database = client.db("Angel_api");
    const collection = database.collection("totalscript");

    // First, let's see what we have
    console.log('\n📊 Current database statistics:');
    
    const totalCount = await collection.countDocuments();
    console.log(`Total documents: ${totalCount}`);

    const nseCount = await collection.countDocuments({ exch_seg: "NSE" });
    console.log(`NSE documents: ${nseCount}`);

    const cdsCount = await collection.countDocuments({ exch_seg: "CDS" });
    console.log(`CDS documents: ${cdsCount}`);

    const otherExchanges = await collection.distinct("exch_seg");
    console.log(`All exchanges: ${otherExchanges.join(', ')}`);

    // Show some CDS examples before deletion
    console.log('\n🔍 Sample CDS documents to be deleted:');
    const cdsSamples = await collection.find({ exch_seg: "CDS" }).limit(5).toArray();
    cdsSamples.forEach((doc, index) => {
      console.log(`  ${index + 1}. ${doc.symbol} (${doc.name}) - Token: ${doc.token}`);
    });

    // Ask for confirmation (in production, you might want to add readline for interactive confirmation)
    console.log(`\n⚠️  WARNING: About to delete ${cdsCount} CDS documents!`);
    console.log('This action cannot be undone.');
    
    // For safety, let's create a backup query first
    console.log('\n💾 Creating backup of CDS data...');
    const cdsBackup = await collection.find({ exch_seg: "CDS" }).toArray();
    
    // Save backup to file
    const fs = require('fs');
    const backupPath = `backup-cds-${new Date().toISOString().split('T')[0]}.json`;
    fs.writeFileSync(backupPath, JSON.stringify(cdsBackup, null, 2));
    console.log(`✅ Backup saved to: ${backupPath}`);

    // Now delete CDS documents
    console.log('\n🗑️  Deleting CDS documents...');
    const deleteResult = await collection.deleteMany({ exch_seg: "CDS" });
    
    console.log(`✅ Successfully deleted ${deleteResult.deletedCount} CDS documents`);

    // Verify the deletion
    console.log('\n📊 Updated database statistics:');
    const newTotalCount = await collection.countDocuments();
    console.log(`Total documents: ${newTotalCount}`);

    const newNseCount = await collection.countDocuments({ exch_seg: "NSE" });
    console.log(`NSE documents: ${newNseCount}`);

    const newCdsCount = await collection.countDocuments({ exch_seg: "CDS" });
    console.log(`CDS documents: ${newCdsCount}`);

    const newOtherExchanges = await collection.distinct("exch_seg");
    console.log(`Remaining exchanges: ${newOtherExchanges.join(', ')}`);

    // Show some NSE equity examples
    console.log('\n✅ Sample remaining NSE equity documents:');
    const nseEquitySamples = await collection.find({ 
      exch_seg: "NSE", 
      symbol: { $regex: "-EQ$" } 
    }).limit(5).toArray();
    
    nseEquitySamples.forEach((doc, index) => {
      console.log(`  ${index + 1}. ${doc.symbol} (${doc.name}) - Token: ${doc.token}`);
    });

    console.log('\n🎉 CDS cleanup completed successfully!');
    console.log('The database now contains only NSE symbols, eliminating the need for exchange filtering.');

  } catch (error) {
    console.error('❌ Error during CDS cleanup:', error);
    throw error;
  } finally {
    if (client) {
      await client.close();
      console.log('🔌 MongoDB connection closed');
    }
  }
}

// Additional function to remove other non-NSE exchanges if needed
async function removeAllNonNSESymbols() {
  let client;
  
  try {
    console.log('🔌 Connecting to MongoDB...');
    client = new MongoClient(MONGODB_URI);
    await client.connect();
    console.log('✅ Connected to MongoDB successfully');

    const database = client.db("Angel_api");
    const collection = database.collection("totalscript");

    // Get all non-NSE exchanges
    const allExchanges = await collection.distinct("exch_seg");
    const nonNSEExchanges = allExchanges.filter(exchange => exchange !== "NSE");
    
    console.log(`\n📊 Non-NSE exchanges found: ${nonNSEExchanges.join(', ')}`);

    for (const exchange of nonNSEExchanges) {
      const count = await collection.countDocuments({ exch_seg: exchange });
      console.log(`${exchange}: ${count} documents`);
    }

    // Create backup
    console.log('\n💾 Creating backup of all non-NSE data...');
    const nonNSEBackup = await collection.find({ exch_seg: { $ne: "NSE" } }).toArray();
    
    const fs = require('fs');
    const backupPath = `backup-non-nse-${new Date().toISOString().split('T')[0]}.json`;
    fs.writeFileSync(backupPath, JSON.stringify(nonNSEBackup, null, 2));
    console.log(`✅ Backup saved to: ${backupPath}`);

    // Delete all non-NSE documents
    console.log('\n🗑️  Deleting all non-NSE documents...');
    const deleteResult = await collection.deleteMany({ exch_seg: { $ne: "NSE" } });
    
    console.log(`✅ Successfully deleted ${deleteResult.deletedCount} non-NSE documents`);

    // Verify
    const finalCount = await collection.countDocuments();
    const finalNSECount = await collection.countDocuments({ exch_seg: "NSE" });
    
    console.log(`\n📊 Final statistics:`);
    console.log(`Total documents: ${finalCount}`);
    console.log(`NSE documents: ${finalNSECount}`);
    console.log(`Other exchanges: ${finalCount - finalNSECount}`);

  } catch (error) {
    console.error('❌ Error during non-NSE cleanup:', error);
    throw error;
  } finally {
    if (client) {
      await client.close();
      console.log('🔌 MongoDB connection closed');
    }
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--all-non-nse')) {
    console.log('🚀 Starting cleanup of ALL non-NSE symbols...');
    await removeAllNonNSESymbols();
  } else {
    console.log('🚀 Starting cleanup of CDS symbols...');
    await removeCDSSymbols();
  }
}

// Run the script
if (require.main === module) {
  main().catch(error => {
    console.error('💥 Script failed:', error);
    process.exit(1);
  });
}

module.exports = {
  removeCDSSymbols,
  removeAllNonNSESymbols
};
