const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

async function resetAdminPassword() {
  try {
    const mongoUri = process.env.TESTLIST || 'mongodb://localhost:27017/trading-platform';
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');

    // Define models
    const User = mongoose.model('User', new mongoose.Schema({}, { strict: false }));
    
    console.log('\n🔧 RESETTING ADMIN PASSWORD');
    console.log('=' .repeat(50));
    
    // Find the admin user
    const adminUser = await User.findOne({ email: '<EMAIL>' });
    if (!adminUser) {
      console.log('❌ Admin user not found');
      return;
    }
    
    console.log(`👤 Found admin user: ${adminUser.name} (${adminUser.email})`);
    console.log(`   Current password hash: ${adminUser.password.substring(0, 20)}...`);
    
    // Hash the new password
    const newPassword = 'admin123';
    const salt = await bcrypt.genSalt(12);
    const hashedPassword = await bcrypt.hash(newPassword, salt);
    
    console.log(`🔑 New password: ${newPassword}`);
    console.log(`🔒 New password hash: ${hashedPassword.substring(0, 20)}...`);
    
    // Update the user's password
    await User.findByIdAndUpdate(adminUser._id, {
      $set: { password: hashedPassword }
    });
    
    console.log('✅ Password updated successfully!');
    
    // Verify the password works
    const updatedUser = await User.findById(adminUser._id);
    const isPasswordValid = await bcrypt.compare(newPassword, updatedUser.password);
    
    console.log(`🧪 Password verification: ${isPasswordValid ? '✅ VALID' : '❌ INVALID'}`);
    
    if (isPasswordValid) {
      console.log('\n🎉 SUCCESS! Admin password reset complete.');
      console.log('📧 Login Credentials:');
      console.log(`   Email: ${adminUser.email}`);
      console.log(`   Password: ${newPassword}`);
      console.log('');
      console.log('🚀 You can now:');
      console.log('   1. Login at: http://localhost:3000/login');
      console.log('   2. Access Admin Dashboard: http://localhost:3000/admin');
      console.log('   3. Manage users and place orders');
    } else {
      console.log('❌ Password verification failed!');
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

resetAdminPassword();
