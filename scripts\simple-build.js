#!/usr/bin/env node

/**
 * Simple build script that doesn't exit on failure
 * Use this for debugging build issues
 */

const { spawn } = require('child_process');

console.log('🚀 Starting simple build process...');
console.log('📝 This script will NOT exit automatically on failure');
console.log('');

/**
 * Execute a command and return a promise
 */
function executeCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    console.log(`🔧 Executing: ${command} ${args.join(' ')}`);

    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      env: { 
        ...process.env, 
        NODE_OPTIONS: '--max-old-space-size=8192',
        NODE_ENV: 'production',
        ...options.env 
      },
      ...options
    });

    child.on('close', (code) => {
      if (code === 0) {
        console.log(`✅ Command completed successfully`);
        resolve();
      } else {
        console.error(`❌ Command failed with exit code ${code}`);
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });

    child.on('error', (error) => {
      console.error(`💥 Command error:`, error.message);
      reject(error);
    });
  });
}

/**
 * Main build function
 */
async function simpleBuild() {
  const startTime = Date.now();
  
  try {
    console.log('🧹 Cleaning previous build...');
    await executeCommand('node', ['scripts/clean.js']);
    console.log('');

    console.log('🏗️  Building Next.js frontend...');
    await executeCommand('npx', ['next', 'build']);
    console.log('');

    const duration = ((Date.now() - startTime) / 1000).toFixed(2);
    console.log(`🎉 Build completed successfully in ${duration}s!`);
    
  } catch (error) {
    const duration = ((Date.now() - startTime) / 1000).toFixed(2);
    console.error('');
    console.error('💥 Build failed:', error.message);
    console.error(`⏱️  Build failed after ${duration}s`);
    console.error('');
    console.error('🔧 Common solutions:');
    console.error('   1. Check TypeScript errors in the output above');
    console.error('   2. Run: npm run lint to check for linting issues');
    console.error('   3. Check your environment variables in .env files');
    console.error('   4. Try deleting node_modules and running: npm install');
    console.error('   5. Check if all imports are correct');
    console.error('');
    console.error('📝 Process will stay open for debugging...');
    console.error('   Press Ctrl+C to exit when ready');
  }
}

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
  console.log('');
  console.log('👋 Build process interrupted by user');
  console.log('   Goodbye!');
  process.exit(0);
});

// Run the build
if (require.main === module) {
  simpleBuild();
}

module.exports = { simpleBuild };
