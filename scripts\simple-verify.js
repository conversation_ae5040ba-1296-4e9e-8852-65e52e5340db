const mongoose = require('mongoose');
require('dotenv').config();

async function simpleVerify() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.TESTLIST || 'mongodb://localhost:27017/trading-platform';
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');

    // Define simple schemas for verification
    const User = mongoose.model('User', new mongoose.Schema({}, { strict: false }));
    const AngelUser = mongoose.model('AngelUser', new mongoose.Schema({}, { strict: false }));
    const MotilalUser = mongoose.model('MotilalUser', new mongoose.Schema({}, { strict: false }));
    const BillingRecord = mongoose.model('BillingRecord', new mongoose.Schema({}, { strict: false }));
    
    console.log('\n📊 CURRENT SYSTEM STATUS');
    console.log('=' .repeat(60));
    
    // 1. Get counts
    const superAdmins = await User.find({ role: 'super_admin' });
    const admins = await User.find({ role: 'admin' });
    const users = await User.find({ role: 'user' });
    const angelAccounts = await AngelUser.find({});
    const motilalAccounts = await MotilalUser.find({});
    const billingRecords = await BillingRecord.find({});
    
    console.log('\n📋 COUNTS:');
    console.log(`👑 Super Admins: ${superAdmins.length}`);
    console.log(`👨‍💼 Admins: ${admins.length}`);
    console.log(`👤 Users: ${users.length}`);
    console.log(`📱 Angel Accounts: ${angelAccounts.length}`);
    console.log(`📱 Motilal Accounts: ${motilalAccounts.length}`);
    console.log(`💰 Billing Records: ${billingRecords.length}`);
    
    // 2. Show users
    console.log('\n👤 USERS (BROKER ACCOUNT OWNERS):');
    for (const user of users) {
      console.log(`${user.userCode} - ${user.name} (${user.email})`);
      console.log(`   Admin: ${user.adminId ? 'Linked' : 'Missing'}`);
      console.log(`   OTP: ${user.userOtp || 'Not Set'}`);
      
      // Find broker accounts owned by this user
      const userAngel = await AngelUser.find({ owner: user._id });
      const userMotilal = await MotilalUser.find({ owner: user._id });
      
      console.log(`   Angel Accounts: ${userAngel.length}`);
      userAngel.forEach(acc => console.log(`     - ${acc.clientName} (${acc.userId})`));
      
      console.log(`   Motilal Accounts: ${userMotilal.length}`);
      userMotilal.forEach(acc => console.log(`     - ${acc.clientName} (${acc.userId})`));
    }
    
    // 3. Show admin isolation
    console.log('\n👨‍💼 ADMIN ISOLATION:');
    for (const admin of admins) {
      console.log(`\n${admin.userCode} - ${admin.name}:`);
      
      const adminUsers = await User.find({ adminId: admin._id, role: 'user' });
      console.log(`   Users: ${adminUsers.length}`);
      
      const userIds = adminUsers.map(u => u._id);
      const adminAngel = await AngelUser.find({ owner: { $in: userIds } });
      const adminMotilal = await MotilalUser.find({ owner: { $in: userIds } });
      const adminBilling = await BillingRecord.find({ adminId: admin._id });
      
      console.log(`   Angel Accounts: ${adminAngel.length}`);
      console.log(`   Motilal Accounts: ${adminMotilal.length}`);
      console.log(`   Billing Records: ${adminBilling.length}`);
      console.log(`   Order Rate: ₹${admin.orderRate || 0}`);
      console.log(`   Total Billing: ₹${admin.totalBilling || 0}`);
    }
    
    // 4. Check broker account ownership
    console.log('\n📱 BROKER ACCOUNT OWNERSHIP CHECK:');
    
    let allGood = true;
    
    for (const angelAccount of angelAccounts) {
      const owner = await User.findById(angelAccount.owner);
      console.log(`Angel ${angelAccount.clientName}:`);
      if (!owner) {
        console.log(`   ❌ NO OWNER`);
        allGood = false;
      } else if (owner.role !== 'user') {
        console.log(`   ❌ Owned by ${owner.role} (should be user)`);
        allGood = false;
      } else {
        console.log(`   ✅ Owned by user ${owner.userCode} - ${owner.name}`);
      }
    }
    
    for (const motilalAccount of motilalAccounts) {
      const owner = await User.findById(motilalAccount.owner);
      console.log(`Motilal ${motilalAccount.clientName}:`);
      if (!owner) {
        console.log(`   ❌ NO OWNER`);
        allGood = false;
      } else if (owner.role !== 'user') {
        console.log(`   ❌ Owned by ${owner.role} (should be user)`);
        allGood = false;
      } else {
        console.log(`   ✅ Owned by user ${owner.userCode} - ${owner.name}`);
      }
    }
    
    // 5. Final status
    console.log('\n🎯 SYSTEM STATUS:');
    if (allGood) {
      console.log('✅ ALL BROKER ACCOUNTS PROPERLY OWNED BY USERS');
    } else {
      console.log('❌ SOME BROKER ACCOUNTS HAVE OWNERSHIP ISSUES');
    }
    
    console.log('\n🔑 LOGIN CREDENTIALS:');
    
    if (superAdmins.length > 0) {
      console.log(`👑 Super Admin: ${superAdmins[0].email}`);
    }
    
    admins.forEach((admin, index) => {
      console.log(`👨‍💼 Admin ${index + 1}: ${admin.email}`);
    });
    
    users.forEach((user, index) => {
      console.log(`👤 User ${index + 1}: ${user.userCode} - OTP: ${user.userOtp || '123456'}`);
    });
    
    console.log('\n✅ VERIFICATION COMPLETE!');
    console.log('\n🎯 KEY POINTS:');
    console.log('   • Each broker account = one user');
    console.log('   • Users own broker accounts');
    console.log('   • Admins place orders on behalf of users');
    console.log('   • Admin isolation enforced');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

simpleVerify();
