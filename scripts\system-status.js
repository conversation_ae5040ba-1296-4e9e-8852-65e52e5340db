const mongoose = require('mongoose');
require('dotenv').config();

async function showSystemStatus() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.TESTLIST || 'mongodb://localhost:27017/trading-platform';
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');

    // Define models
    const User = mongoose.model('User', new mongoose.Schema({}, { strict: false }));
    const AngelUser = mongoose.model('AngelUser', new mongoose.Schema({}, { strict: false }));
    const MotilalUser = mongoose.model('MotilalUser', new mongoose.Schema({}, { strict: false }));
    const BillingRecord = mongoose.model('BillingRecord', new mongoose.Schema({}, { strict: false }));
    
    console.log('\n📊 CURRENT SYSTEM STATUS');
    console.log('=' .repeat(60));
    
    // 1. Get all users by role
    const superAdmins = await User.find({ role: 'super_admin' });
    const admins = await User.find({ role: 'admin' });
    const users = await User.find({ role: 'user' });
    
    console.log('\n👥 USER BREAKDOWN:');
    console.log(`👑 Super Admins: ${superAdmins.length}`);
    console.log(`👨‍💼 Admins: ${admins.length}`);
    console.log(`👤 Users (Broker Account Owners): ${users.length}`);
    
    // 2. Show super admin details
    if (superAdmins.length > 0) {
      console.log('\n👑 SUPER ADMIN:');
      superAdmins.forEach((sa, index) => {
        console.log(`${index + 1}. ${sa.userCode || 'NO_CODE'} - ${sa.name} (${sa.email})`);
      });
    }
    
    // 3. Show admin details with their users
    console.log('\n👨‍💼 ADMIN DETAILS:');
    for (const admin of admins) {
      console.log(`\n${admin.userCode || 'NO_CODE'} - ${admin.name} (${admin.email})`);
      console.log(`   Order Rate: ₹${admin.orderRate || 0}`);
      console.log(`   Total Billing: ₹${admin.totalBilling || 0}`);
      console.log(`   Super Admin: ${admin.superAdminId ? 'Linked' : 'Missing'}`);
      
      // Get users under this admin
      const adminUsers = await User.find({ adminId: admin._id, role: 'user' });
      console.log(`   Users: ${adminUsers.length}`);
      
      adminUsers.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.userCode || 'NO_CODE'} - ${user.name} (${user.email})`);
        console.log(`      OTP: ${user.userOtp || 'Not Set'}`);
      });
      
      // Get broker accounts for this admin's users
      const userIds = adminUsers.map(u => u._id);
      const angelAccounts = await AngelUser.find({ owner: { $in: userIds } });
      const motilalAccounts = await MotilalUser.find({ owner: { $in: userIds } });
      
      console.log(`   Angel Accounts: ${angelAccounts.length}`);
      console.log(`   Motilal Accounts: ${motilalAccounts.length}`);
      
      // Get billing records for this admin
      const billingRecords = await BillingRecord.find({ adminId: admin._id });
      console.log(`   Billing Records: ${billingRecords.length}`);
    }
    
    // 4. Show broker account ownership
    console.log('\n📱 BROKER ACCOUNT OWNERSHIP:');
    
    const allAngelAccounts = await AngelUser.find({});
    const allMotilalAccounts = await MotilalUser.find({});
    
    console.log(`\n📱 Angel Accounts (${allAngelAccounts.length}):`);
    for (const angelAccount of allAngelAccounts) {
      const owner = await User.findById(angelAccount.owner);
      console.log(`   ${angelAccount.clientName || angelAccount.userId}:`);
      console.log(`      Owner: ${owner ? owner.userCode + ' - ' + owner.name + ' (' + owner.role + ')' : 'NO OWNER'}`);
      console.log(`      Status: ${angelAccount.state || 'Unknown'}`);
    }
    
    console.log(`\n📱 Motilal Accounts (${allMotilalAccounts.length}):`);
    for (const motilalAccount of allMotilalAccounts) {
      const owner = await User.findById(motilalAccount.owner);
      console.log(`   ${motilalAccount.clientName || motilalAccount.userId}:`);
      console.log(`      Owner: ${owner ? owner.userCode + ' - ' + owner.name + ' (' + owner.role + ')' : 'NO OWNER'}`);
      console.log(`      Status: ${motilalAccount.status || 'Unknown'}`);
    }
    
    // 5. Show billing summary
    console.log('\n💰 BILLING SUMMARY:');
    const totalBillingRecords = await BillingRecord.countDocuments({});
    console.log(`Total Billing Records: ${totalBillingRecords}`);
    
    for (const admin of admins) {
      const adminBilling = await BillingRecord.find({ adminId: admin._id });
      const totalAmount = adminBilling.reduce((sum, record) => sum + (record.amount || 0), 0);
      console.log(`${admin.userCode}: ${adminBilling.length} orders, ₹${totalAmount} total`);
    }
    
    // 6. Verify admin isolation
    console.log('\n🔒 ADMIN ISOLATION CHECK:');
    for (const admin of admins) {
      const adminUsers = await User.find({ adminId: admin._id, role: 'user' });
      const userIds = adminUsers.map(u => u._id);
      
      // Check if admin can only see their users' broker accounts
      const adminAngelAccounts = await AngelUser.find({ owner: { $in: userIds } });
      const adminMotilalAccounts = await MotilalUser.find({ owner: { $in: userIds } });
      
      console.log(`${admin.userCode}:`);
      console.log(`   Can access: ${adminUsers.length} users, ${adminAngelAccounts.length} Angel, ${adminMotilalAccounts.length} Motilal`);
      console.log(`   ✅ Isolation: ${adminAngelAccounts.length + adminMotilalAccounts.length > 0 ? 'Working' : 'No accounts'}`);
    }
    
    // 7. Show login credentials
    console.log('\n🔑 LOGIN CREDENTIALS:');
    
    console.log('\n👑 Super Admin Login:');
    if (superAdmins.length > 0) {
      console.log(`   URL: http://localhost:3002/super-admin`);
      console.log(`   Email: ${superAdmins[0].email}`);
      console.log(`   Password: [Use existing password]`);
    }
    
    console.log('\n👨‍💼 Admin Login:');
    admins.forEach((admin, index) => {
      console.log(`   Admin ${index + 1}:`);
      console.log(`   URL: http://localhost:3002/admin`);
      console.log(`   Email: ${admin.email}`);
      console.log(`   Password: [Use existing password]`);
    });
    
    console.log('\n👤 User Login (View Orders):');
    users.forEach((user, index) => {
      console.log(`   User ${index + 1}:`);
      console.log(`   URL: http://localhost:3002/user-login`);
      console.log(`   User ID: ${user.userCode}`);
      console.log(`   OTP: ${user.userOtp || '123456'}`);
    });
    
    // 8. System health check
    console.log('\n🏥 SYSTEM HEALTH CHECK:');
    
    const issues = [];
    
    // Check for users without admin
    const orphanUsers = await User.find({ role: 'user', adminId: { $exists: false } });
    if (orphanUsers.length > 0) {
      issues.push(`${orphanUsers.length} users without admin`);
    }
    
    // Check for admins without super admin
    const orphanAdmins = await User.find({ role: 'admin', superAdminId: { $exists: false } });
    if (orphanAdmins.length > 0) {
      issues.push(`${orphanAdmins.length} admins without super admin`);
    }
    
    // Check for broker accounts without owners
    const orphanAngel = await AngelUser.find({ owner: { $exists: false } });
    const orphanMotilal = await MotilalUser.find({ owner: { $exists: false } });
    if (orphanAngel.length > 0) {
      issues.push(`${orphanAngel.length} Angel accounts without owners`);
    }
    if (orphanMotilal.length > 0) {
      issues.push(`${orphanMotilal.length} Motilal accounts without owners`);
    }
    
    // Check for users without codes
    const usersWithoutCodes = await User.find({ 
      $or: [
        { userCode: { $exists: false } },
        { userCode: null },
        { userCode: '' }
      ]
    });
    if (usersWithoutCodes.length > 0) {
      issues.push(`${usersWithoutCodes.length} users without user codes`);
    }
    
    if (issues.length === 0) {
      console.log('✅ All systems healthy!');
    } else {
      console.log('❌ Issues found:');
      issues.forEach(issue => console.log(`   - ${issue}`));
    }
    
    console.log('\n🎯 SYSTEM ARCHITECTURE SUMMARY:');
    console.log('   • Super Admin: Manages everything, collects payments');
    console.log('   • Admins: Place orders on behalf of their users, pay bills');
    console.log('   • Users: Own broker accounts, view order history');
    console.log('   • Broker Accounts: Owned by USERS only');
    console.log('   • Order Placement: Admins place orders using users\' accounts');
    console.log('   • Billing: Goes to admin, attribution to user');
    console.log('   • Isolation: Each admin sees only their users');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

showSystemStatus();
