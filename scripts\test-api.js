const https = require('http');

async function testAPI() {
  console.log('🧪 TESTING API ENDPOINTS');
  console.log('=' .repeat(40));
  
  // Test admin dashboard API
  const options = {
    hostname: 'localhost',
    port: 3002,
    path: '/api/admin/dashboard',
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Cookie': 'admin-token=test' // This might not work without proper auth
    }
  };

  const req = https.request(options, (res) => {
    console.log(`📡 Admin Dashboard API Status: ${res.statusCode}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      try {
        const result = JSON.parse(data);
        if (result.success) {
          console.log('✅ Admin Dashboard API working');
          console.log(`📊 Users: ${result.data.users?.length || 0}`);
          console.log(`💰 Recent Orders: ${result.data.recentOrders?.length || 0}`);
        } else {
          console.log('❌ Admin Dashboard API error:', result.error);
        }
      } catch (e) {
        console.log('❌ Failed to parse response:', data.substring(0, 200));
      }
    });
  });

  req.on('error', (e) => {
    console.error('❌ Request error:', e.message);
  });

  req.end();
  
  // Test user billing API
  setTimeout(() => {
    const billingOptions = {
      hostname: 'localhost',
      port: 3002,
      path: '/api/admin/user-billing',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': 'admin-token=test'
      }
    };

    const billingReq = https.request(billingOptions, (res) => {
      console.log(`📡 User Billing API Status: ${res.statusCode}`);
      
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          if (result.success) {
            console.log('✅ User Billing API working');
            console.log(`👥 User Billing Records: ${result.data?.length || 0}`);
          } else {
            console.log('❌ User Billing API error:', result.error);
          }
        } catch (e) {
          console.log('❌ Failed to parse billing response:', data.substring(0, 200));
        }
      });
    });

    billingReq.on('error', (e) => {
      console.error('❌ Billing request error:', e.message);
    });

    billingReq.end();
  }, 1000);
}

testAPI();
