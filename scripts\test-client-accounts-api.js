const mongoose = require('mongoose');
const jwt = require('jsonwebtoken');
require('dotenv').config();

async function testClientAccountsAPI() {
  try {
    const mongoUri = process.env.TESTLIST || 'mongodb://localhost:27017/trading-platform';
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');

    // Define models
    const User = mongoose.model('User', new mongoose.Schema({}, { strict: false }));
    const AngelUser = mongoose.model('AngelUser', new mongoose.Schema({}, { strict: false }));
    const MotilalUser = mongoose.model('MotilalUser', new mongoose.Schema({}, { strict: false }));
    
    console.log('\n🧪 TESTING CLIENT ACCOUNTS API');
    console.log('=' .repeat(50));
    
    // Get the test user
    const testUser = await User.findOne({ email: '<EMAIL>' });
    if (!testUser) {
      console.log('❌ Test user not found');
      return;
    }
    
    console.log(`👤 Found test user: ${testUser.name} (${testUser.email})`);
    console.log(`   User ID: ${testUser._id}`);
    console.log(`   Role: ${testUser.role}`);
    
    // Get accounts owned by test user
    const angelAccounts = await AngelUser.find({ owner: testUser._id });
    const motilalAccounts = await MotilalUser.find({ owner: testUser._id });
    
    console.log(`\n📱 Accounts owned by test user:`);
    console.log(`   Angel accounts: ${angelAccounts.length}`);
    console.log(`   Motilal accounts: ${motilalAccounts.length}`);
    
    angelAccounts.forEach((acc, i) => {
      console.log(`   ${i + 1}. Angel: ${acc.userId} - ${acc.clientName} (${acc.state})`);
    });
    
    motilalAccounts.forEach((acc, i) => {
      console.log(`   ${i + 1}. Motilal: ${acc.userId} - ${acc.clientName} (${acc.status})`);
    });
    
    // Create a JWT token for the test user
    const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
    const token = jwt.sign(
      {
        userId: testUser._id.toString(),
        email: testUser.email,
        role: testUser.role,
        name: testUser.name
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    );
    
    console.log(`\n🔑 Generated JWT token for test user`);
    console.log(`   Token: ${token.substring(0, 50)}...`);
    
    // Test the API endpoint
    console.log(`\n🔄 Testing /api/client-accounts endpoint...`);

    const { default: fetch } = await import('node-fetch');
    const response = await fetch('http://localhost:3000/api/client-accounts', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': `auth-token=${token}`
      }
    });
    
    console.log(`📡 API Response Status: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log(`✅ API Response:`, JSON.stringify(data, null, 2));
      
      if (data.success && data.accounts) {
        console.log(`\n✅ SUCCESS! Found ${data.accounts.length} client accounts:`);
        data.accounts.forEach((acc, i) => {
          console.log(`   ${i + 1}. ${acc.clientCode} - ${acc.clientName} (${acc.broker.toUpperCase()})`);
        });
      } else {
        console.log(`❌ API returned success=false or no accounts`);
      }
    } else {
      const errorText = await response.text();
      console.log(`❌ API Error: ${response.status} - ${errorText}`);
    }
    
    console.log('\n🎯 TESTING COMPLETE!');
    console.log('📋 Summary:');
    console.log(`   - Test user exists: ✅`);
    console.log(`   - User owns accounts: ✅ (${angelAccounts.length + motilalAccounts.length} total)`);
    console.log(`   - JWT token generated: ✅`);
    console.log(`   - API endpoint accessible: ${response.ok ? '✅' : '❌'}`);
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

testClientAccountsAPI();
