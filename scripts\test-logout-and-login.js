const mongoose = require('mongoose');
require('dotenv').config();

async function testLogoutAndLogin() {
  try {
    const mongoUri = process.env.TESTLIST || 'mongodb://localhost:27017/trading-platform';
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');

    // Define models
    const User = mongoose.model('User', new mongoose.Schema({}, { strict: false }));
    
    console.log('\n🔄 TESTING LOGOUT AND LOGIN FLOW');
    console.log('=' .repeat(50));
    
    // Test logout endpoint
    console.log('🔄 Testing logout endpoint...');
    const { default: fetch } = await import('node-fetch');
    
    const logoutResponse = await fetch('http://localhost:3000/api/auth/logout', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log(`📡 Logout Response Status: ${logoutResponse.status}`);
    
    if (logoutResponse.ok) {
      const logoutData = await logoutResponse.text();
      console.log(`✅ Logout successful:`, logoutData);
    } else {
      console.log(`❌ Logout failed`);
    }
    
    // Test login with admin credentials
    console.log('\n🔄 Testing login with admin credentials...');
    
    const loginData = {
      email: '<EMAIL>',
      password: 'admin123' // From fix-instructions.md
    };
    
    const loginResponse = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(loginData)
    });
    
    console.log(`📡 Login Response Status: ${loginResponse.status}`);
    
    if (loginResponse.ok) {
      const loginResult = await loginResponse.json();
      console.log(`✅ Login successful:`, loginResult);
      
      // Extract the auth token from response headers
      const setCookieHeader = loginResponse.headers.get('set-cookie');
      console.log('🍪 Set-Cookie header:', setCookieHeader);
      
      if (setCookieHeader) {
        const tokenMatch = setCookieHeader.match(/auth-token=([^;]+)/);
        if (tokenMatch) {
          const newToken = tokenMatch[1];
          console.log(`🔑 New auth token: ${newToken.substring(0, 50)}...`);
          
          // Test the Angel users API with the new token
          console.log('\n🔄 Testing /api/users/angel with new token...');
          
          const angelResponse = await fetch('http://localhost:3000/api/users/angel', {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Cookie': `auth-token=${newToken}`
            }
          });
          
          console.log(`📡 Angel API Response Status: ${angelResponse.status}`);
          
          if (angelResponse.ok) {
            const angelData = await angelResponse.json();
            console.log(`✅ Angel API Response:`, JSON.stringify(angelData, null, 2));
          } else {
            const errorText = await angelResponse.text();
            console.log(`❌ Angel API Error: ${angelResponse.status} - ${errorText}`);
          }
        }
      }
    } else {
      const errorText = await loginResponse.text();
      console.log(`❌ Login failed: ${loginResponse.status} - ${errorText}`);
    }
    
    console.log('\n🎯 TESTING COMPLETE!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

testLogoutAndLogin();
