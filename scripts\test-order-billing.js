const fetch = require('node-fetch');

async function testOrderBilling() {
  try {
    console.log('🧪 TESTING ORDER BILLING SYSTEM');
    console.log('=' .repeat(50));

    // First, let's login as the user who should place orders
    console.log('\n🔐 Step 1: Lo<PERSON> as user...');
    
    const loginResponse = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>', // The user who should place orders
        password: 'password123' // You might need to adjust this
      })
    });

    if (!loginResponse.ok) {
      console.log('❌ Login failed. Let me try different credentials...');
      
      // Try with admin credentials
      const adminLoginResponse = await fetch('http://localhost:3000/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>', // Admin user
          password: 'password123'
        })
      });

      if (!adminLoginResponse.ok) {
        console.log('❌ Admin login also failed. Checking available users...');
        return;
      }

      const adminLoginData = await adminLoginResponse.json();
      console.log('✅ Logged in as admin:', adminLoginData.user?.name);
      
      // Get cookies for subsequent requests
      const cookies = adminLoginResponse.headers.get('set-cookie');
      console.log('🍪 Got auth cookies');

      // Now let's check what broker accounts are available
      console.log('\n📋 Step 2: Check available broker accounts...');
      
      const angelResponse = await fetch('http://localhost:3000/api/users/angel', {
        headers: {
          'Cookie': cookies || ''
        }
      });

      if (angelResponse.ok) {
        const angelData = await angelResponse.json();
        console.log('📊 Angel accounts:', angelData.users?.length || 0);
        
        if (angelData.users && angelData.users.length > 0) {
          console.log('✅ Found Angel accounts:', angelData.users.map(u => u.clientName).join(', '));
          
          // Try to place a test order
          console.log('\n📈 Step 3: Place test order...');
          
          const orderData = {
            symbol: 'ASIANPAINT-EQ',
            quantity: 1,
            price: 2500,
            orderType: 'LIMIT',
            transactionType: 'BUY',
            productType: 'DELIVERY',
            clients: ['All Accounts'] // This should include all available accounts
          };

          const orderResponse = await fetch('http://localhost:3000/api/orders/angel', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Cookie': cookies || ''
            },
            body: JSON.stringify(orderData)
          });

          const orderResult = await orderResponse.json();
          console.log('📈 Order response:', orderResult);

          if (orderResponse.ok) {
            console.log('✅ Order placed successfully!');
            
            // Wait a moment for billing to process
            console.log('\n⏳ Waiting 3 seconds for billing to process...');
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // Check if billing was created
            console.log('\n💰 Step 4: Check billing records...');
            
            // We'll need to check the database directly since there's no API endpoint
            console.log('ℹ️  Please check the database for new billing records');
            console.log('ℹ️  Or check the server logs for billing messages');
            
          } else {
            console.log('❌ Order failed:', orderResult);
          }
        } else {
          console.log('❌ No Angel accounts found');
        }
      } else {
        console.log('❌ Failed to get Angel accounts');
      }

    } else {
      const loginData = await loginResponse.json();
      console.log('✅ Logged in as user:', loginData.user?.name);
    }

  } catch (error) {
    console.error('❌ Error testing order billing:', error);
  }
}

testOrderBilling();
