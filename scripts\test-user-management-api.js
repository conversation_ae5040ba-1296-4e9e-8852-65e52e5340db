const mongoose = require('mongoose');
const jwt = require('jsonwebtoken');
require('dotenv').config();

async function testUserManagementAPI() {
  try {
    const mongoUri = process.env.TESTLIST || 'mongodb://localhost:27017/trading-platform';
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');

    // Define models
    const User = mongoose.model('User', new mongoose.Schema({}, { strict: false }));
    
    console.log('\n🧪 TESTING USER MANAGEMENT API');
    console.log('=' .repeat(50));
    
    // Get the admin user (Avisekh ghosh)
    const adminUser = await User.findOne({ email: '<EMAIL>' });
    if (!adminUser) {
      console.log('❌ Admin user not found');
      return;
    }
    
    console.log(`👤 Found admin user: ${adminUser.name} (${adminUser.email})`);
    console.log(`   User ID: ${adminUser._id}`);
    console.log(`   Role: ${adminUser.role}`);
    
    // Create a JWT token for the admin user
    const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
    const token = jwt.sign(
      {
        userId: adminUser._id.toString(),
        email: adminUser.email,
        role: adminUser.role,
        name: adminUser.name
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    );
    
    console.log(`\n🔑 Generated JWT token for admin user`);
    console.log(`   Token: ${token.substring(0, 50)}...`);
    
    // Test the Angel users API endpoint
    console.log(`\n🔄 Testing /api/users/angel endpoint...`);
    
    const { default: fetch } = await import('node-fetch');
    const angelResponse = await fetch('http://localhost:3000/api/users/angel', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': `auth-token=${token}`
      }
    });
    
    console.log(`📡 Angel API Response Status: ${angelResponse.status}`);
    
    if (angelResponse.ok) {
      const angelData = await angelResponse.json();
      console.log(`✅ Angel API Response:`, JSON.stringify(angelData, null, 2));
    } else {
      const errorText = await angelResponse.text();
      console.log(`❌ Angel API Error: ${angelResponse.status} - ${errorText}`);
    }
    
    // Test the Motilal users API endpoint
    console.log(`\n🔄 Testing /api/users/motilal endpoint...`);
    
    const motilalResponse = await fetch('http://localhost:3000/api/users/motilal', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': `auth-token=${token}`
      }
    });
    
    console.log(`📡 Motilal API Response Status: ${motilalResponse.status}`);
    
    if (motilalResponse.ok) {
      const motilalData = await motilalResponse.json();
      console.log(`✅ Motilal API Response:`, JSON.stringify(motilalData, null, 2));
    } else {
      const errorText = await motilalResponse.text();
      console.log(`❌ Motilal API Error: ${motilalResponse.status} - ${errorText}`);
    }
    
    console.log('\n🎯 TESTING COMPLETE!');
    console.log('📋 Summary:');
    console.log(`   - Admin user exists: ✅`);
    console.log(`   - JWT token generated: ✅`);
    console.log(`   - Angel API accessible: ${angelResponse.ok ? '✅' : '❌'}`);
    console.log(`   - Motilal API accessible: ${motilalResponse.ok ? '✅' : '❌'}`);
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

testUserManagementAPI();
