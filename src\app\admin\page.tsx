'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import TradingOrderForm from '@/components/TradingOrderForm';

interface AdminDashboardData {
  admin: {
    name: string;
    email: string;
    userCode?: string;
    orderRate?: number;
    totalBilling?: number;
    totalOrders?: number;
  };
  statistics: {
    totalUsers: number;
    totalBrokerAccounts: number;
    angelAccounts: number;
    motilalAccounts: number;
    currentMonth: {
      orders: number;
      billing: number;
    };
    previousMonth: {
      orders: number;
      billing: number;
    };
    growth: {
      orders: number;
      billing: number;
    };
  };
  users: any[];
  currentMonthBilling: any;
  recentBillingRecords: any[];
  billingCycles: {
    current: string;
    previous: string;
  };
}

interface AccountData {
  angelAccounts: any[];
  motilalAccounts: any[];
  summary: {
    totalAngel: number;
    totalMotilal: number;
    totalAccounts: number;
    activeAngel: number;
    activeMotilal: number;
    totalUsers: number;
  };
}

export default function AdminDashboard() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [dashboardData, setDashboardData] = useState<AdminDashboardData | null>(null);
  const [dashboardLoading, setDashboardLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('dashboard');
  const [accountData, setAccountData] = useState<AccountData | null>(null);
  const [accountLoading, setAccountLoading] = useState(false);

  // Order placement state
  const [showOrderForm, setShowOrderForm] = useState(false);
  const [orderType, setOrderType] = useState<'BUY' | 'SELL'>('BUY');
  const [selectedScript, setSelectedScript] = useState<any>(null);

  // Track if we've already redirected to prevent loops
  const [hasRedirected, setHasRedirected] = useState(false);

  const fetchDashboardData = async () => {
    try {
      setDashboardLoading(true);

      // Use cookies instead of localStorage for authentication
      const response = await fetch('/api/admin/dashboard', {
        credentials: 'include' // This will include cookies
      });

      if (response.status === 401 || response.status === 403) {
        console.log('Authentication failed, redirecting to login');
        if (!hasRedirected) {
          setHasRedirected(true);
          setTimeout(() => {
            router.push('/login');
          }, 0);
        }
        return;
      }

      if (!response.ok) {
        throw new Error(`Failed to fetch dashboard data: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      if (result.success) {
        setDashboardData(result.data);
        setError('');
      } else {
        setError(result.error || 'Failed to load dashboard data');
      }
    } catch (error: any) {
      console.error('Dashboard fetch error:', error);
      if (error.message.includes('401') || error.message.includes('403')) {
        localStorage.removeItem('token');
        router.push('/login');
        return;
      }
      setError(error.message || 'Failed to load dashboard data');
    } finally {
      setDashboardLoading(false);
    }
  };

  // Fetch account data
  const fetchAccountData = async (type: string = 'all') => {
    try {
      setAccountLoading(true);
      setError('');

      const response = await fetch(`/api/admin/accounts?type=${type}`, {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch accounts: ${response.status}`);
      }

      const result = await response.json();
      if (result.success) {
        setAccountData(result.data);
      } else {
        setError(result.error || 'Failed to load account data');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch account data');
    } finally {
      setAccountLoading(false);
    }
  };

  // Order placement functions
  const handlePlaceOrder = (type: 'BUY' | 'SELL', script?: any) => {
    setOrderType(type);
    setSelectedScript(script || {
      ticker: 'CUSTOM',
      securityId: 'CUSTOM',
      ltp: 0,
      exchange: 'NSE'
    });
    setShowOrderForm(true);
  };

  const handleCloseOrderForm = () => {
    setShowOrderForm(false);
    setSelectedScript(null);
  };

  useEffect(() => {
    // Only redirect once and only when loading is complete
    if (!loading) {
      if (!user || user.role !== 'admin') {
        if (!hasRedirected) {
          console.log('Not admin, redirecting to login');
          setHasRedirected(true);

          // Use setTimeout to ensure this happens after render cycle
          setTimeout(() => {
            router.push('/login');
          }, 0);
        }
        return;
      }

      if (user && user.role === 'admin') {
        fetchDashboardData();
      }
    }
  }, [user, loading, router, hasRedirected]);

  // Load account data when tab changes
  useEffect(() => {
    if (user && user.role === 'admin') {
      if (activeTab === 'angel-accounts') {
        fetchAccountData('angel');
      } else if (activeTab === 'motilal-accounts') {
        fetchAccountData('motilal');
      } else if (activeTab === 'all-accounts') {
        fetchAccountData('all');
      }
    }
  }, [activeTab, user]);

  if (loading || dashboardLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h1 className="text-xl font-semibold text-gray-700">Loading Dashboard...</h1>
          <p className="text-gray-500">Please wait while we fetch your data</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Error</h1>
          <p className="text-gray-600">{error}</p>
          <button
            onClick={fetchDashboardData}
            className="mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-600 mb-4">No Data</h1>
          <p className="text-gray-600">No dashboard data available</p>
        </div>
      </div>
    );
  }

  const { admin } = dashboardData;

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
          <p className="text-gray-600 mt-2">
            Welcome back, {admin.name}! Manage your users and view your billing.
          </p>
        </div>

        {/* Navigation Tabs */}
        <div className="mb-6">
          <nav className="flex space-x-8">
            {[
              { id: 'dashboard', label: 'Dashboard', icon: '📊' },
              { id: 'all-accounts', label: 'All Accounts', icon: '🏦' },
              { id: 'angel-accounts', label: 'Angel Accounts', icon: '👼' },
              { id: 'motilal-accounts', label: 'Motilal Accounts', icon: '🏢' },
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                  activeTab === tab.id
                    ? 'bg-indigo-100 text-indigo-700'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Dashboard Content */}
        {activeTab === 'dashboard' && (
          <>
            {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">Total Users</h3>
            <p className="text-2xl font-bold text-gray-900">{dashboardData?.statistics?.totalUsers || 0}</p>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">Broker Accounts</h3>
            <p className="text-2xl font-bold text-gray-900">{dashboardData?.statistics?.totalBrokerAccounts || 0}</p>
            <div className="text-xs text-gray-500 mt-1">
              Angel: {dashboardData?.statistics?.angelAccounts || 0} | Motilal: {dashboardData?.statistics?.motilalAccounts || 0}
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">Orders This Month</h3>
            <p className="text-2xl font-bold text-gray-900">{dashboardData?.statistics?.currentMonth?.orders || 0}</p>
            <p className={`text-sm ${(dashboardData?.statistics?.growth?.orders || 0) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {(dashboardData?.statistics?.growth?.orders || 0) >= 0 ? '+' : ''}{(dashboardData?.statistics?.growth?.orders || 0).toFixed(1)}% from last month
            </p>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">Billing This Month</h3>
            <p className="text-2xl font-bold text-gray-900">₹{(dashboardData?.statistics?.currentMonth?.billing || 0).toFixed(2)}</p>
            <p className={`text-sm ${(dashboardData?.statistics?.growth?.billing || 0) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {(dashboardData?.statistics?.growth?.billing || 0) >= 0 ? '+' : ''}{(dashboardData?.statistics?.growth?.billing || 0).toFixed(1)}% from last month
            </p>
          </div>
        </div>

        {/* Billing Info */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Billing Information ({dashboardData?.billingCycles?.current || 'Current Month'})</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Order Rate:</span>
                <span className="font-semibold text-blue-600">₹{admin?.orderRate || 5} per order</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Total Orders:</span>
                <span className="font-semibold text-gray-900">{dashboardData?.statistics?.currentMonth?.orders || 0}</span>
              </div>
              <div className="flex justify-between border-t pt-2">
                <span className="text-gray-900 font-semibold">Total Billing:</span>
                <span className="font-bold text-red-600">₹{(dashboardData?.statistics?.currentMonth?.billing || 0).toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Status:</span>
                <span className="font-semibold text-orange-600">PENDING</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
            <div className="space-y-3">
              <a
                href="/trading"
                className="block w-full bg-blue-600 text-white text-center py-2 px-4 rounded hover:bg-blue-700 transition-colors"
              >
                Go to Trading
              </a>
              <button
                onClick={fetchDashboardData}
                className="block w-full bg-gray-600 text-white text-center py-2 px-4 rounded hover:bg-gray-700 transition-colors"
              >
                Refresh Data
              </button>
            </div>
          </div>
        </div>

        {/* Users List */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Your Users</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    User
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Angel Accounts
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Motilal Accounts
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Joined
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {dashboardData?.users?.map((user) => (
                  <tr key={user._id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{user.name}</div>
                        <div className="text-sm text-gray-500">{user.email}</div>
                        <div className="text-xs text-gray-400">{user.userCode}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {user.angelAccounts || 0}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {user.motilalAccounts || 0}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {user.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {user.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'N/A'}
                    </td>
                  </tr>
                )) || (
                  <tr>
                    <td colSpan={5} className="px-6 py-4 text-center text-gray-500">No users found</td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
          </>
        )}

        {/* Account Management Sections */}
        {(activeTab === 'all-accounts' || activeTab === 'angel-accounts' || activeTab === 'motilal-accounts') && (
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <div className="flex justify-between items-center mb-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">
                    {activeTab === 'all-accounts' && 'All Broker Accounts'}
                    {activeTab === 'angel-accounts' && 'Angel Broker Accounts'}
                    {activeTab === 'motilal-accounts' && 'Motilal Oswal Accounts'}
                  </h3>
                  <p className="text-sm text-gray-600 mt-1">
                    {activeTab === 'all-accounts' && 'Place orders for all your broker accounts'}
                    {activeTab === 'angel-accounts' && 'Place orders for all your Angel Broking accounts'}
                    {activeTab === 'motilal-accounts' && 'Place orders for all your Motilal Oswal accounts'}
                  </p>
                </div>
                <div className="flex space-x-3">
                  {/* Order Placement Buttons */}
                  <button
                    onClick={() => handlePlaceOrder('BUY')}
                    className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 focus:ring-2 focus:ring-green-500"
                  >
                    🛒 Place BUY Order
                  </button>
                  <button
                    onClick={() => handlePlaceOrder('SELL')}
                    className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 focus:ring-2 focus:ring-red-500"
                  >
                    💰 Place SELL Order
                  </button>
                  <button
                    onClick={() => fetchAccountData(activeTab.replace('-accounts', ''))}
                    disabled={accountLoading}
                    className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 disabled:opacity-50"
                  >
                    {accountLoading ? 'Loading...' : 'Refresh'}
                  </button>
                </div>
              </div>

              {error && (
                <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-4">
                  <div className="text-red-700">{error}</div>
                </div>
              )}

              {accountLoading ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                </div>
              ) : (
                <>
                  {/* Summary Cards */}
                  {accountData && (
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                      {(activeTab === 'all-accounts' || activeTab === 'angel-accounts') && (
                        <div className="bg-blue-50 p-4 rounded-lg">
                          <h4 className="font-medium text-blue-900">Angel Accounts</h4>
                          <p className="text-2xl font-bold text-blue-600">{accountData.summary.totalAngel}</p>
                          <p className="text-sm text-blue-700">{accountData.summary.activeAngel} active</p>
                        </div>
                      )}
                      {(activeTab === 'all-accounts' || activeTab === 'motilal-accounts') && (
                        <div className="bg-green-50 p-4 rounded-lg">
                          <h4 className="font-medium text-green-900">Motilal Accounts</h4>
                          <p className="text-2xl font-bold text-green-600">{accountData.summary.totalMotilal}</p>
                          <p className="text-sm text-green-700">{accountData.summary.activeMotilal} active</p>
                        </div>
                      )}
                      {activeTab === 'all-accounts' && (
                        <div className="bg-purple-50 p-4 rounded-lg">
                          <h4 className="font-medium text-purple-900">Total Accounts</h4>
                          <p className="text-2xl font-bold text-purple-600">{accountData.summary.totalAccounts}</p>
                          <p className="text-sm text-purple-700">All brokers combined</p>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Angel Accounts Table */}
                  {accountData && (activeTab === 'all-accounts' || activeTab === 'angel-accounts') && accountData.angelAccounts.length > 0 && (
                    <div className="mb-8">
                      <h4 className="text-lg font-medium text-gray-900 mb-4">Angel Broker Accounts</h4>
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Client Details
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Owner
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Capital
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Created
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {accountData.angelAccounts.map((account) => (
                              <tr key={account._id}>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <div>
                                    <div className="text-sm font-medium text-gray-900">{account.clientName}</div>
                                    <div className="text-sm text-gray-500">{account.userId}</div>
                                    <div className="text-xs text-gray-400">{account.email}</div>
                                  </div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <div>
                                    <div className="text-sm font-medium text-gray-900">
                                      {account.owner?.name || 'No Owner'}
                                    </div>
                                    <div className="text-sm text-gray-500">
                                      {account.owner?.userCode || 'N/A'}
                                    </div>
                                  </div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                  ₹{account.capital?.toLocaleString() || 0}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                    account.state === 'live'
                                      ? 'bg-green-100 text-green-800'
                                      : 'bg-red-100 text-red-800'
                                  }`}>
                                    {account.state || 'inactive'}
                                  </span>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                  {account.createdAt ? new Date(account.createdAt).toLocaleDateString() : 'N/A'}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}

                  {/* Motilal Accounts Table */}
                  {accountData && (activeTab === 'all-accounts' || activeTab === 'motilal-accounts') && accountData.motilalAccounts.length > 0 && (
                    <div className="mb-8">
                      <h4 className="text-lg font-medium text-gray-900 mb-4">Motilal Oswal Accounts</h4>
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Client Details
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Owner
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Capital
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Created
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {accountData.motilalAccounts.map((account) => (
                              <tr key={account._id}>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <div>
                                    <div className="text-sm font-medium text-gray-900">{account.clientName}</div>
                                    <div className="text-sm text-gray-500">{account.userId}</div>
                                    <div className="text-xs text-gray-400">{account.email}</div>
                                  </div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <div>
                                    <div className="text-sm font-medium text-gray-900">
                                      {account.owner?.name || 'No Owner'}
                                    </div>
                                    <div className="text-sm text-gray-500">
                                      {account.owner?.userCode || 'N/A'}
                                    </div>
                                  </div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                  ₹{account.capital?.toLocaleString() || 0}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                    account.status === 'active'
                                      ? 'bg-green-100 text-green-800'
                                      : 'bg-red-100 text-red-800'
                                  }`}>
                                    {account.status || 'inactive'}
                                  </span>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                  {account.createdAt ? new Date(account.createdAt).toLocaleDateString() : 'N/A'}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}

                  {/* No Data Message */}
                  {accountData &&
                   ((activeTab === 'angel-accounts' && accountData.angelAccounts.length === 0) ||
                    (activeTab === 'motilal-accounts' && accountData.motilalAccounts.length === 0) ||
                    (activeTab === 'all-accounts' && accountData.angelAccounts.length === 0 && accountData.motilalAccounts.length === 0)) && (
                    <div className="text-center py-8">
                      <div className="text-gray-500">
                        <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <h3 className="mt-2 text-sm font-medium text-gray-900">No accounts found</h3>
                        <p className="mt-1 text-sm text-gray-500">
                          {activeTab === 'angel-accounts' && 'No Angel broker accounts are currently configured for your users.'}
                          {activeTab === 'motilal-accounts' && 'No Motilal Oswal accounts are currently configured for your users.'}
                          {activeTab === 'all-accounts' && 'No broker accounts are currently configured for your users.'}
                        </p>
                      </div>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        )}

        {/* Order Placement Form */}
        {showOrderForm && selectedScript && (
          <TradingOrderForm
            isOpen={showOrderForm}
            onClose={handleCloseOrderForm}
            orderType={orderType}
            scriptData={selectedScript}
          />
        )}
      </div>
    </div>
  );
}
