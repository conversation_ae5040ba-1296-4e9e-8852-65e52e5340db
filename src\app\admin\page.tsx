'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';

interface AdminDashboardData {
  admin: {
    name: string;
    email: string;
    userCode?: string;
    orderRate?: number;
    totalBilling?: number;
    totalOrders?: number;
  };
  statistics: {
    totalUsers: number;
    totalBrokerAccounts: number;
    angelAccounts: number;
    motilalAccounts: number;
    currentMonth: {
      orders: number;
      billing: number;
    };
    previousMonth: {
      orders: number;
      billing: number;
    };
    growth: {
      orders: number;
      billing: number;
    };
  };
  users: any[];
  currentMonthBilling: any;
  recentBillingRecords: any[];
  billingCycles: {
    current: string;
    previous: string;
  };
}

export default function AdminDashboard() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [dashboardData, setDashboardData] = useState<AdminDashboardData | null>(null);
  const [dashboardLoading, setDashboardLoading] = useState(true);
  const [error, setError] = useState('');

  // Track if we've already redirected to prevent loops
  const [hasRedirected, setHasRedirected] = useState(false);

  const fetchDashboardData = async () => {
    try {
      setDashboardLoading(true);

      // Use cookies instead of localStorage for authentication
      const response = await fetch('/api/admin/dashboard', {
        credentials: 'include' // This will include cookies
      });

      if (response.status === 401 || response.status === 403) {
        console.log('Authentication failed, redirecting to login');
        if (!hasRedirected) {
          setHasRedirected(true);
          setTimeout(() => {
            router.push('/login');
          }, 0);
        }
        return;
      }

      if (!response.ok) {
        throw new Error(`Failed to fetch dashboard data: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      if (result.success) {
        setDashboardData(result.data);
        setError('');
      } else {
        setError(result.error || 'Failed to load dashboard data');
      }
    } catch (error: any) {
      console.error('Dashboard fetch error:', error);
      if (error.message.includes('401') || error.message.includes('403')) {
        localStorage.removeItem('token');
        router.push('/login');
        return;
      }
      setError(error.message || 'Failed to load dashboard data');
    } finally {
      setDashboardLoading(false);
    }
  };

  useEffect(() => {
    // Only redirect once and only when loading is complete
    if (!loading) {
      if (!user || user.role !== 'admin') {
        if (!hasRedirected) {
          console.log('Not admin, redirecting to login');
          setHasRedirected(true);

          // Use setTimeout to ensure this happens after render cycle
          setTimeout(() => {
            router.push('/login');
          }, 0);
        }
        return;
      }

      if (user && user.role === 'admin') {
        fetchDashboardData();
      }
    }
  }, [user, loading, router, hasRedirected]);

  if (loading || dashboardLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h1 className="text-xl font-semibold text-gray-700">Loading Dashboard...</h1>
          <p className="text-gray-500">Please wait while we fetch your data</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Error</h1>
          <p className="text-gray-600">{error}</p>
          <button
            onClick={fetchDashboardData}
            className="mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-600 mb-4">No Data</h1>
          <p className="text-gray-600">No dashboard data available</p>
        </div>
      </div>
    );
  }

  const { admin } = dashboardData;

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
          <p className="text-gray-600 mt-2">
            Welcome back, {admin.name}! Manage your users and view your billing.
          </p>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">Total Users</h3>
            <p className="text-2xl font-bold text-gray-900">{dashboardData?.statistics?.totalUsers || 0}</p>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">Broker Accounts</h3>
            <p className="text-2xl font-bold text-gray-900">{dashboardData?.statistics?.totalBrokerAccounts || 0}</p>
            <div className="text-xs text-gray-500 mt-1">
              Angel: {dashboardData?.statistics?.angelAccounts || 0} | Motilal: {dashboardData?.statistics?.motilalAccounts || 0}
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">Orders This Month</h3>
            <p className="text-2xl font-bold text-gray-900">{dashboardData?.statistics?.currentMonth?.orders || 0}</p>
            <p className={`text-sm ${(dashboardData?.statistics?.growth?.orders || 0) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {(dashboardData?.statistics?.growth?.orders || 0) >= 0 ? '+' : ''}{(dashboardData?.statistics?.growth?.orders || 0).toFixed(1)}% from last month
            </p>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">Billing This Month</h3>
            <p className="text-2xl font-bold text-gray-900">₹{(dashboardData?.statistics?.currentMonth?.billing || 0).toFixed(2)}</p>
            <p className={`text-sm ${(dashboardData?.statistics?.growth?.billing || 0) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {(dashboardData?.statistics?.growth?.billing || 0) >= 0 ? '+' : ''}{(dashboardData?.statistics?.growth?.billing || 0).toFixed(1)}% from last month
            </p>
          </div>
        </div>

        {/* Billing Info */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Billing Information ({dashboardData?.billingCycles?.current || 'Current Month'})</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Order Rate:</span>
                <span className="font-semibold text-blue-600">₹{admin?.orderRate || 5} per order</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Total Orders:</span>
                <span className="font-semibold text-gray-900">{dashboardData?.statistics?.currentMonth?.orders || 0}</span>
              </div>
              <div className="flex justify-between border-t pt-2">
                <span className="text-gray-900 font-semibold">Total Billing:</span>
                <span className="font-bold text-red-600">₹{(dashboardData?.statistics?.currentMonth?.billing || 0).toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Status:</span>
                <span className="font-semibold text-orange-600">PENDING</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
            <div className="space-y-3">
              <a
                href="/trading"
                className="block w-full bg-blue-600 text-white text-center py-2 px-4 rounded hover:bg-blue-700 transition-colors"
              >
                Go to Trading
              </a>
              <button
                onClick={fetchDashboardData}
                className="block w-full bg-gray-600 text-white text-center py-2 px-4 rounded hover:bg-gray-700 transition-colors"
              >
                Refresh Data
              </button>
            </div>
          </div>
        </div>

        {/* Users List */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Your Users</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    User
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Angel Accounts
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Motilal Accounts
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Joined
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {dashboardData?.users?.map((user) => (
                  <tr key={user._id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{user.name}</div>
                        <div className="text-sm text-gray-500">{user.email}</div>
                        <div className="text-xs text-gray-400">{user.userCode}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {user.angelAccounts || 0}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {user.motilalAccounts || 0}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {user.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {user.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'N/A'}
                    </td>
                  </tr>
                )) || (
                  <tr>
                    <td colSpan={5} className="px-6 py-4 text-center text-gray-500">No users found</td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}
