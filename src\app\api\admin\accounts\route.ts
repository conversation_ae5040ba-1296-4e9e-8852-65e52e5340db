import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { verifyAuth } from '@/middleware/auth';
import AngelUser from '@/models/AngelUser';
import MotilalUser from '@/models/MotilalUser';
import User from '@/models/User';

export async function GET(request: Request) {
  try {
    await connectDB();
    
    // Verify admin authentication
    const decoded = await verifyAuth(request);
    
    if (decoded.role !== 'admin') {
      return NextResponse.json(
        { error: 'Access denied. Admin required.' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type'); // 'angel', 'motilal', or 'all'

    // Get all users under this admin
    const adminUsers = await User.find({ 
      adminId: decoded.userId,
      role: 'user'
    }).select('_id name email userCode');

    const userIds = adminUsers.map(user => user._id);

    let angelAccounts = [];
    let motilalAccounts = [];

    if (type === 'angel' || type === 'all' || !type) {
      // Get Angel accounts owned by admin's users
      angelAccounts = await AngelUser.find({ 
        owner: { $in: userIds } 
      }).populate('owner', 'name email userCode role')
        .sort({ createdAt: -1 });
    }

    if (type === 'motilal' || type === 'all' || !type) {
      // Get Motilal accounts owned by admin's users
      motilalAccounts = await MotilalUser.find({ 
        owner: { $in: userIds } 
      }).populate('owner', 'name email userCode role')
        .sort({ createdAt: -1 });
    }

    // Format the response
    const response = {
      success: true,
      data: {
        angelAccounts: angelAccounts.map(account => ({
          _id: account._id,
          userId: account.userId,
          clientName: account.clientName,
          email: account.email,
          phoneNumber: account.phoneNumber,
          capital: account.capital,
          state: account.state,
          owner: account.owner,
          createdAt: account.createdAt,
          updatedAt: account.updatedAt
        })),
        motilalAccounts: motilalAccounts.map(account => ({
          _id: account._id,
          userId: account.userId,
          clientName: account.clientName,
          email: account.email,
          phoneNumber: account.phoneNumber,
          capital: account.capital,
          status: account.status,
          owner: account.owner,
          createdAt: account.createdAt,
          updatedAt: account.updatedAt
        })),
        summary: {
          totalAngel: angelAccounts.length,
          totalMotilal: motilalAccounts.length,
          totalAccounts: angelAccounts.length + motilalAccounts.length,
          activeAngel: angelAccounts.filter(acc => acc.state === 'live').length,
          activeMotilal: motilalAccounts.filter(acc => acc.status === 'active').length,
          totalUsers: adminUsers.length
        }
      }
    };

    return NextResponse.json(response);

  } catch (error: any) {
    console.error('Error fetching admin accounts:', error);
    
    if (error.message === 'No token provided' || error.name === 'JsonWebTokenError') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to fetch accounts' },
      { status: 500 }
    );
  }
}

// PUT - Update account status (admin can only update their users' accounts)
export async function PUT(request: Request) {
  try {
    await connectDB();
    
    // Verify admin authentication
    const decoded = await verifyAuth(request);
    
    if (decoded.role !== 'admin') {
      return NextResponse.json(
        { error: 'Access denied. Admin required.' },
        { status: 403 }
      );
    }

    const { accountId, accountType, updates } = await request.json();

    if (!accountId || !accountType || !updates) {
      return NextResponse.json(
        { error: 'Account ID, type, and updates are required' },
        { status: 400 }
      );
    }

    // Get all users under this admin
    const adminUsers = await User.find({ 
      adminId: decoded.userId,
      role: 'user'
    }).select('_id');

    const userIds = adminUsers.map(user => user._id);

    let updatedAccount;

    if (accountType === 'angel') {
      // Verify the account belongs to admin's users
      const account = await AngelUser.findOne({
        _id: accountId,
        owner: { $in: userIds }
      });

      if (!account) {
        return NextResponse.json(
          { error: 'Account not found or access denied' },
          { status: 404 }
        );
      }

      updatedAccount = await AngelUser.findByIdAndUpdate(
        accountId,
        updates,
        { new: true }
      ).populate('owner', 'name email userCode role');
    } else if (accountType === 'motilal') {
      // Verify the account belongs to admin's users
      const account = await MotilalUser.findOne({
        _id: accountId,
        owner: { $in: userIds }
      });

      if (!account) {
        return NextResponse.json(
          { error: 'Account not found or access denied' },
          { status: 404 }
        );
      }

      updatedAccount = await MotilalUser.findByIdAndUpdate(
        accountId,
        updates,
        { new: true }
      ).populate('owner', 'name email userCode role');
    } else {
      return NextResponse.json(
        { error: 'Invalid account type' },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Account updated successfully',
      account: updatedAccount
    });

  } catch (error: any) {
    console.error('Error updating account:', error);
    
    if (error.message === 'No token provided' || error.name === 'JsonWebTokenError') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to update account' },
      { status: 500 }
    );
  }
}
