// Admin Dashboard API - Focused on Broker Accounts & Admin Management
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import Angel<PERSON>ser from '@/models/AngelUser';
import MotilalUser from '@/models/MotilalUser';
import { BillingRecord } from '@/models/Billing';
import { requireAdmin, getCurrentBillingCycle, getPreviousBillingCycle } from '@/middleware/auth';

export async function GET(request: Request) {
  try {
    console.log('🔍 Admin Dashboard API called');

    // Verify admin access
    const { user, hasAccess } = await requireAdmin(request);

    console.log('👤 Admin auth result:', { user: user?.email, role: user?.role, hasAccess });

    if (!hasAccess || !user) {
      console.log('❌ Admin access denied');
      return Response.json({ error: 'Unauthorized - Admin access required' }, { status: 403 });
    }

    await connectDB();
    console.log('✅ Database connected');

    // Get current and previous billing cycles
    const currentCycle = getCurrentBillingCycle();
    const previousCycle = getPreviousBillingCycle();

    // 1. Get admin's own details
    const adminDetails = await User.findById(user.userId).select('-password').lean() as any;

    if (!adminDetails) {
      return Response.json({ error: 'Admin not found' }, { status: 404 });
    }

    // 2. Get all users under this admin (broker account owners)
    const users = await User.find({
      adminId: user.userId,
      role: 'user',
      isActive: true
    }).select('userCode name email companyName isActive createdAt').lean();

    // 3. Get broker accounts for admin's users and admin themselves (hide sensitive data)
    const userIds = [...users.map(u => u._id), user.userId]; // Include admin's ID
    console.log('Looking for broker accounts for users:', userIds);

    const angelAccounts = await AngelUser.find({
      owner: { $in: userIds },
      state: 'live'
    }).select('-password -totpKey -apiKey -jwtToken -refreshToken -feedToken')
      .lean();

    console.log('Found Angel accounts:', angelAccounts.length);

    const motilalAccounts = await MotilalUser.find({
      owner: { $in: userIds },
      status: 'active'
    }).select('-password -totpKey -apiKey -authToken')
      .lean();

    console.log('Found Motilal accounts:', motilalAccounts.length);

    // 4. Calculate current month billing directly from billing records
    console.log('Looking for billing records for admin:', user.userId, 'cycle:', currentCycle);
    const currentMonthBillingRecords = await BillingRecord.find({
      adminId: user.userId,
      billingCycle: currentCycle
    }).sort({ createdAt: -1 }).limit(50).lean();

    console.log('Found billing records:', currentMonthBillingRecords.length);

    // 5. Get recent billing records (last 10)
    const recentBillingRecords = await BillingRecord.find({
      adminId: user.userId
    }).sort({ createdAt: -1 }).limit(10).lean();

    // 6. Calculate current month totals from billing records
    // Let's try a direct count and sum instead of aggregation
    const allBillingRecords = await BillingRecord.find({
      billingCycle: currentCycle
    }).lean();

    console.log('All billing records for cycle:', currentCycle, 'count:', allBillingRecords.length);

    // Filter records for this admin
    const adminBillingRecords = allBillingRecords.filter(record =>
      record.adminId && record.adminId.toString() === user.userId
    );

    console.log('Admin billing records:', adminBillingRecords.length);

    // Calculate totals manually
    const totalOrders = adminBillingRecords.length;
    const totalBillingAmount = adminBillingRecords.reduce((sum, record) => sum + (record.amount || 0), 0);

    console.log('Calculated totals:', { totalOrders, totalBillingAmount });

    // 7. Calculate previous month totals for growth calculation
    const previousBillingRecords = await BillingRecord.find({
      billingCycle: previousCycle
    }).lean();

    // Filter records for this admin
    const adminPreviousBillingRecords = previousBillingRecords.filter(record =>
      record.adminId && record.adminId.toString() === user.userId
    );

    // Calculate previous month totals
    const previousMonthOrders = adminPreviousBillingRecords.length;
    const previousMonthAmount = adminPreviousBillingRecords.reduce((sum, record) => sum + (record.amount || 0), 0);



    // 8. Calculate statistics
    const totalBrokerAccounts = angelAccounts.length + motilalAccounts.length;
    const totalUsers = users.length;

    const orderGrowth = previousMonthOrders
      ? ((totalOrders - previousMonthOrders) / previousMonthOrders * 100)
      : 0;

    const billingGrowth = previousMonthAmount
      ? ((totalBillingAmount - previousMonthAmount) / previousMonthAmount * 100)
      : 0;



    // 10. Return dashboard data
    return Response.json({
      success: true,
      data: {
        admin: {
          name: adminDetails.name,
          email: adminDetails.email,
          userCode: adminDetails.userCode,
          orderRate: adminDetails.orderRate || 0,
          totalBilling: adminDetails.totalBilling || 0,
          totalOrders: adminDetails.totalOrders || 0
        },
        statistics: {
          totalBrokerAccounts,
          totalUsers,
          angelAccounts: angelAccounts.length,
          motilalAccounts: motilalAccounts.length,
          currentMonth: {
            orders: totalOrders,
            billing: totalBillingAmount
          },
          previousMonth: {
            orders: previousMonthOrders,
            billing: previousMonthAmount
          },
          growth: {
            orders: orderGrowth,
            billing: billingGrowth
          }
        },
        users: [
          // Include admin as a user in the list
          {
            _id: adminDetails._id,
            name: adminDetails.name,
            email: adminDetails.email,
            userCode: adminDetails.userCode,
            role: 'admin',
            isAdmin: true,
            angelAccounts: angelAccounts.filter(acc => acc.owner?.toString() === user.userId).length,
            motilalAccounts: motilalAccounts.filter(acc => acc.owner?.toString() === user.userId).length
          },
          // Include regular users
          ...users.map(user => {
            const userAngelAccounts = angelAccounts.filter(acc => acc.owner?.toString() === (user as any)._id.toString()).length;
            const userMotilalAccounts = motilalAccounts.filter(acc => acc.owner?.toString() === (user as any)._id.toString()).length;

            return {
              ...user,
              isAdmin: false,
              angelAccounts: userAngelAccounts,
              motilalAccounts: userMotilalAccounts
            };
          })
        ],
        currentMonthBilling: {
          totalOrders,
          totalAmount: totalBillingAmount,
          billingCycle: currentCycle
        },
        recentBillingRecords: recentBillingRecords,
        billingCycles: {
          current: currentCycle,
          previous: previousCycle
        }
      }
    });

  } catch (error: any) {
    console.error('❌ Admin Dashboard API Error:', error);
    return Response.json(
      { 
        error: 'Internal server error',
        message: error.message 
      }, 
      { status: 500 }
    );
  }
}
