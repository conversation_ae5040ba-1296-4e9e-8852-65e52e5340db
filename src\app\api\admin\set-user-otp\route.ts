import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { verifyAuth } from '@/middleware/auth';

export async function POST(request: Request) {
  try {
    console.log('🔍 Set User OTP API called');
    
    // Verify admin authentication
    const authResult = await verifyAuth(request);
    console.log('👤 Admin auth result:', authResult);

    if (!authResult || authResult.role !== 'admin') {
      return Response.json({ error: 'Admin access required' }, { status: 403 });
    }

    const { userId, otp } = await request.json();

    if (!userId || !otp) {
      return Response.json({ error: 'User ID and OTP are required' }, { status: 400 });
    }

    // Validate OTP format (6 digits)
    if (!/^\d{6}$/.test(otp)) {
      return Response.json({ error: 'OTP must be 6 digits' }, { status: 400 });
    }

    await connectDB();
    console.log('✅ Database connected');

    // Get the admin user
    const admin = await User.findOne({
      email: authResult.email,
      role: 'admin'
    });

    if (!admin) {
      return Response.json({ error: 'Admin not found' }, { status: 404 });
    }

    // Get the user to update
    const user = await User.findOne({
      _id: userId,
      adminId: admin._id, // Ensure user belongs to this admin
      role: 'user'
    });

    if (!user) {
      return Response.json({ error: 'User not found or not under your management' }, { status: 404 });
    }

    console.log(`👤 Setting OTP for user: ${user.name} (${user.email})`);
    console.log(`🔑 New OTP: ${otp}`);

    // Update user's OTP
    await User.findByIdAndUpdate(userId, {
      $set: { userOtp: otp }
    });

    console.log(`✅ OTP set successfully for user: ${user.name}`);

    return Response.json({
      success: true,
      message: `OTP set successfully for ${user.name}`,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        otp: otp
      }
    });

  } catch (error: any) {
    console.error('❌ Error setting user OTP:', error);
    return Response.json({ 
      error: 'Internal server error',
      details: error.message 
    }, { status: 500 });
  }
}
