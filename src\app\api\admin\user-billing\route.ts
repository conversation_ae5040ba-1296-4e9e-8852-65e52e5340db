
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { BillingRecord } from '@/models/Billing';
import { verifyAuth } from '@/middleware/auth';

export async function GET(request: Request) {
  try {
    console.log('🔍 Admin User Billing API called');
    
    // Verify admin authentication
    const authResult = await verifyAuth(request);
    console.log('👤 Admin auth result:', authResult);

    if (!authResult || authResult.role !== 'admin') {
      return Response.json({ error: 'Admin access required' }, { status: 403 });
    }

    await connectDB();
    console.log('✅ Database connected');

    // Get the admin user
    const admin = await User.findOne({
      email: authResult.email,
      role: 'admin'
    });

    if (!admin) {
      return Response.json({ error: 'Admin not found' }, { status: 404 });
    }

    console.log(`👤 Admin found: ${admin.name} (${admin.email})`);

    // Get all users under this admin
    const users = await User.find({
      adminId: admin._id,
      role: 'user'
    }).select('_id name email userCode companyName');

    console.log(`👥 Found ${users.length} users under admin`);

    // Get billing data for each user
    const userBillingData = [];

    for (const user of users) {
      // Get all billing records for this user
      const billingRecords = await BillingRecord.find({
        userId: user._id,
        adminId: admin._id
      }).sort({ createdAt: -1 });

      // Calculate totals
      const totalOrders = billingRecords.length;
      const totalBilling = billingRecords.reduce((sum, record) => sum + (record.amount || 0), 0);

      // Calculate current month data
      const currentMonth = new Date();
      const startOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
      const endOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0);

      const currentMonthRecords = billingRecords.filter(record => {
        const recordDate = new Date(record.createdAt);
        return recordDate >= startOfMonth && recordDate <= endOfMonth;
      });

      const currentMonthOrders = currentMonthRecords.length;
      const currentMonthBilling = currentMonthRecords.reduce((sum, record) => sum + (record.amount || 0), 0);

      // Get last order date
      const lastOrderDate = billingRecords.length > 0 ? billingRecords[0].createdAt : null;

      userBillingData.push({
        userId: user._id,
        userCode: user.userCode,
        userName: user.name,
        userEmail: user.email,
        userCompany: user.companyName,
        totalOrders,
        totalBilling,
        currentMonthOrders,
        currentMonthBilling,
        lastOrderDate,
        recentOrders: billingRecords.slice(0, 5).map(record => ({
          orderId: record.orderId,
          symbol: record.symbol,
          amount: record.amount,
          broker: record.broker,
          createdAt: record.createdAt,
          status: record.status
        }))
      });
    }

    // Sort by total billing (highest first)
    userBillingData.sort((a, b) => b.totalBilling - a.totalBilling);

    console.log(`📊 Prepared billing data for ${userBillingData.length} users`);

    // Calculate summary statistics
    const summary = {
      totalUsers: users.length,
      totalOrdersAllUsers: userBillingData.reduce((sum, user) => sum + user.totalOrders, 0),
      totalBillingAllUsers: userBillingData.reduce((sum, user) => sum + user.totalBilling, 0),
      currentMonthOrdersAllUsers: userBillingData.reduce((sum, user) => sum + user.currentMonthOrders, 0),
      currentMonthBillingAllUsers: userBillingData.reduce((sum, user) => sum + user.currentMonthBilling, 0),
      activeUsers: userBillingData.filter(user => user.totalOrders > 0).length
    };

    return Response.json({
      success: true,
      userBilling: userBillingData,
      summary,
      admin: {
        name: admin.name,
        email: admin.email,
        orderRate: admin.orderRate || 5
      }
    });

  } catch (error: any) {
    console.error('❌ Error in admin user billing API:', error);
    return Response.json({ 
      error: 'Internal server error',
      details: error.message 
    }, { status: 500 });
  }
}
