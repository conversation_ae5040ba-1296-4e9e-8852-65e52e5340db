// Using standard Request for Next.js 15 compatibility
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { verifyInvitationToken } from '@/middleware/auth';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { invitationToken, newPassword, confirmPassword } = body;

    // Validate required fields
    if (!invitationToken || !newPassword || !confirmPassword) {
      return Response.json({ 
        error: 'Missing required fields: invitationToken, newPassword, confirmPassword' 
      }, { status: 400 });
    }

    // Validate password match
    if (newPassword !== confirmPassword) {
      return Response.json({ 
        error: 'Passwords do not match' 
      }, { status: 400 });
    }

    // Validate password strength
    if (newPassword.length < 8) {
      return Response.json({ 
        error: 'Password must be at least 8 characters long' 
      }, { status: 400 });
    }

    // Verify invitation token
    try {
      verifyInvitationToken(invitationToken);
    } catch (error) {
      return Response.json({ 
        error: 'Invalid or expired invitation token' 
      }, { status: 400 });
    }

    await connectDB();

    // Find user with this invitation token
    const user = await User.findOne({
      invitationToken,
      isInvitationAccepted: false,
      invitationExpiry: { $gt: new Date() }
    });

    if (!user) {
      return Response.json({ 
        error: 'Invalid invitation or invitation has expired' 
      }, { status: 404 });
    }

    // Hash new password
    const salt = await bcrypt.genSalt(12);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

    // Update user - accept invitation and set new password
    await User.findByIdAndUpdate(user._id, {
      password: hashedPassword,
      isInvitationAccepted: true,
      invitationToken: null, // Clear the token
      invitationExpiry: null,
    });

    console.log(`✅ Invitation accepted by ${user.email} (${user.name})`);

    // Generate JWT token for immediate login
    const token = jwt.sign(
      { 
        userId: user._id,
        email: user.email,
        role: user.role 
      },
      JWT_SECRET,
      { expiresIn: '7d' }
    );

    // Create response with cookie
    const response = Response.json({
      success: true,
      message: 'Invitation accepted successfully',
      user: {
        id: user._id,
        email: user.email,
        name: user.name,
        role: user.role,
        companyName: user.companyName,
        orderRate: user.orderRate,
      },
      token,
    }, {
      status: 200,
      headers: {
        'Set-Cookie': `auth-token=${token}; HttpOnly; ${process.env.NODE_ENV === 'production' ? 'Secure;' : ''} SameSite=Strict; Max-Age=${7 * 24 * 60 * 60}; Path=/`
      }
    });

    return response;

  } catch (error) {
    console.error('Accept Invitation Error:', error);
    return Response.json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// GET: Verify invitation token and get invitation details
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const invitationToken = searchParams.get('token');

    if (!invitationToken) {
      return Response.json({ 
        error: 'Invitation token is required' 
      }, { status: 400 });
    }

    // Verify invitation token
    try {
      verifyInvitationToken(invitationToken);
    } catch (error) {
      return Response.json({ 
        error: 'Invalid or expired invitation token' 
      }, { status: 400 });
    }

    await connectDB();

    // Find user with this invitation token
    const user = await User.findOne({
      invitationToken,
      isInvitationAccepted: false,
      invitationExpiry: { $gt: new Date() }
    }).populate('invitedBy', 'name email companyName').select('-password');

    if (!user) {
      return Response.json({ 
        error: 'Invalid invitation or invitation has expired' 
      }, { status: 404 });
    }

    return Response.json({
      success: true,
      data: {
        email: user.email,
        name: user.name,
        role: user.role,
        companyName: user.companyName,
        orderRate: user.orderRate,
        invitedBy: user.invitedBy,
        invitationExpiry: user.invitationExpiry,
        isValid: true
      }
    });

  } catch (error) {
    console.error('Verify Invitation Error:', error);
    return Response.json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
