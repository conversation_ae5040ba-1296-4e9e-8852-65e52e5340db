import { loginAllAngelUsers, loginAllMotilalUsers, loginAngelUser, loginMotilalUser } from '@/lib/brokerAuth';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// Middleware to verify authentication
async function verifyAuth(request: Request) {
  try {
    const authHeader = request.headers.get('authorization');
    const cookieHeader = request.headers.get('cookie');

    let token = null;

    // Extract token from Authorization header
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.replace('Bearer ', '').trim();
    }

    // Extract token from cookies if not found in header
    if (!token && cookieHeader) {
      const cookieMatch = cookieHeader.match(/auth-token=([^;]+)/);
      if (cookieMatch) {
        token = cookieMatch[1].trim();
      }
    }

    // Check if token is valid
    if (!token || token === 'undefined' || token === 'null' || token.length < 10) {
      console.log('⚠️ No valid JWT token provided - proceeding without authentication for testing');
      return { userId: 'test-user', role: 'user' };
    }

    // Verify JWT token
    const decoded = jwt.verify(token, JWT_SECRET) as any;
    console.log('✅ JWT token verified successfully');
    return decoded;
  } catch (error) {
    console.error('JWT verification failed:', error);
    console.log('⚠️ Proceeding without authentication for testing purposes');
    return { userId: 'test-user', role: 'user' };
  }
}

export async function POST(request: Request) {
  try {
    console.log('📥 Broker login request received');

    // Verify authentication (non-blocking for testing)
    const user = await verifyAuth(request);
    console.log('👤 Authentication result:', user ? 'Success' : 'Failed (proceeding anyway)');

    const { action, broker, userId, password, totpKey, apiKey, twoFA } = await request.json();

    switch (action) {
      case 'login-all-angel':
        const angelResults = await loginAllAngelUsers();
        return Response.json(angelResults);

      case 'login-all-motilal':
        const motilalResults = await loginAllMotilalUsers();
        return Response.json(motilalResults);

      case 'login-single-angel':
        if (!userId || !password || !totpKey || !apiKey) {
          return Response.json({ 
            error: 'Missing required fields for Angel login',
            required: ['userId', 'password', 'totpKey', 'apiKey']
          }, { status: 400 });
        }
        
        const angelResult = await loginAngelUser(userId, password, totpKey, apiKey);
        return Response.json(angelResult);

      case 'login-single-motilal':
        if (!userId || !password || !apiKey || !twoFA || !totpKey) {
          return Response.json({ 
            error: 'Missing required fields for Motilal login',
            required: ['userId', 'password', 'apiKey', 'twoFA', 'totpKey']
          }, { status: 400 });
        }
        
        const motilalResult = await loginMotilalUser(userId, password, apiKey, twoFA, totpKey);
        return Response.json(motilalResult);

      default:
        return Response.json({ 
          error: 'Invalid action',
          validActions: ['login-all-angel', 'login-all-motilal', 'login-single-angel', 'login-single-motilal']
        }, { status: 400 });
    }

  } catch (error) {
    console.error('Error in broker login:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return Response.json({
      error: 'Internal server error',
      message: errorMessage
    }, { status: 500 });
  }
}

export async function GET(request: Request) {
  try {
    // Verify authentication
    const user = await verifyAuth(request);
    if (!user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    return Response.json({
      message: 'Broker login API',
      endpoints: {
        'POST /api/auth/broker-login': {
          description: 'Login to broker accounts',
          actions: [
            'login-all-angel',
            'login-all-motilal', 
            'login-single-angel',
            'login-single-motilal'
          ]
        }
      }
    });

  } catch (error) {
    return Response.json({ error: 'Unauthorized' }, { status: 401 });
  }
}
