// Using standard Response for Next.js 15 compatibility

export async function POST(request: Request) {
  try {
    return Response.json(
      { message: 'Logout successful' },
      {
        status: 200,
        headers: {
          'Set-Cookie': `auth-token=; HttpOnly; ${process.env.NODE_ENV === 'production' ? 'Secure;' : ''} SameSite=Strict; Max-Age=0; Path=/`
        }
      }
    );
  } catch (error: any) {
    console.error('Logout error:', error);
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}
