// Using standard Response for Next.js 15 compatibility
import jwt from 'jsonwebtoken';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export async function GET(request: Request) {
  try {
    await connectDB();

    // Get token from cookie or Authorization header
    const token = request.headers.get('cookie')?.split('auth-token=')[1]?.split(';')[0] ||
                  request.headers.get('authorization')?.replace('Bearer ', '');

    if (!token) {
      return Response.json({ error: 'No token provided' }, { status: 401 });
    }

    // Verify token
    const decoded = jwt.verify(token, JWT_SECRET) as any;

    // Find user
    const user = await User.findById(decoded.userId);
    if (!user || !user.isActive) {
      return Response.json({ error: 'User not found or inactive' }, { status: 401 });
    }

    return Response.json({
      user: {
        id: user._id,
        email: user.email,
        name: user.name,
        role: user.role,
        isActive: user.isActive,
        createdAt: user.createdAt,
      }
    });
  } catch (error: any) {
    console.error('Auth verification error:', error);

    if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      return Response.json({ error: 'Invalid or expired token' }, { status: 401 });
    }

    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}
