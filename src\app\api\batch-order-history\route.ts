import connectDB from '@/lib/mongodb';
import { verifyAuth } from '@/middleware/auth';
import Batch<PERSON>rderHistory from '@/models/BatchOrderHistory';

export async function GET(request: Request) {
  try {
    await connectDB();
    
    const user = await verifyAuth(request);
    if (!user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const batchId = searchParams.get('batchId');
    const symbol = searchParams.get('symbol');

    // Build query
    const query: any = { owner: user.userId };
    if (batchId) query.batchId = batchId;
    if (symbol) query.symbol = { $regex: symbol, $options: 'i' };

    // Get total count
    const total = await BatchOrderHistory.countDocuments(query);

    // Get paginated results
    const history = await BatchOrderHistory.find(query)
      .sort({ executedAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit);

    return Response.json({
      success: true,
      history: history.map(record => ({
        id: record._id,
        batchId: record.batchId,
        batchName: record.batchName,
        symbol: record.symbol,
        orderType: record.orderType,
        quantity: record.quantity,
        price: record.price,
        productType: record.productType,
        validity: record.validity,
        variety: record.variety,
        exchange: record.exchange,
        totalAccounts: record.totalAccounts,
        successfulOrders: record.successfulOrders,
        failedOrders: record.failedOrders,
        results: record.results,
        executedAt: record.executedAt,
        createdAt: record.createdAt
      })),
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching batch order history:', error);
    return Response.json({ 
      error: 'Failed to fetch batch order history',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    await connectDB();
    
    const user = await verifyAuth(request);
    if (!user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const historyData = await request.json();

    // Validate required fields
    const requiredFields = [
      'batchId', 'batchName', 'symbol', 'orderType', 'quantity', 'price',
      'productType', 'validity', 'variety', 'exchange', 'totalAccounts',
      'successfulOrders', 'failedOrders', 'results', 'executedAt'
    ];

    for (const field of requiredFields) {
      if (historyData[field] === undefined || historyData[field] === null) {
        return Response.json({ 
          error: `Missing required field: ${field}` 
        }, { status: 400 });
      }
    }

    // Create new batch order history record
    const newHistory = new BatchOrderHistory({
      ...historyData,
      owner: user.userId
    });

    await newHistory.save();

    console.log(`📊 Saved batch order history for batch "${historyData.batchName}" - ${historyData.successfulOrders}/${historyData.totalAccounts} successful`);

    return Response.json({
      success: true,
      message: 'Batch order history saved successfully',
      historyId: newHistory._id
    });

  } catch (error) {
    console.error('Error saving batch order history:', error);
    return Response.json({ 
      error: 'Failed to save batch order history',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
