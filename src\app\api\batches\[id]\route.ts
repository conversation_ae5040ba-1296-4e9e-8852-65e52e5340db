import connectDB from '@/lib/mongodb';
import { verifyAuth } from '@/middleware/auth';
import UserBatch from '@/models/UserBatch';

export async function GET(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    await connectDB();

    const user = await verifyAuth(request);
    if (!user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;
    const batch = await UserBatch.findOne({ _id: id, owner: user.userId });
    
    if (!batch) {
      return Response.json({ error: 'Batch not found' }, { status: 404 });
    }

    return Response.json({
      success: true,
      batch: {
        id: batch._id,
        name: batch.name,
        description: batch.description,
        accounts: batch.accounts,
        createdAt: batch.createdAt,
        isActive: batch.isActive
      }
    });

  } catch (error) {
    console.error('Error fetching batch:', error);
    return Response.json({ 
      error: 'Failed to fetch batch',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function DELETE(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    await connectDB();

    const user = await verifyAuth(request);
    if (!user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;
    const batch = await UserBatch.findOne({ _id: id, owner: user.userId });
    
    if (!batch) {
      return Response.json({ error: 'Batch not found' }, { status: 404 });
    }

    await UserBatch.deleteOne({ _id: id, owner: user.userId });

    console.log(`🗑️ Deleted batch "${batch.name}" for user ${user.email}`);

    return Response.json({
      success: true,
      message: 'Batch deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting batch:', error);
    return Response.json({ 
      error: 'Failed to delete batch',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function PUT(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    await connectDB();

    const user = await verifyAuth(request);
    if (!user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { name, description, accounts, isActive } = await request.json();
    const { id } = await params;

    const batch = await UserBatch.findOne({ _id: id, owner: user.userId });
    
    if (!batch) {
      return Response.json({ error: 'Batch not found' }, { status: 404 });
    }

    // Update fields if provided
    if (name !== undefined) batch.name = name.trim();
    if (description !== undefined) batch.description = description.trim();
    if (accounts !== undefined) batch.accounts = accounts;
    if (isActive !== undefined) batch.isActive = isActive;

    await batch.save();

    console.log(`✅ Updated batch "${batch.name}" for user ${user.email}`);

    return Response.json({
      success: true,
      message: 'Batch updated successfully',
      batch: {
        id: batch._id,
        name: batch.name,
        description: batch.description,
        accounts: batch.accounts,
        createdAt: batch.createdAt,
        isActive: batch.isActive
      }
    });

  } catch (error) {
    console.error('Error updating batch:', error);
    return Response.json({ 
      error: 'Failed to update batch',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
