import connectDB from '@/lib/mongodb';
import { verifyAuth } from '@/middleware/auth';
import UserBatch from '@/models/UserBatch';

export async function GET(request: Request) {
  try {
    await connectDB();
    
    const user = await verifyAuth(request);
    if (!user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get all batches for the current user
    const batches = await UserBatch.find({ owner: user.userId })
      .sort({ createdAt: -1 });

    return Response.json({
      success: true,
      batches: batches.map(batch => ({
        id: batch._id,
        name: batch.name,
        description: batch.description,
        accounts: batch.accounts,
        createdAt: batch.createdAt,
        isActive: batch.isActive
      }))
    });

  } catch (error) {
    console.error('Error fetching batches:', error);
    return Response.json({ 
      error: 'Failed to fetch batches',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    await connectDB();
    
    const user = await verifyAuth(request);
    if (!user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { name, description, accounts } = await request.json();

    // Validate input
    if (!name || !name.trim()) {
      return Response.json({ error: 'Batch name is required' }, { status: 400 });
    }

    if (!accounts || !Array.isArray(accounts) || accounts.length === 0) {
      return Response.json({ error: 'At least one account is required' }, { status: 400 });
    }

    // Check if batch name already exists for this user
    const existingBatch = await UserBatch.findOne({ 
      owner: user.userId, 
      name: name.trim() 
    });

    if (existingBatch) {
      return Response.json({ error: 'Batch name already exists' }, { status: 400 });
    }

    // Create new batch
    const newBatch = new UserBatch({
      name: name.trim(),
      description: description?.trim() || '',
      accounts: accounts,
      owner: user.userId,
      isActive: true
    });

    await newBatch.save();

    console.log(`✅ Created batch "${name}" with ${accounts.length} accounts for user ${user.email}`);

    return Response.json({
      success: true,
      message: 'Batch created successfully',
      batch: {
        id: newBatch._id,
        name: newBatch.name,
        description: newBatch.description,
        accounts: newBatch.accounts,
        createdAt: newBatch.createdAt,
        isActive: newBatch.isActive
      }
    });

  } catch (error) {
    console.error('Error creating batch:', error);
    return Response.json({ 
      error: 'Failed to create batch',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function PUT(request: Request) {
  try {
    await connectDB();
    
    const user = await verifyAuth(request);
    if (!user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id, name, description, accounts, isActive } = await request.json();

    if (!id) {
      return Response.json({ error: 'Batch ID is required' }, { status: 400 });
    }

    // Find and update batch
    const batch = await UserBatch.findOne({ _id: id, owner: user.userId });
    
    if (!batch) {
      return Response.json({ error: 'Batch not found' }, { status: 404 });
    }

    // Update fields if provided
    if (name !== undefined) batch.name = name.trim();
    if (description !== undefined) batch.description = description.trim();
    if (accounts !== undefined) batch.accounts = accounts;
    if (isActive !== undefined) batch.isActive = isActive;

    await batch.save();

    console.log(`✅ Updated batch "${batch.name}" for user ${user.email}`);

    return Response.json({
      success: true,
      message: 'Batch updated successfully',
      batch: {
        id: batch._id,
        name: batch.name,
        description: batch.description,
        accounts: batch.accounts,
        createdAt: batch.createdAt,
        isActive: batch.isActive
      }
    });

  } catch (error) {
    console.error('Error updating batch:', error);
    return Response.json({ 
      error: 'Failed to update batch',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
