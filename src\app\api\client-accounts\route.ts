
import connectDB from '@/lib/mongodb';
import Angel<PERSON><PERSON> from '@/models/AngelUser';
import MotilalU<PERSON> from '@/models/MotilalUser';
import { verifyAuth, getUsersUnderAdmin } from '@/middleware/auth';

export async function GET(request: Request) {
  try {
    await connectDB();

    // Verify authentication
    const user = await verifyAuth(request);
    if (!user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log(`🔄 Fetching client accounts for user: ${user.email} (${user.role})`);

    let angelAccounts = [];
    let motilalAccounts = [];

    if (user.role === 'super_admin') {
      // Super admin can see all accounts
      angelAccounts = await AngelUser.find({})
        .populate('owner', 'name email adminId')
        .select('-password -totpKey')
        .sort({ createdAt: -1 });

      motilalAccounts = await MotilalUser.find({})
        .populate('owner', 'name email adminId')
        .select('-password -totpKey')
        .sort({ createdAt: -1 });

    } else if (user.role === 'admin') {
      // Admin can see accounts of users under their management AND their own accounts
      const managedUsers = await getUsersUnderAdmin(user.userId);
      const managedUserIds = managedUsers.map(u => u._id);
      const allOwnerIds = [...managedUserIds, user.userId];

      angelAccounts = await AngelUser.find({ owner: { $in: allOwnerIds } })
        .populate('owner', 'name email userCode')
        .select('-password -totpKey')
        .sort({ createdAt: -1 });

      motilalAccounts = await MotilalUser.find({ owner: { $in: allOwnerIds } })
        .populate('owner', 'name email userCode')
        .select('-password -totpKey')
        .sort({ createdAt: -1 });

    } else {
      // Regular user can only see their own accounts
      angelAccounts = await AngelUser.find({ owner: user.userId })
        .select('-password -totpKey')
        .sort({ createdAt: -1 });

      motilalAccounts = await MotilalUser.find({ owner: user.userId })
        .select('-password -totpKey')
        .sort({ createdAt: -1 });
    }

    // Format accounts for client consumption
    const formattedAccounts = [
      ...angelAccounts.map((acc: any) => ({
        id: acc._id.toString(),
        clientCode: acc.userId,
        clientName: acc.clientName,
        broker: 'angel',
        isActive: acc.state === 'live' || acc.status === 'active',
        owner: acc.owner
      })),
      ...motilalAccounts.map((acc: any) => ({
        id: acc._id.toString(),
        clientCode: acc.userId,
        clientName: acc.clientName,
        broker: 'motilal',
        isActive: acc.state === 'live' || acc.status === 'active',
        owner: acc.owner
      }))
    ];

    console.log(`✅ Found ${formattedAccounts.length} client accounts (${angelAccounts.length} Angel, ${motilalAccounts.length} Motilal)`);

    return Response.json({
      success: true,
      accounts: formattedAccounts,
      angelAccounts: formattedAccounts.filter(acc => acc.broker === 'angel'),
      motilalAccounts: formattedAccounts.filter(acc => acc.broker === 'motilal'),
      count: {
        total: formattedAccounts.length,
        angel: angelAccounts.length,
        motilal: motilalAccounts.length
      }
    });
  } catch (error) {
    console.error('💥 Error fetching client accounts:', error);
    return Response.json(
      { success: false, error: 'Failed to fetch client accounts' },
      { status: 500 }
    );
  }
}