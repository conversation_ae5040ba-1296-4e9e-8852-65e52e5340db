
import connectDB from '@/lib/mongodb';

// Mock models - replace with actual models
const mockAngelAccounts = [
  { clientCode: 'ANG001', userId: 'user1', isActive: true },
  { clientCode: 'ANG002', userId: 'user2', isActive: true },
];

const mockMotilalAccounts = [
  { clientCode: 'MOT001', userId: 'user3', isActive: true },
  { clientCode: 'MOT002', userId: 'user4', isActive: true },
];

const mockDhanAccounts = [
  { clientCode: 'DHN001', userId: 'user5', isActive: true },
  { clientCode: 'DHN002', userId: 'user6', isActive: true },
];

export async function GET(request: Request) {
  try {
    await connectDB();

    // In production, replace with actual database queries
    const angelAccounts = mockAngelAccounts;
    const motilalAccounts = mockMotilalAccounts;
    const dhanAccounts = mockDhanAccounts;
    const otherAccounts: any[] = [];

    return Response.json({
      success: true,
      angelAccounts,
      motilalAccounts,
      dhanAccounts,
      otherAccounts
    });
  } catch (error) {
    console.error('Error fetching client accounts:', error);
    return Response.json(
      { success: false, error: 'Failed to fetch client accounts' },
      { status: 500 }
    );
  }
}