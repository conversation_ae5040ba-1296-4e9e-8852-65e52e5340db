import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import bcrypt from 'bcryptjs';

export async function POST(request: Request, { params }: { params: Promise<{ token: string }> }) {
  try {
    await connectDB();

    const { token } = await params;
    const { password } = await request.json();
    
    if (!token) {
      return Response.json({ error: 'Token is required' }, { status: 400 });
    }

    if (!password || password.length < 6) {
      return Response.json({ error: 'Password must be at least 6 characters long' }, { status: 400 });
    }

    // Find user with this invitation token
    const user = await User.findOne({ 
      invitationToken: token,
      role: 'admin'
    });

    if (!user) {
      return Response.json({ error: 'Invalid invitation token' }, { status: 404 });
    }

    // Check if invitation is expired
    if (user.invitationExpiry && new Date() > user.invitationExpiry) {
      return Response.json({ error: 'Invitation has expired' }, { status: 400 });
    }

    // Check if invitation is already accepted
    if (user.isInvitationAccepted) {
      return Response.json({ error: 'Invitation has already been accepted' }, { status: 400 });
    }

    // Hash the new password
    const salt = await bcrypt.genSalt(12);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Update user with new password and mark invitation as accepted
    await User.findByIdAndUpdate(user._id, {
      $set: {
        password: hashedPassword,
        isInvitationAccepted: true,
        isActive: true
      },
      $unset: {
        invitationToken: 1,
        invitationExpiry: 1
      }
    });

    console.log(`✅ Admin invitation accepted: ${user.email}`);

    return Response.json({
      message: 'Invitation accepted successfully',
      user: {
        email: user.email,
        name: user.name,
        role: user.role
      }
    });

  } catch (error: any) {
    console.error('Error accepting invitation:', error);
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}
