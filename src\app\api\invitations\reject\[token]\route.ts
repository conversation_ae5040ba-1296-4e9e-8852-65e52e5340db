import connectDB from '@/lib/mongodb';
import User from '@/models/User';

export async function POST(request: Request, { params }: { params: Promise<{ token: string }> }) {
  try {
    await connectDB();

    const { token } = await params;
    
    if (!token) {
      return Response.json({ error: 'Token is required' }, { status: 400 });
    }

    // Find user with this invitation token
    const user = await User.findOne({ 
      invitationToken: token,
      role: 'admin'
    });

    if (!user) {
      return Response.json({ error: 'Invalid invitation token' }, { status: 404 });
    }

    // Check if invitation is already accepted
    if (user.isInvitationAccepted) {
      return Response.json({ error: 'Invitation has already been accepted' }, { status: 400 });
    }

    // Delete the user record (since they rejected the invitation)
    await User.findByIdAndDelete(user._id);

    console.log(`❌ Admin invitation rejected and user deleted: ${user.email}`);

    return Response.json({
      message: 'Invitation rejected successfully'
    });

  } catch (error: any) {
    console.error('Error rejecting invitation:', error);
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}
