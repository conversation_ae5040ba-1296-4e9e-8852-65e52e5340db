import connectDB from '@/lib/mongodb';
import User from '@/models/User';

export async function GET(request: Request, { params }: { params: Promise<{ token: string }> }) {
  try {
    await connectDB();

    const { token } = await params;
    
    if (!token) {
      return Response.json({ error: 'Token is required' }, { status: 400 });
    }

    // Find user with this invitation token
    const user = await User.findOne({ 
      invitationToken: token,
      role: 'admin'
    }).select('-password');

    if (!user) {
      return Response.json({ error: 'Invalid invitation token' }, { status: 404 });
    }

    // Check if invitation is expired
    const isExpired = user.invitationExpiry && new Date() > user.invitationExpiry;
    
    // Check if invitation is already accepted
    const isAccepted = user.isInvitationAccepted;

    return Response.json({
      invitation: {
        email: user.email,
        name: user.name,
        companyName: user.companyName,
        orderRate: user.orderRate,
        invitedBy: 'Super Administrator', // You can make this dynamic
        isExpired,
        isAccepted
      }
    });

  } catch (error: any) {
    console.error('Error verifying invitation:', error);
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}
