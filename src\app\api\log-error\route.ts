// Error logging API endpoint
import connectDB from '@/lib/mongodb';
import { withRateLimit, rateLimitConfigs } from '@/middleware/rateLimit';

// Error log model (simple schema)
interface ErrorLog {
  message: string;
  stack?: string;
  componentStack?: string;
  timestamp: string;
  userAgent: string;
  url: string;
  additionalInfo?: any;
  severity: 'low' | 'medium' | 'high' | 'critical';
  resolved: boolean;
  createdAt: Date;
}

// In-memory error storage for development (in production, use a proper database)
const errorLogs: ErrorLog[] = [];
const MAX_LOGS = 1000; // Keep only last 1000 errors

async function handleErrorLog(request: Request) {
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.message || !body.timestamp) {
      return Response.json(
        { error: 'Missing required fields: message, timestamp' },
        { status: 400 }
      );
    }

    // Determine error severity based on message content
    const severity = determineSeverity(body.message, body.stack);

    // Create error log entry
    const errorLog: ErrorLog = {
      message: body.message,
      stack: body.stack,
      componentStack: body.componentStack,
      timestamp: body.timestamp,
      userAgent: body.userAgent || 'unknown',
      url: body.url || 'unknown',
      additionalInfo: body.additionalInfo,
      severity,
      resolved: false,
      createdAt: new Date()
    };

    // Store error log
    errorLogs.unshift(errorLog); // Add to beginning
    
    // Keep only the most recent logs
    if (errorLogs.length > MAX_LOGS) {
      errorLogs.splice(MAX_LOGS);
    }

    // Log to console for immediate visibility
    console.error('🚨 Client Error Logged:', {
      message: errorLog.message,
      severity: errorLog.severity,
      url: errorLog.url,
      timestamp: errorLog.timestamp
    });

    // In production, you might want to:
    // 1. Store in database
    // 2. Send to external logging service (Sentry, LogRocket, etc.)
    // 3. Send alerts for critical errors
    
    if (severity === 'critical') {
      console.error('🔥 CRITICAL ERROR DETECTED:', errorLog);
      // Send alert notification here
    }

    return Response.json({ 
      success: true, 
      message: 'Error logged successfully',
      severity: errorLog.severity
    });

  } catch (error) {
    console.error('Error in error logging endpoint:', error);
    return Response.json(
      { error: 'Failed to log error' },
      { status: 500 }
    );
  }
}

function determineSeverity(message: string, stack?: string): 'low' | 'medium' | 'high' | 'critical' {
  const lowerMessage = message.toLowerCase();
  const lowerStack = stack?.toLowerCase() || '';

  // Critical errors
  if (
    lowerMessage.includes('network error') ||
    lowerMessage.includes('failed to fetch') ||
    lowerMessage.includes('authentication') ||
    lowerMessage.includes('unauthorized') ||
    lowerMessage.includes('payment') ||
    lowerMessage.includes('order') ||
    lowerStack.includes('trading') ||
    lowerStack.includes('order')
  ) {
    return 'critical';
  }

  // High severity errors
  if (
    lowerMessage.includes('cannot read') ||
    lowerMessage.includes('undefined') ||
    lowerMessage.includes('null') ||
    lowerMessage.includes('reference error') ||
    lowerMessage.includes('type error')
  ) {
    return 'high';
  }

  // Medium severity errors
  if (
    lowerMessage.includes('warning') ||
    lowerMessage.includes('deprecated') ||
    lowerMessage.includes('validation')
  ) {
    return 'medium';
  }

  // Default to low severity
  return 'low';
}

// GET endpoint to retrieve error logs (for admin dashboard)
async function handleGetErrorLogs(request: Request) {
  try {
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit') || '50');
    const severity = url.searchParams.get('severity') as 'low' | 'medium' | 'high' | 'critical' | null;
    const resolved = url.searchParams.get('resolved') === 'true';

    let filteredLogs = errorLogs;

    // Filter by severity if specified
    if (severity) {
      filteredLogs = filteredLogs.filter(log => log.severity === severity);
    }

    // Filter by resolved status
    filteredLogs = filteredLogs.filter(log => log.resolved === resolved);

    // Limit results
    const logs = filteredLogs.slice(0, limit);

    return Response.json({
      logs,
      total: filteredLogs.length,
      summary: {
        critical: errorLogs.filter(log => log.severity === 'critical' && !log.resolved).length,
        high: errorLogs.filter(log => log.severity === 'high' && !log.resolved).length,
        medium: errorLogs.filter(log => log.severity === 'medium' && !log.resolved).length,
        low: errorLogs.filter(log => log.severity === 'low' && !log.resolved).length,
        total: errorLogs.length
      }
    });

  } catch (error) {
    console.error('Error retrieving error logs:', error);
    return Response.json(
      { error: 'Failed to retrieve error logs' },
      { status: 500 }
    );
  }
}

// Apply rate limiting to prevent abuse
export const POST = withRateLimit(handleErrorLog, {
  maxRequests: 50, // Allow 50 error reports per 15 minutes per client
  windowMs: 15 * 60 * 1000
});

export const GET = withRateLimit(handleGetErrorLogs, rateLimitConfigs.admin);
