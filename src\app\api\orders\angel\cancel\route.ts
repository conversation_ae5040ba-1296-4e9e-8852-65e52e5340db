import connectDB from '@/lib/mongodb';
import Angel<PERSON><PERSON> from '@/models/AngelUser';
import { OrderResponse } from '@/models/OrderModel';
import { verifyAuth } from '@/middleware/auth';

export async function POST(request: Request) {
  try {
    await connectDB();
    
    // Verify authentication (allow for testing without auth)
    await verifyAuth(request);
    // Authentication is handled in middleware/auth.ts
    // We're allowing requests without authentication for testing

    const { orderId, variety = 'NORMAL' } = await request.json();

    if (!orderId) {
      return Response.json({ error: 'Order ID is required' }, { status: 400 });
    }

    // Find the order in our database to get client information
    const orderRecord = await OrderResponse.findOne({
      'details.orderid': orderId,
      broker: 'angel'
    });

    if (!orderRecord) {
      return Response.json({ error: 'Order not found in database' }, { status: 404 });
    }

    // Get the client information
    const client = await AngelUser.findOne({ userId: orderRecord.clientId });

    if (!client || !client.jwtToken) {
      return Response.json({ error: 'Client not found or not logged in' }, { status: 404 });
    }

    // Prepare cancellation data for Angel API
    const cancelData = {
      variety: variety,
      orderid: orderId
    };

    console.log('🔄 Angel Cancel Order Request:', {
      orderId,
      variety,
      clientId: client.userId,
      cancelData
    });

    // Make API call to Angel Broking
    const response = await fetch('https://apiconnect.angelone.in/rest/secure/angelbroking/order/v1/cancelOrder', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${client.jwtToken}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-UserType': 'USER',
        'X-SourceID': 'WEB',
        'X-ClientLocalIP': '***********',
        'X-ClientPublicIP': '***********',
        'X-MACAddress': '00:00:00:00:00:00',
        'X-PrivateKey': client.apiKey,
      },
      body: JSON.stringify(cancelData)
    });

    const result = await response.json();

    console.log('📡 Angel Cancel Order Response:', {
      status: response.status,
      statusText: response.statusText,
      result: result
    });

    // Save cancellation response to database
    const cancelOrderResponse = new OrderResponse({
      clientId: client.userId,
      orderType: 'CANCEL',
      strategyName: 'Manual Cancellation',
      details: {
        status: result.status === 'success',
        message: result.message || 'Order cancellation attempted',
        script: orderRecord.details.script,
        orderid: orderId,
        uniqueorderid: result.data?.uniqueorderid || '',
        response: result,
        apiKey: client.apiKey,
        jwtToken: client.jwtToken,
      },
      broker: 'angel',
      symboltoken: orderRecord.symboltoken,
    });

    await cancelOrderResponse.save();

    // Update original order record if cancellation was successful
    if (result.status === 'success') {
      await OrderResponse.updateOne(
        { 'details.orderid': orderId },
        {
          $set: {
            'details.cancelled': true,
            'details.cancelledAt': new Date()
          }
        }
      );
      console.log('✅ Order marked as cancelled in database');
    } else {
      console.log('❌ Order cancellation failed:', result.message);
    }

    const responseData = {
      success: result.status === 'success',
      message: result.message || 'Cancellation request processed',
      orderId: orderId,
      response: result
    };

    console.log('📤 Sending response to client:', responseData);

    return Response.json(responseData);

  } catch (error) {
    console.error('Error in Angel order cancellation:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return Response.json({
      error: 'Internal server error',
      message: errorMessage
    }, { status: 500 });
  }
}
