export async function POST(request: Request) {
  const startTime = Date.now();

  try {
    const orderData = await request.json();
    const { clientCode, orderType, quantity, price, productType, validity, symbolToken, tradingSymbol } = orderData;

    console.log('📥 Bulk Order Request:', {
      clientCode,
      orderType,
      quantity,
      price,
      productType,
      validity,
      symbolToken,
      tradingSymbol
    });

    const results = [];

    // Handle bulk orders for all accounts
    if (clientCode === 'all-accounts') {
      console.log('🏦 Processing bulk order for ALL ACCOUNTS (Angel + Motilal)');
      
      // Place orders on Angel
      try {
        console.log('🔥 Placing orders on Angel accounts...');
        const url = new URL(request.url);
        const angelResponse = await fetch(`${url.origin}/api/orders/angel`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': request.headers.get('authorization') || '',
            'Cookie': request.headers.get('cookie') || ''
          },
          body: JSON.stringify({
            ...orderData,
            clientCode: 'all-angel'
          })
        });

        const angelResult = await angelResponse.json();
        results.push({
          broker: 'angel',
          success: angelResponse.ok,
          data: angelResult
        });
        console.log(`🔥 Angel orders result: ${angelResponse.ok ? 'SUCCESS' : 'FAILED'}`);
      } catch (error) {
        console.error('❌ Error placing Angel orders:', error);
        results.push({
          broker: 'angel',
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }

      // Place orders on Motilal
      try {
        console.log('💼 Placing orders on Motilal accounts...');
        const url = new URL(request.url);
        const motilalResponse = await fetch(`${url.origin}/api/orders/motilal`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': request.headers.get('authorization') || '',
            'Cookie': request.headers.get('cookie') || ''
          },
          body: JSON.stringify({
            ...orderData,
            clientCode: 'all-motilal'
          })
        });

        const motilalResult = await motilalResponse.json();
        results.push({
          broker: 'motilal',
          success: motilalResponse.ok,
          data: motilalResult
        });
        console.log(`💼 Motilal orders result: ${motilalResponse.ok ? 'SUCCESS' : 'FAILED'}`);
      } catch (error) {
        console.error('❌ Error placing Motilal orders:', error);
        results.push({
          broker: 'motilal',
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }

      // Calculate summary
      const totalSuccessful = results.filter(r => r.success).length;
      const totalBrokers = results.length;
      
      return Response.json({
        success: totalSuccessful > 0,
        message: `Bulk order processed across ${totalBrokers} brokers (${totalSuccessful} successful)`,
        results: results,
        summary: {
          totalBrokers,
          successfulBrokers: totalSuccessful,
          failedBrokers: totalBrokers - totalSuccessful
        },
        responseTime: `${Date.now() - startTime}ms`
      });

    } else if (clientCode === 'all-angel') {
      // Redirect to Angel API
      console.log('🔥 Redirecting to Angel API for all Angel accounts');
      const url = new URL(request.url);
      const angelResponse = await fetch(`${url.origin}/api/orders/angel`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': request.headers.get('authorization') || '',
          'Cookie': request.headers.get('cookie') || ''
        },
        body: JSON.stringify(orderData)
      });

      const angelResult = await angelResponse.json();
      return Response.json(angelResult, { status: angelResponse.status });

    } else if (clientCode === 'all-motilal') {
      // Redirect to Motilal API
      console.log('💼 Redirecting to Motilal API for all Motilal accounts');
      const url2 = new URL(request.url);
      const motilalResponse = await fetch(`${url2.origin}/api/orders/motilal`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': request.headers.get('authorization') || '',
          'Cookie': request.headers.get('cookie') || ''
        },
        body: JSON.stringify(orderData)
      });

      const motilalResult = await motilalResponse.json();
      return Response.json(motilalResult, { status: motilalResponse.status });

    } else {
      return Response.json({
        error: 'Invalid bulk client code',
        message: 'Supported bulk codes: all-accounts, all-angel, all-motilal',
        clientCode
      }, { status: 400 });
    }

  } catch (error) {
    console.error('Error in bulk order placement:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return Response.json({
      error: 'Internal server error',
      message: errorMessage,
      responseTime: `${Date.now() - startTime}ms`
    }, { status: 500 });
  }
}

export async function GET() {
  return Response.json({
    message: 'Bulk Order API',
    supportedClientCodes: [
      'all-accounts',
      'all-angel', 
      'all-motilal'
    ]
  });
}
