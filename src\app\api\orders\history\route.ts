import connectDB from '@/lib/mongodb';
import { OrderResponse } from '@/models/OrderModel';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// Middleware to verify authentication
async function verifyAuth(request: Request) {
  const authHeader = request.headers.get('authorization');
  const token = authHeader?.replace('Bearer ', '');
  
  if (!token) {
    return null;
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET) as any;
    return decoded;
  } catch (error) {
    console.error('JWT verification failed:', error);
    return null;
  }
}

export async function GET(request: Request) {
  try {
    await connectDB();
    
    // Skip authentication for testing purposes
    console.log('⚠️ Authentication disabled for testing purposes');
    // const user = await verifyAuth(request);
    // if (!user) {
    //   return Response.json({ error: 'Unauthorized' }, { status: 401 });
    // }

    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit') || '100');
    const offset = parseInt(url.searchParams.get('offset') || '0');
    const broker = url.searchParams.get('broker');
    const status = url.searchParams.get('status');
    const clientId = url.searchParams.get('clientId');

    // Build query
    const query: any = {};
    
    if (broker) {
      query.broker = broker;
    }
    
    if (status) {
      query['details.status'] = status === 'success';
    }
    
    if (clientId) {
      query.clientId = clientId;
    }

    // Fetch orders with pagination
    const orders = await OrderResponse.find(query)
      .sort({ createdAt: -1 })
      .limit(limit)
      .skip(offset)
      .lean();

    // Get total count for pagination
    const totalCount = await OrderResponse.countDocuments(query);

    // Calculate summary statistics
    const summary = await OrderResponse.aggregate([
      { $match: query },
      {
        $group: {
          _id: null,
          totalOrders: { $sum: 1 },
          successfulOrders: {
            $sum: { $cond: [{ $eq: ['$details.status', true] }, 1, 0] }
          },
          failedOrders: {
            $sum: { $cond: [{ $eq: ['$details.status', false] }, 1, 0] }
          },
          angelOrders: {
            $sum: { $cond: [{ $eq: ['$broker', 'angel'] }, 1, 0] }
          },
          motilalOrders: {
            $sum: { $cond: [{ $eq: ['$broker', 'motilal'] }, 1, 0] }
          }
        }
      }
    ]);

    return Response.json({
      success: true,
      orders,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + limit < totalCount
      },
      summary: summary[0] || {
        totalOrders: 0,
        successfulOrders: 0,
        failedOrders: 0,
        angelOrders: 0,
        motilalOrders: 0
      }
    });

  } catch (error) {
    console.error('Error fetching order history:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return Response.json({
      error: 'Internal server error',
      message: errorMessage
    }, { status: 500 });
  }
}

export async function DELETE(request: Request) {
  try {
    await connectDB();
    
    // Verify authentication
    const user = await verifyAuth(request);
    if (!user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { orderId } = await request.json();

    if (!orderId) {
      return Response.json({ error: 'Order ID is required' }, { status: 400 });
    }

    const result = await OrderResponse.findByIdAndDelete(orderId);

    if (!result) {
      return Response.json({ error: 'Order not found' }, { status: 404 });
    }

    return Response.json({
      success: true,
      message: 'Order deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting order:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return Response.json({
      error: 'Internal server error',
      message: errorMessage
    }, { status: 500 });
  }
}
