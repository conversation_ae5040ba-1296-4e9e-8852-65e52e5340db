import connectDB from '@/lib/mongodb';
import Motil<PERSON><PERSON><PERSON> from '@/models/MotilalUser';
import { OrderResponse } from '@/models/OrderModel';
import { verifyAuth } from '@/middleware/auth';

export async function POST(request: Request) {
  try {
    await connectDB();
    
    // Verify authentication (allow for testing without auth)
    await verifyAuth(request);
    // Authentication is handled in middleware/auth.ts
    // We're allowing requests without authentication for testing

    const { orderId } = await request.json();

    if (!orderId) {
      return Response.json({ error: 'Order ID is required' }, { status: 400 });
    }

    // Find the order in our database to get client information
    const orderRecord = await OrderResponse.findOne({
      'details.orderid': orderId,
      broker: 'motilal'
    });

    if (!orderRecord) {
      return Response.json({ error: 'Order not found in database' }, { status: 404 });
    }

    // Get the client information
    const client = await MotilalUser.findOne({ userId: orderRecord.clientId });

    if (!client || !client.authToken) {
      return Response.json({ error: 'Client not found or not logged in' }, { status: 404 });
    }

    // Prepare cancellation data for Motilal API
    const cancelData = {
      orderid: orderId
    };

    console.log('🔄 Motilal Cancel Order Request:', {
      orderId,
      clientId: client.userId,
      cancelData
    });

    // Make API call to Motilal Oswal with proper headers
    const response = await fetch('https://openapi.motilaloswal.com/rest/trans/v1/cancelorder', {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'MOSL/V.1.1.0',
        'Content-Type': 'application/json',
        'Authorization': client.authToken,
        'ApiKey': client.apiKey,
        'ClientLocalIp': '***********',
        'ClientPublicIp': '***********',
        'MacAddress': '00:00:00:00:00:00',
        'SourceId': 'WEB',
        'vendorinfo': client.userId,
        'osname': 'Windows 10',
        'osversion': '10.0.19041',
        'devicemodel': 'AHV',
        'manufacturer': 'DELL',
        'productname': 'Trading App',
        'productversion': '1.0.0',
        'installedappid': 'TradingApp',
        'browsername': 'Chrome',
        'browserversion': '105.0',
      },
      body: JSON.stringify(cancelData)
    });

    const result = await response.json();

    console.log('📡 Motilal Cancel Order Response:', {
      status: response.status,
      statusText: response.statusText,
      result: result
    });

    // Save cancellation response to database
    const cancelOrderResponse = new OrderResponse({
      clientId: client.userId,
      orderType: 'CANCEL',
      strategyName: 'Manual Cancellation',
      details: {
        status: result.status === 'success',
        message: result.message || 'Order cancellation attempted',
        script: orderRecord.details.script,
        orderid: orderId,
        uniqueorderid: result.data?.uniqueorderid || '',
        response: result,
        apiKey: client.apiKey,
        jwtToken: client.authToken,
      },
      broker: 'motilal',
      symboltoken: orderRecord.symboltoken,
    });

    await cancelOrderResponse.save();

    // Update original order record if cancellation was successful
    if (result.status === 'success') {
      await OrderResponse.updateOne(
        { 'details.orderid': orderId },
        { 
          $set: { 
            'details.cancelled': true,
            'details.cancelledAt': new Date()
          }
        }
      );
    }

    return Response.json({
      success: result.status === 'success',
      message: result.message || 'Cancellation request processed',
      orderId: orderId,
      response: result
    });

  } catch (error) {
    console.error('Error in Motilal order cancellation:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return Response.json({
      error: 'Internal server error',
      message: errorMessage
    }, { status: 500 });
  }
}
