import connectDB from '@/lib/mongodb';
import MotilalUser from '@/models/MotilalUser';
import User from '@/models/User';
import { OrderResponse } from '@/models/OrderModel';
import { BillingRecord } from '@/models/Billing';
import { findSymbolInAngelDB, findSymbolBySecurityId } from '@/lib/symbolUtils';
import { getCurrentBillingCycle } from '@/middleware/auth';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// Middleware to verify authentication
async function verifyAuth(request: Request) {
  // Try to get token from Authorization header first
  const authHeader = request.headers.get('authorization');
  let token = authHeader?.replace('Bearer ', '');

  // If no auth header, try to get from cookies
  if (!token) {
    const cookieHeader = request.headers.get('cookie');
    if (cookieHeader) {
      const cookies = cookieHeader.split(';').reduce((acc, cookie) => {
        const [key, value] = cookie.trim().split('=');
        acc[key] = value;
        return acc;
      }, {} as Record<string, string>);
      token = cookies.token;
    }
  }

  if (!token) {
    console.log('⚠️ No JWT token provided - proceeding without authentication for testing');
    return { userId: 'test-user', role: 'user', hasAccess: true };
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET) as any;
    console.log('✅ JWT token verified successfully');
    return { ...decoded, hasAccess: true };
  } catch (error) {
    console.error('JWT verification failed:', error);
    console.log('⚠️ Proceeding without authentication for testing purposes');
    return { userId: 'test-user', role: 'user', hasAccess: true };
  }
}

export async function POST(request: Request) {
  try {
    console.log('📥 Motilal order placement request received');
    await connectDB();

    // Verify authentication to ensure admin isolation
    const authUser = await verifyAuth(request);
    console.log('👤 Authentication result:', authUser ? 'Success' : 'Failed (proceeding anyway)');

    const orderData = await request.json();
    const { clientCode, orderType, quantity, price, productType, validity, symbolToken, tradingSymbol } = orderData;

    console.log('📥 Motilal Order Request:', {
      clientCode,
      orderType,
      quantity,
      price,
      productType,
      validity,
      symbolToken,
      tradingSymbol
    });

    // Get symbol data from Angel_api database using same approach as epicrisenew
    let symbolData = null;

    if (tradingSymbol) {
      // Use trading symbol name to find the -EQ symbol (same as epicrisenew approach)
      console.log(`🔍 Looking up symbol using trading symbol name: ${tradingSymbol}`);
      symbolData = await findSymbolInAngelDB(tradingSymbol);
    } else if (symbolToken) {
      // Fallback: try security ID lookup if no trading symbol
      console.log(`🔍 Fallback: Looking up using security ID: ${symbolToken}`);
      symbolData = await findSymbolBySecurityId(symbolToken);
    }

    if (!symbolData) {
      console.error('❌ Symbol not found in Angel_api database');
      return Response.json({
        error: 'Symbol not found in Angel_api database',
        symbolToken,
        tradingSymbol,
        message: 'Please provide a valid security ID (symbolToken) or trading symbol'
      }, { status: 400 });
    }

    console.log('✅ Symbol data found:', {
      securityId: symbolData.token,
      tradingSymbol: symbolData.symbol,
      companyName: symbolData.name,
      exchange: symbolData.exch_seg
    });

    // Get Motilal clients - ONLY for current admin's users (ADMIN ISOLATION)
    let targetClients = [];

    // First, get the current admin's users (if admin is placing order)
    let allowedUserIds = [];
    if (authUser && authUser.role === 'admin') {
      const User = (await import('@/models/User')).default;
      const adminUsers = await User.find({
        adminId: authUser.userId,
        role: 'user'
      }).select('_id');
      allowedUserIds = adminUsers.map(u => u._id);
      console.log(`👨‍💼 Admin can access ${allowedUserIds.length} users' Motilal accounts`);
    }

    if (clientCode === 'allMo' || clientCode === 'all-motilal' || clientCode === 'all-accounts') {
      // Get all active Motilal clients for this admin's users only
      const query = { status: 'active' };

      // ADMIN ISOLATION: If admin, restrict to their users' accounts only
      if (authUser && authUser.role === 'admin' && allowedUserIds.length > 0) {
        (query as any).owner = { $in: allowedUserIds };
      }

      targetClients = await MotilalUser.find(query);
      console.log(`📋 Found ${targetClients.length} Motilal clients for bulk order (${clientCode})`);
    } else {
      // Get specific client - but only if it belongs to admin's user
      const query = {
        $or: [
          { userId: clientCode },
          { clientName: clientCode }
        ]
      };

      // ADMIN ISOLATION: If admin, restrict to their users' accounts only
      if (authUser && authUser.role === 'admin' && allowedUserIds.length > 0) {
        (query as any).owner = { $in: allowedUserIds };
      }

      const client = await MotilalUser.findOne(query);
      if (client) {
        targetClients = [client];
        console.log(`👤 Found specific Motilal client for admin's user: ${client.clientName} (${client.userId})`);
      }
    }

    if (targetClients.length === 0) {
      console.error(`❌ No valid Motilal clients found for clientCode: ${clientCode}`);
      return Response.json({ error: 'No valid clients found' }, { status: 400 });
    }

    const orderResults = [];

    // Place orders for each client
    for (const client of targetClients) {
      try {
        console.log(`🔄 Processing order for Motilal client: ${client.clientName} (${client.userId})`);

        // Check if client has valid tokens
        if (!client.authToken) {
          console.error(`❌ Client ${client.clientName} missing auth token`);
          orderResults.push({
            clientId: client.userId,
            clientName: client.clientName,
            status: false,
            message: 'Client not logged in - missing auth token',
            error: 'MISSING_AUTH_TOKEN'
          });
          continue;
        }

        // Prepare order data for Motilal API (based on epicrisenew logic)
        // Map product types correctly for Motilal Oswal
        let motilalProductType = "VALUEPLUS"; // Default for INTRADAY
        if (productType === 'DELIVERY') {
          motilalProductType = "DELIVERY";
          console.log('📦 Using DELIVERY product type for Motilal order');
        } else if (productType === 'INTRADAY') {
          motilalProductType = "VALUEPLUS";
          console.log('⚡ Using VALUEPLUS product type for Motilal intraday order');
        }

        console.log(`🔄 Product type mapping: ${productType} → ${motilalProductType}`);

        const motilalOrderData = {
          exchange: "NSE",
          symboltoken: Number(symbolData.token), // Use token as security id
          buyorsell: orderType.toUpperCase(), // BUY or SELL
          ordertype: price && price > 0 ? "LIMIT" : "MARKET", // Only LIMIT and MARKET
          producttype: motilalProductType, // Correctly mapped product type
          orderduration: "DAY",
          price: price && price > 0 ? Number(price) : 0,
          quantityinlot: Number(quantity),
          amoorder: "N"
        };

        console.log(`📤 Motilal Order Data for ${client.clientName}:`, motilalOrderData);

        // Make API call to Motilal Oswal (based on epicrisenew logic and proper headers)
        console.log(`🔄 Making API call to Motilal Oswal for client ${client.clientName} (${client.userId})`);

        const requestStartTime = Date.now();
        const response = await fetch('https://openapi.motilaloswal.com/rest/trans/v1/placeorder', {
          method: 'POST',
          headers: {
            'Accept': 'application/json',
            'User-Agent': 'MOSL/V.1.1.0',
            'Content-Type': 'application/json',
            'Authorization': client.authToken,
            'ApiKey': client.apiKey,
            'ClientLocalIp': '***********',
            'ClientPublicIp': '***********',
            'MacAddress': '00:00:00:00:00:00',
            'SourceId': 'WEB',
            'vendorinfo': client.userId,
            'osname': 'Windows 10',
            'osversion': '10.0.19041',
            'devicemodel': 'AHV',
            'manufacturer': 'DELL',
            'productname': 'Trading App',
            'productversion': '1.0.0',
            'installedappid': 'TradingApp',
            'browsername': 'Chrome',
            'browserversion': '105.0',
          },
          body: JSON.stringify(motilalOrderData)
        });
        const requestDuration = Date.now() - requestStartTime;

        console.log(`⏱️ Motilal API response time: ${requestDuration}ms for client ${client.clientName}`);
        console.log(`📊 Motilal API response status: ${response.status} ${response.statusText}`);

        // Log response headers for debugging
        const responseHeaders: Record<string, string> = {};
        response.headers.forEach((value, key) => {
          responseHeaders[key] = value;
        });
        console.log(`🔍 Motilal API response headers:`, responseHeaders);

        const result = await response.json();
        console.log(`📥 Motilal API response data for ${client.clientName}:`, result);

        // Enhanced response analysis
        const isSuccess = response.ok && (result.status === 'SUCCESS' || result.status === 'success' || result.status === true);
        const errorMessage = result.message || result.errorMessage || result.emsg || 'Unknown error';
        const orderId = result.data?.orderid || result.orderid || result.data?.oms_order_id || null;

        console.log(`📨 Motilal API Response Analysis for ${client.clientName}:`, {
          httpStatus: response.status,
          httpStatusText: response.statusText,
          apiStatus: result.status,
          success: isSuccess,
          orderId: orderId,
          errorMessage: errorMessage,
          requestDuration: `${requestDuration}ms`,
          fullResponse: result
        });

        // Save order response to database (based on epicrisenew OrderResponse model)
        const isOrderSuccess = result.status === true || result.status === 'SUCCESS' || result.status === 'success';

        // Extract order ID from Motilal response - they use uniqueorderid field
        const motilalOrderId = result.uniqueorderid || result.data?.uniqueorderid || result.orderid || result.data?.orderid || '';

        const orderResponse = new OrderResponse({
          clientId: client.userId,
          orderType: orderType.toUpperCase(),
          strategyName: 'Manual Trading',
          details: {
            status: isOrderSuccess,
            message: result.message || result.errorMessage || 'Order processed',
            script: symbolData.symbol,
            orderid: motilalOrderId, // Use the extracted Motilal order ID
            uniqueorderid: motilalOrderId, // Same for unique order ID
            response: result,
            apiKey: client.apiKey || '',
            jwtToken: client.authToken || '',
          },
          broker: 'motilal',
          symboltoken: symbolData.token,
        });

        await orderResponse.save();

        console.log(`💾 Order saved to database for ${client.clientName}:`, {
          orderId: motilalOrderId,
          status: result.status === true || result.status === 'SUCCESS' || result.status === 'success' ? 'SUCCESS' : 'FAILED'
        });

        const isSuccess = result.status === true || result.status === 'SUCCESS' || result.status === 'success';

        // Add billing tracking for successful orders
        console.log(`🔍 Checking Motilal billing for order: ${motilalOrderId}, isSuccess: ${isSuccess}`);
        if (isSuccess && motilalOrderId) {
          try {
            console.log(`🔍 Getting Motilal user for client: ${client.userId}`);
            // Get the user who owns this Motilal account
            const motilalUser = await MotilalUser.findOne({ userId: client.userId });
            console.log(`🔍 Motilal user found: ${motilalUser ? 'Yes' : 'No'}`);
            if (motilalUser && motilalUser.owner) {
              // Get the actual user by the owner ObjectId
              const user = await User.findById(motilalUser.owner);
              console.log(`🔍 User found: ${user ? user.userCode + ' - ' + user.name + ' (' + user.email + ')' : 'No'}, role: ${user?.role}, adminId: ${user?.adminId}`);

              // Determine which admin to bill
              let adminToBill = null;
              let userForBilling = user;

              if (user?.role === 'admin') {
                // If the user is an admin, bill them directly
                adminToBill = user;
                console.log(`💼 User is admin - billing admin directly: ${adminToBill.userCode} - ${adminToBill.name}`);
              } else if (user?.role === 'user' && user.adminId) {
                // If the user is a regular user, bill their admin
                adminToBill = await User.findById(user.adminId);
                console.log(`👤 User is regular user - billing their admin: ${adminToBill ? adminToBill.userCode + ' - ' + adminToBill.name : 'Not found'}`);
              } else {
                console.log(`❌ Cannot determine admin to bill for user: ${user ? user.userCode + ' - ' + user.name : 'unknown'}`);
              }

              // Create billing if we have an admin to bill
              if (adminToBill && adminToBill.orderRate && adminToBill.orderRate > 0) {
                console.log(`💰 Creating Motilal billing for admin: ${adminToBill.name}, rate: ₹${adminToBill.orderRate}`);
                // Create billing record
                const billingRecord = new BillingRecord({
                  adminId: adminToBill._id,
                  superAdminId: adminToBill.superAdminId,
                  orderId: motilalOrderId,
                  orderType: orderType.toUpperCase(),
                  symbol: symbolData.symbol,
                  quantity: parseInt(quantity),
                  price: parseFloat(price) || 0,
                  broker: 'motilal',
                  userId: userForBilling._id,
                  clientId: client.userId,
                  orderRate: adminToBill.orderRate,
                  amount: adminToBill.orderRate, // Charge per order
                  status: 'pending',
                  billingCycle: getCurrentBillingCycle(),
                });

                await billingRecord.save();

                // Update admin's total orders and billing
                await User.findByIdAndUpdate(adminToBill._id, {
                  $inc: {
                    totalOrders: 1,
                    totalBilling: adminToBill.orderRate
                  },
                  lastBillingDate: new Date()
                });

                console.log(`💰 ✅ Motilal billing record created for admin ${adminToBill.name}: ₹${adminToBill.orderRate}`);
              } else {
                console.log(`❌ Motilal admin billing failed: admin=${adminToBill ? 'found' : 'not found'}, orderRate=${adminToBill?.orderRate || 0}`);
                console.log(`   Reason: ${!adminToBill ? 'No admin to bill' : 'Admin has no order rate or rate is 0'}`);
              }
            } else {
              console.log(`❌ Motilal user or owner not found for client: ${client.userId}`);
            }
          } catch (billingError) {
            console.error('❌ Error creating Motilal billing record:', billingError);
            // Don't fail the order if billing fails
          }
        } else {
          console.log(`❌ Motilal order not successful or no orderid: isSuccess=${isSuccess}, orderid=${motilalOrderId}`);
        }
        orderResults.push({
          clientId: client.userId,
          clientName: client.clientName,
          status: isSuccess,
          message: result.message || result.errorMessage || 'Order processed',
          orderid: result.data?.orderid || result.orderid || '',
          uniqueorderid: result.data?.uniqueorderid || result.uniqueorderid || '',
          symbolData: symbolData,
          response: result
        });

        console.log(`${isSuccess ? '✅' : '❌'} Order result for ${client.clientName}: ${isSuccess ? 'SUCCESS' : 'FAILED'}`);
        if (!isSuccess) {
          // Enhanced error logging for Motilal failures
          const errorMessage = result.message || result.errorMessage || 'Order failed';
          const errorCode = result.errorcode || 'UNKNOWN';

          console.log(`❌ Motilal Order Failed for ${client.clientName}:`);
          console.log(`   Error Code: ${errorCode}`);
          console.log(`   Error Message: ${errorMessage}`);
          console.log(`   Order ID: ${motilalOrderId}`);
          console.log(`   Full Response:`, result);

          // Common Motilal error codes and solutions
          switch (errorCode) {
            case '100018':
              console.log(`   💡 Solution: Insufficient margin - Add funds or reduce quantity`);
              break;
            case '100048':
              console.log(`   💡 Solution: ValuePlus Orders Not Allowed - Use DELIVERY product type for delivery orders`);
              break;
            case '16308':
              console.log(`   💡 Solution: Price freeze - Try market order or different price`);
              break;
            case 'MO2004':
              console.log(`   💡 Solution: Invalid request format - Check order parameters`);
              break;
            case 'MO2012':
              console.log(`   💡 Solution: Invalid vendor info - Check broker configuration`);
              break;
          }
        }

      } catch (error) {
        console.error(`Error placing order for client ${client.userId}:`, error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        orderResults.push({
          clientId: client.userId,
          clientName: client.clientName,
          status: false,
          message: `Error: ${errorMessage}`,
          error: errorMessage
        });
      }
    }

    return Response.json({
      success: true,
      message: `Orders processed for ${targetClients.length} client(s)`,
      results: orderResults
    });

  } catch (error) {
    console.error('Error in Motilal order placement:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return Response.json({
      error: 'Internal server error',
      message: errorMessage
    }, { status: 500 });
  }
}

export async function GET() {
  try {
    return Response.json([]);
  } catch (error) {
    return Response.json({
      error: 'Internal server error'
    }, { status: 500 });
  }
}
