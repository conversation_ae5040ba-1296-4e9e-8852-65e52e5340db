import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      clientCode,
      securityId,
      ticker,
      quantity,
      price,
      productType,
      validity,
      orderType
    } = body;

    // Validate required fields
    if (!clientCode || !securityId || !quantity || !price || !orderType) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    await connectDB();

    // Mock order placement - replace with actual broker API calls
    const orderId = `ORD_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Simulate order placement logic
    const orderData = {
      orderId,
      clientCode,
      securityId,
      ticker,
      quantity: parseInt(quantity),
      price: parseFloat(price),
      productType,
      validity,
      orderType,
      status: 'PENDING',
      timestamp: new Date(),
    };

    // In production, save to database and call broker APIs
    console.log('Order placed:', orderData);

    return NextResponse.json({
      success: true,
      orderId,
      message: 'Order placed successfully',
      data: orderData
    });
  } catch (error) {
    console.error('Error placing order:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to place order' },
      { status: 500 }
    );
  }
}