import connectDB from '@/lib/mongodb';
import { verifyAuth } from '@/middleware/auth';
import { withOrderRateLimit } from '@/middleware/rateLimit';
import { validateRequestBody, ValidationSchemas, getSecurityHeaders } from '@/lib/validation';
import { handleApiError } from '@/lib/errorHandler';

async function handleOrderPlacement(request: Request) {
  try {
    // Verify authentication
    const user = await verifyAuth(request);
    if (!user) {
      return Response.json(
        { success: false, error: 'Unauthorized' },
        { status: 401, headers: getSecurityHeaders() }
      );
    }

    const body = await request.json();

    // Validate and sanitize input data
    const validatedData = validateRequestBody(body, {
      clientCode: ValidationSchemas.order.clientCode,
      securityId: ValidationSchemas.order.symbol,
      ticker: ValidationSchemas.order.symbol,
      quantity: ValidationSchemas.order.quantity,
      price: ValidationSchemas.order.price,
      productType: ValidationSchemas.order.productType,
      orderType: ValidationSchemas.order.orderType,
      validity: (input: any) => {
        const validity = input?.toString().toUpperCase() || 'DAY';
        if (!['DAY', 'IOC', 'GTD'].includes(validity)) {
          throw new Error('Invalid validity type');
        }
        return validity;
      }
    });

    await connectDB();

    // Generate secure order ID
    const orderId = `ORD_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Create order data with validated inputs
    const orderData = {
      orderId,
      userId: user.userId,
      clientCode: validatedData.clientCode,
      securityId: validatedData.securityId,
      ticker: validatedData.ticker,
      quantity: validatedData.quantity,
      price: validatedData.price,
      productType: validatedData.productType,
      validity: validatedData.validity,
      orderType: validatedData.orderType,
      status: 'PENDING',
      timestamp: new Date(),
    };

    // Log order placement for audit trail
    console.log('🔒 Secure order placed:', {
      orderId: orderData.orderId,
      userId: user.userId,
      clientCode: orderData.clientCode,
      symbol: orderData.ticker,
      quantity: orderData.quantity,
      orderType: orderData.orderType
    });

    return Response.json({
      success: true,
      orderId,
      message: 'Order placed successfully',
      data: orderData
    }, {
      headers: getSecurityHeaders()
    });
  } catch (error) {
    console.error('Error placing order:', error);
    return handleApiError(error);
  }
}

// Apply rate limiting and export
export const POST = withOrderRateLimit(handleOrderPlacement);