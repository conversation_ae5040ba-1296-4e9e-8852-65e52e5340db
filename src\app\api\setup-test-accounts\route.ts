import connectDB from '@/lib/mongodb';
import { verifyAuth } from '@/middleware/auth';
import Angel<PERSON><PERSON> from '@/models/AngelUser';
import MotilalUser from '@/models/MotilalUser';

export async function POST(request: Request) {
  try {
    await connectDB();
    
    // Verify authentication
    const user = await verifyAuth(request);
    if (!user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log(`🔧 Setting up test accounts for user: ${user.email} (${user.userId})`);

    // Check if user already has accounts
    const existingAngel = await AngelUser.find({ owner: user.userId });
    const existingMotilal = await MotilalUser.find({ owner: user.userId });

    if (existingAngel.length > 0 || existingMotilal.length > 0) {
      return Response.json({
        message: 'Test accounts already exist',
        existing: {
          angel: existingAngel.length,
          motilal: existingMotilal.length
        }
      });
    }

    // Create test Angel accounts
    const angelAccounts = [
      {
        userId: 'ANG001',
        password: 'test123',
        api<PERSON>ey: 'test-angel-api-key-1',
        totpKey: 'JBSWY3DPEHPK3PXP',
        clientName: 'Test Angel Client 1',
        email: user.email,
        phoneNumber: '**********',
        state: 'live',
        capital: 100000,
        owner: user.userId
      },
      {
        userId: 'ANG002',
        password: 'test123',
        apiKey: 'test-angel-api-key-2',
        totpKey: 'JBSWY3DPEHPK3PXP',
        clientName: 'Test Angel Client 2',
        email: user.email,
        phoneNumber: '**********',
        state: 'live',
        capital: 150000,
        owner: user.userId
      }
    ];

    // Create test Motilal accounts
    const motilalAccounts = [
      {
        userId: 'MOT001',
        password: 'test123',
        apiKey: 'test-motilal-api-key-1',
        twoFA: 'test2fa1',
        totpKey: 'JBSWY3DPEHPK3PXP',
        clientName: 'Test Motilal Client 1',
        email: user.email,
        phoneNumber: '**********',
        status: 'active',
        capital: 120000,
        owner: user.userId
      },
      {
        userId: 'MOT002',
        password: 'test123',
        apiKey: 'test-motilal-api-key-2',
        twoFA: 'test2fa2',
        totpKey: 'JBSWY3DPEHPK3PXP',
        clientName: 'Test Motilal Client 2',
        email: user.email,
        phoneNumber: '**********',
        status: 'active',
        capital: 180000,
        owner: user.userId
      }
    ];

    // Insert accounts
    const createdAngel = await AngelUser.insertMany(angelAccounts);
    const createdMotilal = await MotilalUser.insertMany(motilalAccounts);

    console.log(`✅ Created ${createdAngel.length} Angel accounts and ${createdMotilal.length} Motilal accounts`);

    return Response.json({
      success: true,
      message: 'Test accounts created successfully',
      created: {
        angel: createdAngel.length,
        motilal: createdMotilal.length
      },
      accounts: {
        angel: createdAngel.map(acc => ({
          id: acc._id,
          userId: acc.userId,
          clientName: acc.clientName,
          state: acc.state
        })),
        motilal: createdMotilal.map(acc => ({
          id: acc._id,
          userId: acc.userId,
          clientName: acc.clientName,
          status: acc.status
        }))
      }
    });

  } catch (error) {
    console.error('Error setting up test accounts:', error);
    return Response.json({ 
      error: 'Failed to setup test accounts',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// GET - Check existing accounts
export async function GET(request: Request) {
  try {
    await connectDB();
    
    const user = await verifyAuth(request);
    if (!user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const angelAccounts = await AngelUser.find({ owner: user.userId })
      .select('-password -totpKey -apiKey')
      .sort({ createdAt: -1 });

    const motilalAccounts = await MotilalUser.find({ owner: user.userId })
      .select('-password -totpKey -apiKey -twoFA')
      .sort({ createdAt: -1 });

    return Response.json({
      success: true,
      accounts: {
        angel: angelAccounts,
        motilal: motilalAccounts
      },
      summary: {
        totalAngel: angelAccounts.length,
        totalMotilal: motilalAccounts.length,
        totalAccounts: angelAccounts.length + motilalAccounts.length
      }
    });

  } catch (error) {
    console.error('Error fetching accounts:', error);
    return Response.json({ 
      error: 'Failed to fetch accounts',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
