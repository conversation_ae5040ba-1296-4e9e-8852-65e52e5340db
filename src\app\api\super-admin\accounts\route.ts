
import connectDB from '@/lib/mongodb';
import { verifyAuth } from '@/middleware/auth';
import AngelUser from '@/models/AngelUser';
import MotilalUser from '@/models/MotilalUser';
import User from '@/models/User';
import { with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AuthorizationError, ValidationError, NotFoundError, createSuccessResponse } from '@/lib/errorHandler';

export const GET = withErrorHandler(async (request: Request) => {
  await connectDB();

  // Verify super admin authentication
  const decoded = await verifyAuth(request);

  if (!decoded || decoded.role !== 'super_admin') {
    throw new AuthorizationError('Super admin access required');
  }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type'); // 'angel', 'motilal', or 'all'
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const skip = (page - 1) * limit;

    let angelAccounts = [];
    let motilalAccounts = [];
    let totalAngel = 0;
    let totalMotilal = 0;

    if (type === 'angel' || type === 'all' || !type) {
      // Get Angel accounts with owner information
      const angelQuery = AngelUser.find({})
        .populate('owner', 'name email userCode role')
        .sort({ createdAt: -1 });
      
      if (type === 'angel') {
        angelAccounts = await angelQuery.skip(skip).limit(limit);
        totalAngel = await AngelUser.countDocuments({});
      } else {
        angelAccounts = await angelQuery;
        totalAngel = angelAccounts.length;
      }
    }

    if (type === 'motilal' || type === 'all' || !type) {
      // Get Motilal accounts with owner information
      const motilalQuery = MotilalUser.find({})
        .populate('owner', 'name email userCode role')
        .sort({ createdAt: -1 });
      
      if (type === 'motilal') {
        motilalAccounts = await motilalQuery.skip(skip).limit(limit);
        totalMotilal = await MotilalUser.countDocuments({});
      } else {
        motilalAccounts = await motilalQuery;
        totalMotilal = motilalAccounts.length;
      }
    }

    // Format the response
    const response = {
      success: true,
      data: {
        angelAccounts: angelAccounts.map(account => ({
          _id: account._id,
          userId: account.userId,
          clientName: account.clientName,
          email: account.email,
          phoneNumber: account.phoneNumber,
          capital: account.capital,
          state: account.state,
          owner: account.owner,
          createdAt: account.createdAt,
          updatedAt: account.updatedAt
        })),
        motilalAccounts: motilalAccounts.map(account => ({
          _id: account._id,
          userId: account.userId,
          clientName: account.clientName,
          email: account.email,
          phoneNumber: account.phoneNumber,
          capital: account.capital,
          status: account.status,
          owner: account.owner,
          createdAt: account.createdAt,
          updatedAt: account.updatedAt
        })),
        pagination: {
          page,
          limit,
          totalAngel,
          totalMotilal,
          totalPages: type === 'angel' ? Math.ceil(totalAngel / limit) : 
                     type === 'motilal' ? Math.ceil(totalMotilal / limit) : 1
        },
        summary: {
          totalAngel,
          totalMotilal,
          totalAccounts: totalAngel + totalMotilal,
          activeAngel: angelAccounts.filter(acc => acc.state === 'live').length,
          activeMotilal: motilalAccounts.filter(acc => acc.status === 'active').length
        }
      }
    };

    return createSuccessResponse(response.data, 'Accounts fetched successfully');
});

// PUT - Update account status
export const PUT = withErrorHandler(async (request: Request) => {
  await connectDB();

  // Verify super admin authentication
  const decoded = await verifyAuth(request);

  if (!decoded || decoded.role !== 'super_admin') {
    throw new AuthorizationError('Super admin access required');
  }

  const { accountId, accountType, updates } = await request.json();

  if (!accountId || !accountType || !updates) {
    throw new ValidationError('Account ID, type, and updates are required');
  }

    let updatedAccount;

    if (accountType === 'angel') {
      updatedAccount = await AngelUser.findByIdAndUpdate(
        accountId,
        updates,
        { new: true }
      ).populate('owner', 'name email userCode role');
    } else if (accountType === 'motilal') {
      updatedAccount = await MotilalUser.findByIdAndUpdate(
        accountId,
        updates,
        { new: true }
      ).populate('owner', 'name email userCode role');
    } else {
      return NextResponse.json(
        { error: 'Invalid account type' },
        { status: 400 }
      );
    }

    if (!updatedAccount) {
      throw new NotFoundError('Account not found');
    }

    return createSuccessResponse(
      { account: updatedAccount },
      'Account updated successfully'
    );
});
