import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { verifyAuth } from '@/middleware/auth';
import AngelUser from '@/models/AngelUser';
import MotilalUser from '@/models/MotilalUser';
import User from '@/models/User';

export async function GET(request: NextRequest) {
  try {
    await connectDB();
    
    // Verify super admin authentication
    const decoded = await verifyAuth(request);
    
    if (decoded.role !== 'super_admin') {
      return NextResponse.json(
        { error: 'Access denied. Super admin required.' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type'); // 'angel', 'motilal', or 'all'
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const skip = (page - 1) * limit;

    let angelAccounts = [];
    let motilalAccounts = [];
    let totalAngel = 0;
    let totalMotilal = 0;

    if (type === 'angel' || type === 'all' || !type) {
      // Get Angel accounts with owner information
      const angelQuery = AngelUser.find({})
        .populate('owner', 'name email userCode role')
        .sort({ createdAt: -1 });
      
      if (type === 'angel') {
        angelAccounts = await angelQuery.skip(skip).limit(limit);
        totalAngel = await AngelUser.countDocuments({});
      } else {
        angelAccounts = await angelQuery;
        totalAngel = angelAccounts.length;
      }
    }

    if (type === 'motilal' || type === 'all' || !type) {
      // Get Motilal accounts with owner information
      const motilalQuery = MotilalUser.find({})
        .populate('owner', 'name email userCode role')
        .sort({ createdAt: -1 });
      
      if (type === 'motilal') {
        motilalAccounts = await motilalQuery.skip(skip).limit(limit);
        totalMotilal = await MotilalUser.countDocuments({});
      } else {
        motilalAccounts = await motilalQuery;
        totalMotilal = motilalAccounts.length;
      }
    }

    // Format the response
    const response = {
      success: true,
      data: {
        angelAccounts: angelAccounts.map(account => ({
          _id: account._id,
          userId: account.userId,
          clientName: account.clientName,
          email: account.email,
          phoneNumber: account.phoneNumber,
          capital: account.capital,
          state: account.state,
          owner: account.owner,
          createdAt: account.createdAt,
          updatedAt: account.updatedAt
        })),
        motilalAccounts: motilalAccounts.map(account => ({
          _id: account._id,
          userId: account.userId,
          clientName: account.clientName,
          email: account.email,
          phoneNumber: account.phoneNumber,
          capital: account.capital,
          status: account.status,
          owner: account.owner,
          createdAt: account.createdAt,
          updatedAt: account.updatedAt
        })),
        pagination: {
          page,
          limit,
          totalAngel,
          totalMotilal,
          totalPages: type === 'angel' ? Math.ceil(totalAngel / limit) : 
                     type === 'motilal' ? Math.ceil(totalMotilal / limit) : 1
        },
        summary: {
          totalAngel,
          totalMotilal,
          totalAccounts: totalAngel + totalMotilal,
          activeAngel: angelAccounts.filter(acc => acc.state === 'live').length,
          activeMotilal: motilalAccounts.filter(acc => acc.status === 'active').length
        }
      }
    };

    return NextResponse.json(response);

  } catch (error: any) {
    console.error('Error fetching accounts:', error);
    
    if (error.message === 'No token provided' || error.name === 'JsonWebTokenError') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to fetch accounts' },
      { status: 500 }
    );
  }
}

// PUT - Update account status
export async function PUT(request: NextRequest) {
  try {
    await connectDB();
    
    // Verify super admin authentication
    const decoded = await verifyAuth(request);
    
    if (decoded.role !== 'super_admin') {
      return NextResponse.json(
        { error: 'Access denied. Super admin required.' },
        { status: 403 }
      );
    }

    const { accountId, accountType, updates } = await request.json();

    if (!accountId || !accountType || !updates) {
      return NextResponse.json(
        { error: 'Account ID, type, and updates are required' },
        { status: 400 }
      );
    }

    let updatedAccount;

    if (accountType === 'angel') {
      updatedAccount = await AngelUser.findByIdAndUpdate(
        accountId,
        updates,
        { new: true }
      ).populate('owner', 'name email userCode role');
    } else if (accountType === 'motilal') {
      updatedAccount = await MotilalUser.findByIdAndUpdate(
        accountId,
        updates,
        { new: true }
      ).populate('owner', 'name email userCode role');
    } else {
      return NextResponse.json(
        { error: 'Invalid account type' },
        { status: 400 }
      );
    }

    if (!updatedAccount) {
      return NextResponse.json(
        { error: 'Account not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Account updated successfully',
      account: updatedAccount
    });

  } catch (error: any) {
    console.error('Error updating account:', error);
    
    if (error.message === 'No token provided' || error.name === 'JsonWebTokenError') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to update account' },
      { status: 500 }
    );
  }
}
