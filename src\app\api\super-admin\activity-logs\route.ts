import connectDB from '@/lib/mongodb';
import ActivityLog from '@/models/ActivityLog';
import { requireSuperAdmin } from '@/middleware/auth';

// GET: Get activity logs with filtering and pagination
export async function GET(request: Request) {
  try {
    const { user, hasAccess } = await requireSuperAdmin(request);
    
    if (!hasAccess || !user) {
      return Response.json({ error: 'Unauthorized - Super Admin access required' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const adminId = searchParams.get('adminId');
    const category = searchParams.get('category');
    const severity = searchParams.get('severity');
    const status = searchParams.get('status');
    const action = searchParams.get('action');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');

    await connectDB();

    // Build query
    let query: any = {
      superAdminId: user.userId
    };

    if (adminId) {
      query.adminId = adminId;
    }

    if (category && category !== 'all') {
      query.category = category;
    }

    if (severity && severity !== 'all') {
      query.severity = severity;
    }

    if (status && status !== 'all') {
      query.status = status;
    }

    if (action) {
      query.action = { $regex: action, $options: 'i' };
    }

    // Date range filter
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) {
        query.createdAt.$gte = new Date(startDate);
      }
      if (endDate) {
        query.createdAt.$lte = new Date(endDate);
      }
    }

    const skip = (page - 1) * limit;

    // Get logs with pagination
    const [logs, total] = await Promise.all([
      ActivityLog.find(query)
        .populate('adminId', 'name email companyName')
        .populate('userId', 'name email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      ActivityLog.countDocuments(query)
    ]);

    // Get summary statistics
    const summaryStats = await ActivityLog.aggregate([
      { $match: { superAdminId: user.userId } },
      {
        $group: {
          _id: null,
          totalLogs: { $sum: 1 },
          todayLogs: {
            $sum: {
              $cond: [
                {
                  $gte: [
                    '$createdAt',
                    new Date(new Date().setHours(0, 0, 0, 0))
                  ]
                },
                1,
                0
              ]
            }
          },
          criticalLogs: {
            $sum: {
              $cond: [{ $eq: ['$severity', 'critical'] }, 1, 0]
            }
          },
          failedActions: {
            $sum: {
              $cond: [{ $eq: ['$status', 'failed'] }, 1, 0]
            }
          }
        }
      }
    ]);

    // Get category breakdown
    const categoryStats = await ActivityLog.aggregate([
      { $match: { superAdminId: user.userId } },
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 }
        }
      },
      { $sort: { count: -1 } }
    ]);

    // Get recent critical activities
    const recentCritical = await ActivityLog.find({
      superAdminId: user.userId,
      severity: 'critical',
      createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } // Last 7 days
    })
      .populate('adminId', 'name email')
      .sort({ createdAt: -1 })
      .limit(10)
      .lean();

    const summary = summaryStats[0] || {
      totalLogs: 0,
      todayLogs: 0,
      criticalLogs: 0,
      failedActions: 0
    };

    return Response.json({
      success: true,
      data: {
        logs,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        },
        summary,
        categoryStats,
        recentCritical,
        filters: {
          adminId,
          category,
          severity,
          status,
          action,
          startDate,
          endDate
        }
      }
    });

  } catch (error) {
    console.error('❌ Error fetching activity logs:', error);
    return Response.json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// POST: Create a new activity log entry
export async function POST(request: Request) {
  try {
    const { user, hasAccess } = await requireSuperAdmin(request);
    
    if (!hasAccess || !user) {
      return Response.json({ error: 'Unauthorized - Super Admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const {
      adminId,
      userId,
      action,
      category,
      description,
      details,
      severity = 'low',
      status = 'success'
    } = body;

    if (!action || !category || !description) {
      return Response.json({ 
        error: 'Action, category, and description are required' 
      }, { status: 400 });
    }

    await connectDB();

    // Get request info
    const ipAddress = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    const activityLog = new ActivityLog({
      superAdminId: user.userId,
      adminId,
      userId,
      action,
      category,
      description,
      details,
      ipAddress,
      userAgent,
      severity,
      status
    });

    await activityLog.save();

    console.log(`📝 Activity logged: ${action} - ${description}`);

    return Response.json({
      success: true,
      message: 'Activity logged successfully',
      data: activityLog
    });

  } catch (error) {
    console.error('❌ Error creating activity log:', error);
    return Response.json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
