import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { BillingRecord, BillingSummary } from '@/models/Billing';
import { requireSuperAdmin } from '@/middleware/auth';

// GET: Get specific admin details
export async function GET(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { user, hasAccess } = await requireSuperAdmin(request);

    if (!hasAccess || !user) {
      return Response.json({ error: 'Unauthorized - Super Admin access required' }, { status: 403 });
    }

    await connectDB();

    const { id } = await params;

    const admin = await User.findOne({
      _id: id,
      superAdminId: user.userId,
      role: 'admin'
    }).select('-password').lean();

    if (!admin) {
      return Response.json({ error: 'Admin not found' }, { status: 404 });
    }

    // Get admin's users count
    const userCount = await User.countDocuments({
      adminId: (admin as any)._id,
      role: 'user',
      isActive: true
    });

    // Get billing statistics
    const billingStats = await BillingRecord.aggregate([
      {
        $match: { adminId: (admin as any)._id }
      },
      {
        $group: {
          _id: null,
          totalOrders: { $sum: 1 },
          totalRevenue: { $sum: '$amount' },
          pendingAmount: {
            $sum: {
              $cond: [{ $eq: ['$status', 'pending'] }, '$amount', 0]
            }
          },
          paidAmount: {
            $sum: {
              $cond: [{ $eq: ['$status', 'paid'] }, '$amount', 0]
            }
          }
        }
      }
    ]);

    const stats = billingStats[0] || {
      totalOrders: 0,
      totalRevenue: 0,
      pendingAmount: 0,
      paidAmount: 0
    };

    return Response.json({
      success: true,
      data: {
        ...admin,
        statistics: {
          userCount,
          ...stats
        }
      }
    });

  } catch (error) {
    console.error('❌ Error fetching admin:', error);
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PUT: Update admin details
export async function PUT(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { user, hasAccess } = await requireSuperAdmin(request);

    if (!hasAccess || !user) {
      return Response.json({ error: 'Unauthorized - Super Admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const {
      name,
      email,
      companyName,
      phoneNumber,
      address,
      orderRate,
      isActive
    } = body;

    await connectDB();

    const { id } = await params;

    // Check if admin exists and belongs to this super admin
    const existingAdmin = await User.findOne({
      _id: id,
      superAdminId: user.userId,
      role: 'admin'
    });

    if (!existingAdmin) {
      return Response.json({ error: 'Admin not found' }, { status: 404 });
    }

    // Check if email is already taken by another user
    if (email && email !== existingAdmin.email) {
      const emailExists = await User.findOne({
        email: email.toLowerCase(),
        _id: { $ne: id }
      });

      if (emailExists) {
        return Response.json({ error: 'Email already exists' }, { status: 400 });
      }
    }

    // Update admin
    const updatedAdmin = await User.findByIdAndUpdate(
      id,
      {
        ...(name && { name }),
        ...(email && { email: email.toLowerCase() }),
        ...(companyName !== undefined && { companyName }),
        ...(phoneNumber !== undefined && { phoneNumber }),
        ...(address !== undefined && { address }),
        ...(orderRate !== undefined && { orderRate: Number(orderRate) }),
        ...(isActive !== undefined && { isActive }),
        updatedAt: new Date()
      },
      { new: true, runValidators: true }
    ).select('-password');

    console.log(`✅ Admin updated: ${updatedAdmin?.email}`);

    return Response.json({
      success: true,
      message: 'Admin updated successfully',
      data: updatedAdmin
    });

  } catch (error) {
    console.error('❌ Error updating admin:', error);
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// DELETE: Delete admin (soft delete - deactivate)
export async function DELETE(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { user, hasAccess } = await requireSuperAdmin(request);

    if (!hasAccess || !user) {
      return Response.json({ error: 'Unauthorized - Super Admin access required' }, { status: 403 });
    }

    await connectDB();

    const { id } = await params;

    // Check if admin exists and belongs to this super admin
    const admin = await User.findOne({
      _id: id,
      superAdminId: user.userId,
      role: 'admin'
    });

    if (!admin) {
      return Response.json({ error: 'Admin not found' }, { status: 404 });
    }

    // Soft delete - deactivate admin and their users
    await User.findByIdAndUpdate(id, {
      isActive: false,
      updatedAt: new Date()
    });

    // Also deactivate all users under this admin
    await User.updateMany(
      { adminId: id, role: 'user' },
      { 
        isActive: false,
        updatedAt: new Date()
      }
    );

    console.log(`✅ Admin deactivated: ${admin.email}`);

    return Response.json({
      success: true,
      message: 'Admin deactivated successfully'
    });

  } catch (error) {
    console.error('❌ Error deleting admin:', error);
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}
