import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { requireSuperAdmin } from '@/middleware/auth';

// PUT: Update admin status (suspend, block, activate)
export async function PUT(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { user, hasAccess } = await requireSuperAdmin(request);

    if (!hasAccess || !user) {
      return Response.json({ error: 'Unauthorized - Super Admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const { status } = body;

    if (!status || !['active', 'suspended', 'blocked'].includes(status)) {
      return Response.json({ error: 'Valid status is required (active, suspended, blocked)' }, { status: 400 });
    }

    await connectDB();

    const { id } = await params;

    // Check if admin exists and belongs to this super admin
    const admin = await User.findOne({
      _id: id,
      superAdminId: user.userId,
      role: 'admin'
    });

    if (!admin) {
      return Response.json({ error: 'Admin not found' }, { status: 404 });
    }

    // Update admin status
    const updateData: any = {
      status,
      updatedAt: new Date()
    };

    // Set isActive based on status
    if (status === 'active') {
      updateData.isActive = true;
    } else if (status === 'suspended' || status === 'blocked') {
      updateData.isActive = false;
    }

    const updatedAdmin = await User.findByIdAndUpdate(
      id,
      updateData,
      { new: true }
    ).select('-password');

    // Also update all users under this admin if blocking/suspending
    if (status === 'suspended' || status === 'blocked') {
      await User.updateMany(
        { adminId: id, role: 'user' },
        { 
          isActive: false,
          updatedAt: new Date()
        }
      );
    } else if (status === 'active') {
      // Reactivate users when admin is reactivated
      await User.updateMany(
        { adminId: id, role: 'user' },
        { 
          isActive: true,
          updatedAt: new Date()
        }
      );
    }

    console.log(`✅ Admin status updated: ${admin.email} -> ${status}`);

    return Response.json({
      success: true,
      message: `Admin ${status} successfully`,
      data: updatedAdmin
    });

  } catch (error) {
    console.error('❌ Error updating admin status:', error);
    return Response.json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
