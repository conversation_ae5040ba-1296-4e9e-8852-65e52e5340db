import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { BillingRecord, BillingSummary } from '@/models/Billing';
import { OrderResponse, SimpleOrder } from '@/models/OrderModel';
import BatchOrderHistory from '@/models/BatchOrderHistory';
import Angel<PERSON><PERSON> from '@/models/AngelUser';
import MotilalU<PERSON> from '@/models/MotilalUser';
import { requireSuperAdmin } from '@/middleware/auth';

// PUT: Terminate admin account (permanent deletion)
export async function PUT(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { user, hasAccess } = await requireSuperAdmin(request);

    if (!hasAccess || !user) {
      return Response.json({ error: 'Unauthorized - Super Admin access required' }, { status: 403 });
    }

    await connectDB();

    const { id } = await params;

    // Check if admin exists and belongs to this super admin
    const admin = await User.findOne({
      _id: id,
      superAdminId: user.userId,
      role: 'admin'
    });

    if (!admin) {
      return Response.json({ error: 'Admin not found' }, { status: 404 });
    }

    console.log(`🗑️ Starting termination process for admin: ${admin.email}`);

    let deletionSummary = {
      users: 0,
      angelAccounts: 0,
      motilalAccounts: 0,
      billingRecords: 0,
      billingSummaries: 0,
      orderResponses: 0,
      simpleOrders: 0,
      batchOrderHistory: 0,
      admin: 0
    };

    // Get all users under this admin
    const adminUsers = await User.find({ adminId: id, role: 'user' });
    const userIds = adminUsers.map(u => u._id);

    // Delete all Angel accounts for these users
    const angelResult = await AngelUser.deleteMany({ userId: { $in: userIds } });
    deletionSummary.angelAccounts = angelResult.deletedCount || 0;
    console.log(`✅ Deleted ${deletionSummary.angelAccounts} Angel accounts`);

    // Delete all Motilal accounts for these users
    const motilalResult = await MotilalUser.deleteMany({ userId: { $in: userIds } });
    deletionSummary.motilalAccounts = motilalResult.deletedCount || 0;
    console.log(`✅ Deleted ${deletionSummary.motilalAccounts} Motilal accounts`);

    // Delete all billing records for this admin
    const billingRecordResult = await BillingRecord.deleteMany({ adminId: id });
    deletionSummary.billingRecords = billingRecordResult.deletedCount || 0;
    console.log(`✅ Deleted ${deletionSummary.billingRecords} billing records`);

    // Delete all billing summaries for this admin
    const billingSummaryResult = await BillingSummary.deleteMany({ adminId: id });
    deletionSummary.billingSummaries = billingSummaryResult.deletedCount || 0;
    console.log(`✅ Deleted ${deletionSummary.billingSummaries} billing summaries`);

    // Delete all order responses for users under this admin
    const orderResponseResult = await OrderResponse.deleteMany({ 
      $or: [
        { userId: { $in: userIds } },
        { adminId: id }
      ]
    });
    deletionSummary.orderResponses = orderResponseResult.deletedCount || 0;
    console.log(`✅ Deleted ${deletionSummary.orderResponses} order responses`);

    // Delete all simple orders for users under this admin
    const simpleOrderResult = await SimpleOrder.deleteMany({ 
      userId: { $in: userIds }
    });
    deletionSummary.simpleOrders = simpleOrderResult.deletedCount || 0;
    console.log(`✅ Deleted ${deletionSummary.simpleOrders} simple orders`);

    // Delete all batch order history for users under this admin
    const batchOrderResult = await BatchOrderHistory.deleteMany({ 
      owner: { $in: userIds }
    });
    deletionSummary.batchOrderHistory = batchOrderResult.deletedCount || 0;
    console.log(`✅ Deleted ${deletionSummary.batchOrderHistory} batch order history`);

    // Delete all users under this admin
    const userResult = await User.deleteMany({ adminId: id, role: 'user' });
    deletionSummary.users = userResult.deletedCount || 0;
    console.log(`✅ Deleted ${deletionSummary.users} users`);

    // Finally, delete the admin account
    const adminResult = await User.deleteOne({ _id: id });
    deletionSummary.admin = adminResult.deletedCount || 0;
    console.log(`✅ Deleted admin account: ${admin.email}`);

    const totalDeleted = Object.values(deletionSummary).reduce((sum, count) => sum + count, 0);

    console.log(`🎉 Termination completed: ${totalDeleted} records deleted`);

    return Response.json({
      success: true,
      message: `Admin account terminated successfully. All associated data has been permanently deleted.`,
      data: {
        terminatedAdmin: {
          id: admin._id,
          name: admin.name,
          email: admin.email,
          companyName: admin.companyName
        },
        deletionSummary,
        totalDeleted,
        terminatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('❌ Error terminating admin:', error);
    return Response.json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
