import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { requireSuperAdmin } from '@/middleware/auth';

// PUT: Bulk update admin order rates
export async function PUT(request: Request) {
  try {
    const { user, hasAccess } = await requireSuperAdmin(request);
    
    if (!hasAccess || !user) {
      return Response.json({ error: 'Unauthorized - Super Admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const { updates } = body;

    if (!updates || !Array.isArray(updates)) {
      return Response.json({ error: 'Updates array is required' }, { status: 400 });
    }

    await connectDB();

    const results = [];
    const errors = [];

    // Process each update
    for (const update of updates) {
      const { adminId, orderRate } = update;

      if (!adminId || orderRate === undefined) {
        errors.push({ adminId, error: 'Admin ID and order rate are required' });
        continue;
      }

      if (orderRate < 0) {
        errors.push({ adminId, error: 'Order rate must be positive' });
        continue;
      }

      try {
        // Find and update the admin
        const admin = await User.findOneAndUpdate(
          { 
            _id: adminId, 
            superAdminId: user.userId, 
            role: 'admin' 
          },
          { 
            orderRate: orderRate,
            updatedAt: new Date()
          },
          { new: true }
        );

        if (!admin) {
          errors.push({ adminId, error: 'Admin not found or access denied' });
          continue;
        }

        results.push({
          adminId: admin._id,
          name: admin.name,
          email: admin.email,
          oldRate: admin.orderRate,
          newRate: orderRate,
          updated: true
        });

      } catch (error) {
        console.error(`Error updating admin ${adminId}:`, error);
        errors.push({ 
          adminId, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
      }
    }

    return Response.json({
      success: true,
      data: {
        updated: results,
        errors: errors,
        summary: {
          totalRequested: updates.length,
          successful: results.length,
          failed: errors.length
        }
      }
    });

  } catch (error) {
    console.error('Bulk update error:', error);
    return Response.json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// GET: Get all admins for bulk operations
export async function GET(request: Request) {
  try {
    const { user, hasAccess } = await requireSuperAdmin(request);
    
    if (!hasAccess || !user) {
      return Response.json({ error: 'Unauthorized - Super Admin access required' }, { status: 403 });
    }

    await connectDB();

    const admins = await User.find({
      superAdminId: user.userId,
      role: 'admin'
    }).select('name email companyName orderRate isActive createdAt').sort({ name: 1 });

    return Response.json({
      success: true,
      data: admins
    });

  } catch (error) {
    console.error('Error fetching admins for bulk operations:', error);
    return Response.json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
