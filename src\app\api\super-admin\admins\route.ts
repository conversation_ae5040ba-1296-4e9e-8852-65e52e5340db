// Using standard Request for Next.js 15 compatibility
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { BillingRecord, BillingSummary } from '@/models/Billing';
import { requireSuperAdmin, getCurrentBillingCycle } from '@/middleware/auth';

// GET: List all admins with their statistics
export async function GET(request: Request) {
  try {
    // Verify super admin access
    const { user, hasAccess } = await requireSuperAdmin(request);
    
    if (!hasAccess || !user) {
      return Response.json({ error: 'Unauthorized - Super Admin access required' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || 'all'; // all, active, inactive

    await connectDB();

    // Build query
    let query: any = {
      superAdminId: user.userId,
      role: 'admin'
    };

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { companyName: { $regex: search, $options: 'i' } }
      ];
    }

    if (status !== 'all') {
      query.isActive = status === 'active';
    }

    // Get total count
    const total = await User.countDocuments(query);

    // Get admins with pagination
    const admins = await User.find(query)
      .select('-password')
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .lean();

    // Get additional statistics for each admin
    const currentCycle = getCurrentBillingCycle();
    const adminIds = admins.map(admin => admin._id);

    // Get user counts for each admin
    const userCounts = await User.aggregate([
      {
        $match: {
          adminId: { $in: adminIds },
          role: 'user',
          isActive: true
        }
      },
      {
        $group: {
          _id: '$adminId',
          userCount: { $sum: 1 }
        }
      }
    ]);

    // Get current month billing for each admin
    const billingStats = await BillingRecord.aggregate([
      {
        $match: {
          adminId: { $in: adminIds },
          billingCycle: currentCycle
        }
      },
      {
        $group: {
          _id: '$adminId',
          totalOrders: { $sum: 1 },
          totalRevenue: { $sum: '$amount' }
        }
      }
    ]);

    // Get billing summaries
    const billingSummaries = await BillingSummary.find({
      adminId: { $in: adminIds },
      billingCycle: currentCycle
    }).lean();

    // Combine data
    const adminsWithStats = admins.map(admin => {
      const userCount = userCounts.find(uc => uc._id.toString() === (admin as any)._id.toString())?.userCount || 0;
      const billing = billingStats.find(bs => bs._id.toString() === (admin as any)._id.toString());
      const billingSummary = billingSummaries.find(bs => bs.adminId.toString() === (admin as any)._id.toString());

      return {
        ...admin,
        statistics: {
          userCount,
          currentMonth: {
            totalOrders: billing?.totalOrders || 0,
            totalRevenue: billing?.totalRevenue || 0,
            billingStatus: billingSummary?.status || 'draft',
            lastBillingDate: admin.lastBillingDate
          }
        }
      };
    });

    return Response.json({
      success: true,
      data: {
        admins: adminsWithStats,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        },
        summary: {
          totalAdmins: total,
          activeAdmins: await User.countDocuments({ ...query, isActive: true }),
          inactiveAdmins: await User.countDocuments({ ...query, isActive: false })
        }
      }
    });

  } catch (error) {
    console.error('Get Admins Error:', error);
    return Response.json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// PUT: Update admin settings
export async function PUT(request: Request) {
  try {
    // Verify super admin access
    const { user, hasAccess } = await requireSuperAdmin(request);
    
    if (!hasAccess || !user) {
      return Response.json({ error: 'Unauthorized - Super Admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const { 
      adminId, 
      orderRate, 
      isActive, 
      companyName, 
      phoneNumber, 
      address 
    } = body;

    if (!adminId) {
      return Response.json({ error: 'Admin ID is required' }, { status: 400 });
    }

    await connectDB();

    // Find the admin and verify ownership
    const admin = await User.findOne({
      _id: adminId,
      superAdminId: user.userId,
      role: 'admin'
    });

    if (!admin) {
      return Response.json({ error: 'Admin not found or not authorized' }, { status: 404 });
    }

    // Prepare update data
    const updateData: any = {};

    if (orderRate !== undefined) {
      if (orderRate < 0 || orderRate > 1000) {
        return Response.json({ 
          error: 'Order rate must be between 0 and 1000' 
        }, { status: 400 });
      }
      updateData.orderRate = orderRate;
    }

    if (isActive !== undefined) {
      updateData.isActive = isActive;
    }

    if (companyName !== undefined) {
      updateData.companyName = companyName;
    }

    if (phoneNumber !== undefined) {
      updateData.phoneNumber = phoneNumber;
    }

    if (address !== undefined) {
      updateData.address = address;
    }

    // Update admin
    const updatedAdmin = await User.findByIdAndUpdate(
      adminId,
      updateData,
      { new: true, runValidators: true }
    ).select('-password');

    console.log(`⚙️ Admin updated:`, {
      adminId,
      email: admin.email,
      name: admin.name,
      updatedBy: user.name,
      changes: updateData
    });

    return Response.json({
      success: true,
      message: 'Admin updated successfully',
      data: updatedAdmin
    });

  } catch (error) {
    console.error('Update Admin Error:', error);
    return Response.json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// DELETE: Deactivate admin (soft delete)
export async function DELETE(request: Request) {
  try {
    // Verify super admin access
    const { user, hasAccess } = await requireSuperAdmin(request);
    
    if (!hasAccess || !user) {
      return Response.json({ error: 'Unauthorized - Super Admin access required' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const adminId = searchParams.get('adminId');

    if (!adminId) {
      return Response.json({ error: 'Admin ID is required' }, { status: 400 });
    }

    await connectDB();

    // Find the admin and verify ownership
    const admin = await User.findOne({
      _id: adminId,
      superAdminId: user.userId,
      role: 'admin'
    });

    if (!admin) {
      return Response.json({ error: 'Admin not found or not authorized' }, { status: 404 });
    }

    // Deactivate admin and all their users
    await User.updateOne(
      { _id: adminId },
      { isActive: false }
    );

    await User.updateMany(
      { adminId: adminId, role: 'user' },
      { isActive: false }
    );

    console.log(`🚫 Admin deactivated:`, {
      adminId,
      email: admin.email,
      name: admin.name,
      deactivatedBy: user.name
    });

    return Response.json({
      success: true,
      message: 'Admin and all associated users deactivated successfully'
    });

  } catch (error) {
    console.error('Deactivate Admin Error:', error);
    return Response.json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
