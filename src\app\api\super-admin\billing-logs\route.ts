import connectDB from '@/lib/mongodb';
import { BillingRecord } from '@/models/Billing';
import { OrderResponse } from '@/models/OrderModel';
import User from '@/models/User';
import { requireSuperAdmin } from '@/middleware/auth';

export async function GET(request: Request) {
  try {
    console.log('🔍 Super Admin Billing Logs API called');

    // Verify super admin access
    const { user, hasAccess } = await requireSuperAdmin(request);

    if (!hasAccess || !user) {
      console.log('❌ Super Admin access denied');
      return Response.json({ error: 'Unauthorized - Super Admin access required' }, { status: 403 });
    }

    await connectDB();

    // Get URL parameters for pagination and filtering
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '50');
    const adminId = url.searchParams.get('adminId');
    const status = url.searchParams.get('status');
    const startDate = url.searchParams.get('startDate');
    const endDate = url.searchParams.get('endDate');

    // Build query
    const query: any = { superAdminId: user.userId };
    
    if (adminId) query.adminId = adminId;
    if (status) query.status = status;
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) query.createdAt.$gte = new Date(startDate);
      if (endDate) query.createdAt.$lte = new Date(endDate);
    }

    // Get billing records with pagination
    const skip = (page - 1) * limit;
    const billingRecords = await BillingRecord.find(query)
      .populate('adminId', 'name email')
      .populate('userId', 'name email')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    // Get total count for pagination
    const totalRecords = await BillingRecord.countDocuments(query);

    // Get recent orders for context
    const recentOrders = await OrderResponse.find({
      superAdminId: user.userId
    })
      .populate('adminId', 'name email')
      .populate('userId', 'name email')
      .sort({ createdAt: -1 })
      .limit(100)
      .lean();

    // Get billing summary statistics
    const billingStats = await BillingRecord.aggregate([
      { $match: { superAdminId: user.userId } },
      {
        $group: {
          _id: null,
          totalAmount: { $sum: '$amount' },
          totalRecords: { $sum: 1 },
          pendingAmount: {
            $sum: {
              $cond: [{ $eq: ['$status', 'pending'] }, '$amount', 0]
            }
          },
          paidAmount: {
            $sum: {
              $cond: [{ $eq: ['$status', 'paid'] }, '$amount', 0]
            }
          },
          pendingCount: {
            $sum: {
              $cond: [{ $eq: ['$status', 'pending'] }, 1, 0]
            }
          },
          paidCount: {
            $sum: {
              $cond: [{ $eq: ['$status', 'paid'] }, 1, 0]
            }
          }
        }
      }
    ]);

    // Get admin-wise billing breakdown
    const adminBilling = await BillingRecord.aggregate([
      { $match: { superAdminId: user.userId } },
      {
        $group: {
          _id: '$adminId',
          totalAmount: { $sum: '$amount' },
          totalOrders: { $sum: 1 },
          pendingAmount: {
            $sum: {
              $cond: [{ $eq: ['$status', 'pending'] }, '$amount', 0]
            }
          },
          paidAmount: {
            $sum: {
              $cond: [{ $eq: ['$status', 'paid'] }, '$amount', 0]
            }
          }
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'admin'
        }
      },
      {
        $unwind: '$admin'
      },
      {
        $project: {
          adminName: '$admin.name',
          adminEmail: '$admin.email',
          totalAmount: 1,
          totalOrders: 1,
          pendingAmount: 1,
          paidAmount: 1
        }
      }
    ]);

    console.log(`📊 Found ${billingRecords.length} billing records (page ${page})`);

    return Response.json({
      success: true,
      data: {
        billingRecords,
        recentOrders,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(totalRecords / limit),
          totalRecords,
          limit
        },
        statistics: billingStats[0] || {
          totalAmount: 0,
          totalRecords: 0,
          pendingAmount: 0,
          paidAmount: 0,
          pendingCount: 0,
          paidCount: 0
        },
        adminBilling
      }
    });

  } catch (error) {
    console.error('Billing Logs Error:', error);
    return Response.json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
