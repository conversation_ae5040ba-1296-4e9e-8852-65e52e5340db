import connectDB from '@/lib/mongodb';
import { BillingRecord } from '@/models/Billing';
import User from '@/models/User';
import { requireSuperAdmin } from '@/middleware/auth';

export async function GET(request: Request) {
  try {
    const { user, hasAccess } = await requireSuperAdmin(request);
    
    if (!hasAccess || !user) {
      return Response.json({ error: 'Unauthorized - Super Admin access required' }, { status: 403 });
    }

    const url = new URL(request.url);
    const startDate = url.searchParams.get('startDate');
    const endDate = url.searchParams.get('endDate');

    await connectDB();

    // Build date filter
    const dateFilter: any = { superAdminId: user.userId };
    if (startDate || endDate) {
      dateFilter.createdAt = {};
      if (startDate) dateFilter.createdAt.$gte = new Date(startDate);
      if (endDate) dateFilter.createdAt.$lte = new Date(endDate + 'T23:59:59.999Z');
    }

    // Get overview statistics
    const overviewStats = await BillingRecord.aggregate([
      { $match: dateFilter },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$amount' },
          totalOrders: { $sum: 1 },
          pendingAmount: {
            $sum: {
              $cond: [{ $eq: ['$status', 'pending'] }, '$amount', 0]
            }
          },
          paidAmount: {
            $sum: {
              $cond: [{ $eq: ['$status', 'paid'] }, '$amount', 0]
            }
          }
        }
      }
    ]);

    // Get current month statistics
    const currentMonthStart = new Date(new Date().getFullYear(), new Date().getMonth(), 1);
    const monthlyStats = await BillingRecord.aggregate([
      { 
        $match: { 
          superAdminId: user.userId,
          createdAt: { $gte: currentMonthStart }
        }
      },
      {
        $group: {
          _id: null,
          monthlyRevenue: { $sum: '$amount' },
          monthlyOrders: { $sum: 1 }
        }
      }
    ]);

    // Get monthly trends (last 6 months)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
    
    const monthlyTrends = await BillingRecord.aggregate([
      { 
        $match: { 
          superAdminId: user.userId,
          createdAt: { $gte: sixMonthsAgo }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          revenue: { $sum: '$amount' },
          orders: { $sum: 1 },
          pendingAmount: {
            $sum: {
              $cond: [{ $eq: ['$status', 'pending'] }, '$amount', 0]
            }
          },
          paidAmount: {
            $sum: {
              $cond: [{ $eq: ['$status', 'paid'] }, '$amount', 0]
            }
          }
        }
      },
      {
        $sort: { '_id.year': 1, '_id.month': 1 }
      },
      {
        $project: {
          month: {
            $concat: [
              { $toString: '$_id.year' },
              '-',
              { $cond: [
                { $lt: ['$_id.month', 10] },
                { $concat: ['0', { $toString: '$_id.month' }] },
                { $toString: '$_id.month' }
              ]}
            ]
          },
          revenue: 1,
          orders: 1,
          pendingAmount: 1,
          paidAmount: 1
        }
      }
    ]);

    // Get admin performance
    const adminPerformance = await BillingRecord.aggregate([
      { $match: dateFilter },
      {
        $group: {
          _id: '$adminId',
          totalRevenue: { $sum: '$amount' },
          totalOrders: { $sum: 1 },
          pendingAmount: {
            $sum: {
              $cond: [{ $eq: ['$status', 'pending'] }, '$amount', 0]
            }
          },
          paidAmount: {
            $sum: {
              $cond: [{ $eq: ['$status', 'paid'] }, '$amount', 0]
            }
          }
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'admin'
        }
      },
      {
        $unwind: '$admin'
      },
      {
        $project: {
          adminId: '$_id',
          adminName: '$admin.name',
          adminEmail: '$admin.email',
          totalRevenue: 1,
          totalOrders: 1,
          pendingAmount: 1,
          paidAmount: 1,
          averageOrderValue: {
            $cond: [
              { $gt: ['$totalOrders', 0] },
              { $divide: ['$totalRevenue', '$totalOrders'] },
              0
            ]
          },
          collectionRate: {
            $cond: [
              { $gt: ['$totalRevenue', 0] },
              { $multiply: [{ $divide: ['$paidAmount', '$totalRevenue'] }, 100] },
              0
            ]
          }
        }
      },
      {
        $sort: { totalRevenue: -1 }
      }
    ]);

    // Get broker breakdown
    const brokerBreakdown = await BillingRecord.aggregate([
      { $match: dateFilter },
      {
        $group: {
          _id: '$broker',
          revenue: { $sum: '$amount' },
          orders: { $sum: 1 },
          pendingAmount: {
            $sum: {
              $cond: [{ $eq: ['$status', 'pending'] }, '$amount', 0]
            }
          },
          paidAmount: {
            $sum: {
              $cond: [{ $eq: ['$status', 'paid'] }, '$amount', 0]
            }
          }
        }
      }
    ]);

    // Process data
    const overview = overviewStats[0] || {
      totalRevenue: 0,
      totalOrders: 0,
      pendingAmount: 0,
      paidAmount: 0
    };

    const monthly = monthlyStats[0] || {
      monthlyRevenue: 0,
      monthlyOrders: 0
    };

    const averageOrderValue = overview.totalOrders > 0 ? overview.totalRevenue / overview.totalOrders : 0;
    const collectionRate = overview.totalRevenue > 0 ? (overview.paidAmount / overview.totalRevenue) * 100 : 0;

    // Format broker breakdown
    const brokerData = {
      angel: { revenue: 0, orders: 0, pendingAmount: 0, paidAmount: 0 },
      motilal: { revenue: 0, orders: 0, pendingAmount: 0, paidAmount: 0 }
    };

    brokerBreakdown.forEach(broker => {
      if (broker._id === 'angel') {
        brokerData.angel = broker;
      } else if (broker._id === 'motilal') {
        brokerData.motilal = broker;
      }
    });

    return Response.json({
      success: true,
      data: {
        overview: {
          ...overview,
          ...monthly,
          averageOrderValue,
          collectionRate
        },
        monthlyTrends,
        adminPerformance,
        brokerBreakdown: brokerData
      }
    });

  } catch (error) {
    console.error('Billing analytics error:', error);
    return Response.json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
