import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { BillingRecord, BillingSummary } from '@/models/Billing';
import { requireSuperAdmin } from '@/middleware/auth';

// POST: Clear billing for specific admin or all admins
export async function POST(request: Request) {
  try {
    const { user, hasAccess } = await requireSuperAdmin(request);
    
    if (!hasAccess || !user) {
      return Response.json({ error: 'Unauthorized - Super Admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const { adminId, billingCycle, clearType } = body;

    if (!clearType || !['all', 'pending', 'specific'].includes(clearType)) {
      return Response.json({ error: 'Invalid clear type. Must be: all, pending, or specific' }, { status: 400 });
    }

    await connectDB();

    let query: any = { superAdminId: user.userId };
    let summaryQuery: any = { superAdminId: user.userId };

    // Build query based on parameters
    if (adminId) {
      query.adminId = adminId;
      summaryQuery.adminId = adminId;
    }

    if (billingCycle) {
      query.billingCycle = billingCycle;
      summaryQuery.billingCycle = billingCycle;
    }

    if (clearType === 'pending') {
      query.status = 'pending';
      summaryQuery.status = { $in: ['draft', 'generated'] };
    }

    let clearedRecords = 0;
    let clearedSummaries = 0;

    if (clearType === 'all' || clearType === 'pending') {
      // Delete billing records
      const recordResult = await BillingRecord.deleteMany(query);
      clearedRecords = recordResult.deletedCount || 0;

      // Delete billing summaries
      const summaryResult = await BillingSummary.deleteMany(summaryQuery);
      clearedSummaries = summaryResult.deletedCount || 0;

      // Reset admin billing totals if clearing all
      if (clearType === 'all') {
        const adminQuery: any = { superAdminId: user.userId, role: 'admin' };
        if (adminId) {
          adminQuery._id = adminId;
        }

        await User.updateMany(
          adminQuery,
          {
            totalOrders: 0,
            totalBilling: 0,
            lastBillingDate: new Date()
          }
        );
      }
    }

    console.log(`✅ Billing cleared: ${clearedRecords} records, ${clearedSummaries} summaries`);

    return Response.json({
      success: true,
      message: `Billing cleared successfully`,
      data: {
        clearedRecords,
        clearedSummaries,
        clearType,
        adminId: adminId || 'all',
        billingCycle: billingCycle || 'all'
      }
    });

  } catch (error) {
    console.error('❌ Error clearing billing:', error);
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// GET: Get clearable billing data preview
export async function GET(request: Request) {
  try {
    const { user, hasAccess } = await requireSuperAdmin(request);
    
    if (!hasAccess || !user) {
      return Response.json({ error: 'Unauthorized - Super Admin access required' }, { status: 403 });
    }

    const url = new URL(request.url);
    const adminId = url.searchParams.get('adminId');
    const billingCycle = url.searchParams.get('billingCycle');
    const clearType = url.searchParams.get('clearType') || 'pending';

    await connectDB();

    let query: any = { superAdminId: user.userId };
    let summaryQuery: any = { superAdminId: user.userId };

    if (adminId) {
      query.adminId = adminId;
      summaryQuery.adminId = adminId;
    }

    if (billingCycle) {
      query.billingCycle = billingCycle;
      summaryQuery.billingCycle = billingCycle;
    }

    if (clearType === 'pending') {
      query.status = 'pending';
      summaryQuery.status = { $in: ['draft', 'generated'] };
    }

    // Count records that would be cleared
    const recordCount = await BillingRecord.countDocuments(query);
    const summaryCount = await BillingSummary.countDocuments(summaryQuery);

    // Get total amounts
    const recordTotals = await BillingRecord.aggregate([
      { $match: query },
      {
        $group: {
          _id: null,
          totalAmount: { $sum: '$amount' },
          totalOrders: { $sum: 1 }
        }
      }
    ]);

    const summaryTotals = await BillingSummary.aggregate([
      { $match: summaryQuery },
      {
        $group: {
          _id: null,
          totalAmount: { $sum: '$totalAmount' },
          totalOrders: { $sum: '$totalOrders' }
        }
      }
    ]);

    const recordTotal = recordTotals[0] || { totalAmount: 0, totalOrders: 0 };
    const summaryTotal = summaryTotals[0] || { totalAmount: 0, totalOrders: 0 };

    return Response.json({
      success: true,
      data: {
        preview: {
          recordsToDelete: recordCount,
          summariesToDelete: summaryCount,
          recordTotalAmount: recordTotal.totalAmount,
          recordTotalOrders: recordTotal.totalOrders,
          summaryTotalAmount: summaryTotal.totalAmount,
          summaryTotalOrders: summaryTotal.totalOrders
        },
        filters: {
          adminId: adminId || 'all',
          billingCycle: billingCycle || 'all',
          clearType
        }
      }
    });

  } catch (error) {
    console.error('❌ Error getting billing preview:', error);
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}
