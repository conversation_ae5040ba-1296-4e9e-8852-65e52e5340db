import connectDB from '@/lib/mongodb';
import { BillingSummary } from '@/models/Billing';
import { requireSuperAdmin } from '@/middleware/auth';

// GET: Export payment history to CSV
export async function GET(request: Request) {
  try {
    const { user, hasAccess } = await requireSuperAdmin(request);
    
    if (!hasAccess || !user) {
      return Response.json({ error: 'Unauthorized - Super Admin access required' }, { status: 403 });
    }

    const url = new URL(request.url);
    const adminId = url.searchParams.get('adminId');
    const billingCycle = url.searchParams.get('billingCycle');

    await connectDB();

    let query: any = { 
      superAdminId: user.userId,
      status: 'paid'
    };

    if (adminId) {
      query.adminId = adminId;
    }

    if (billingCycle) {
      query.billingCycle = billingCycle;
    }

    // Get all payments without pagination
    const payments = await BillingSummary.find(query)
      .populate('adminId', 'name email companyName')
      .sort({ paidAt: -1 })
      .lean();

    // Generate CSV content
    const csvHeader = [
      'Admin Name',
      'Admin Email',
      'Company',
      'Billing Cycle',
      'Total Orders',
      'Amount (₹)',
      'Order Rate (₹)',
      'Payment Method',
      'Payment Reference',
      'Paid Date',
      'Notes'
    ].join(',');

    const csvRows = payments.map(payment => {
      // Escape fields that might contain commas
      const escapeCsv = (field: any) => {
        if (field === null || field === undefined) return '';
        const str = String(field);
        return str.includes(',') || str.includes('"') || str.includes('\n') 
          ? `"${str.replace(/"/g, '""')}"` 
          : str;
      };

      return [
        escapeCsv(payment.adminId?.name || 'N/A'),
        escapeCsv(payment.adminId?.email || 'N/A'),
        escapeCsv(payment.adminId?.companyName || 'N/A'),
        escapeCsv(payment.billingCycle),
        payment.totalOrders,
        payment.totalAmount,
        payment.orderRate,
        escapeCsv(payment.paymentMethod || 'N/A'),
        escapeCsv(payment.paymentReference || 'N/A'),
        payment.paidAt ? new Date(payment.paidAt).toISOString() : 'N/A',
        escapeCsv(payment.notes || '')
      ].join(',');
    });

    const csvContent = [csvHeader, ...csvRows].join('\n');

    // Set headers for CSV download
    const headers = new Headers();
    headers.set('Content-Type', 'text/csv');
    headers.set('Content-Disposition', `attachment; filename="payment-history-${new Date().toISOString().split('T')[0]}.csv"`);

    return new Response(csvContent, {
      headers
    });

  } catch (error) {
    console.error('❌ Error exporting payment history:', error);
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}
