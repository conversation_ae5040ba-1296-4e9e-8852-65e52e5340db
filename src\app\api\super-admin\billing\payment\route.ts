import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { BillingRecord, BillingSummary } from '@/models/Billing';
import { requireSuperAdmin } from '@/middleware/auth';

// POST: Mark payment as received
export async function POST(request: Request) {
  try {
    const { user, hasAccess } = await requireSuperAdmin(request);
    
    if (!hasAccess || !user) {
      return Response.json({ error: 'Unauthorized - Super Admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const { 
      summaryId, 
      adminId, 
      billingCycle, 
      paymentMethod, 
      paymentReference, 
      notes,
      partialAmount 
    } = body;

    await connectDB();

    let updatedSummaries = [];
    let updatedRecords = 0;

    if (summaryId) {
      // Mark specific billing summary as paid
      const billingSummary = await BillingSummary.findOne({
        _id: summaryId,
        superAdminId: user.userId
      });

      if (!billingSummary) {
        return Response.json({ error: 'Billing summary not found' }, { status: 404 });
      }

      const updateData: any = {
        status: 'paid',
        paidAt: new Date(),
        paymentMethod,
        paymentReference,
        notes
      };

      if (partialAmount && partialAmount < billingSummary.totalAmount) {
        // Handle partial payment - create new summary for remaining amount
        const remainingAmount = billingSummary.totalAmount - partialAmount;
        
        // Update current summary with paid amount
        updateData.totalAmount = partialAmount;
        
        // Create new summary for remaining amount
        const remainingSummary = new BillingSummary({
          adminId: billingSummary.adminId,
          superAdminId: billingSummary.superAdminId,
          billingCycle: billingSummary.billingCycle,
          totalOrders: 0, // Will be calculated separately
          totalAmount: remainingAmount,
          orderRate: billingSummary.orderRate,
          status: 'generated',
          generatedAt: new Date(),
          dueDate: billingSummary.dueDate,
          notes: `Remaining amount from partial payment of ${paymentReference}`
        });

        await remainingSummary.save();
      }

      const updated = await BillingSummary.findByIdAndUpdate(
        summaryId,
        updateData,
        { new: true }
      ).populate('adminId', 'name email companyName');

      updatedSummaries.push(updated);

      // Mark related billing records as paid
      const recordUpdate = await BillingRecord.updateMany(
        {
          adminId: billingSummary.adminId,
          billingCycle: billingSummary.billingCycle,
          status: 'billed'
        },
        { 
          status: 'paid',
          updatedAt: new Date()
        }
      );

      updatedRecords = recordUpdate.modifiedCount || 0;

    } else if (adminId && billingCycle) {
      // Mark all billing for specific admin and cycle as paid
      const summaries = await BillingSummary.find({
        adminId,
        billingCycle,
        superAdminId: user.userId,
        status: { $in: ['generated', 'sent'] }
      });

      for (const summary of summaries) {
        const updated = await BillingSummary.findByIdAndUpdate(
          summary._id,
          {
            status: 'paid',
            paidAt: new Date(),
            paymentMethod,
            paymentReference,
            notes
          },
          { new: true }
        ).populate('adminId', 'name email companyName');

        updatedSummaries.push(updated);
      }

      // Mark related billing records as paid
      const recordUpdate = await BillingRecord.updateMany(
        {
          adminId,
          billingCycle,
          superAdminId: user.userId,
          status: 'billed'
        },
        { 
          status: 'paid',
          updatedAt: new Date()
        }
      );

      updatedRecords = recordUpdate.modifiedCount || 0;

    } else {
      return Response.json({ error: 'Either summaryId or adminId+billingCycle is required' }, { status: 400 });
    }

    console.log(`✅ Payment marked: ${updatedSummaries.length} summaries, ${updatedRecords} records`);

    return Response.json({
      success: true,
      message: 'Payment marked successfully',
      data: {
        updatedSummaries,
        updatedRecords,
        paymentMethod,
        paymentReference
      }
    });

  } catch (error) {
    console.error('❌ Error marking payment:', error);
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// GET: Get payment history
export async function GET(request: Request) {
  try {
    const { user, hasAccess } = await requireSuperAdmin(request);
    
    if (!hasAccess || !user) {
      return Response.json({ error: 'Unauthorized - Super Admin access required' }, { status: 403 });
    }

    const url = new URL(request.url);
    const adminId = url.searchParams.get('adminId');
    const billingCycle = url.searchParams.get('billingCycle');
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');

    await connectDB();

    let query: any = { 
      superAdminId: user.userId,
      status: 'paid'
    };

    if (adminId) {
      query.adminId = adminId;
    }

    if (billingCycle) {
      query.billingCycle = billingCycle;
    }

    const skip = (page - 1) * limit;

    const [payments, total] = await Promise.all([
      BillingSummary.find(query)
        .populate('adminId', 'name email companyName')
        .sort({ paidAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      BillingSummary.countDocuments(query)
    ]);

    // Calculate totals
    const totals = await BillingSummary.aggregate([
      { $match: query },
      {
        $group: {
          _id: null,
          totalAmount: { $sum: '$totalAmount' },
          totalOrders: { $sum: '$totalOrders' },
          count: { $sum: 1 }
        }
      }
    ]);

    const summary = totals[0] || {
      totalAmount: 0,
      totalOrders: 0,
      count: 0
    };

    return Response.json({
      success: true,
      data: {
        payments,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        },
        summary
      }
    });

  } catch (error) {
    console.error('❌ Error fetching payment history:', error);
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}
