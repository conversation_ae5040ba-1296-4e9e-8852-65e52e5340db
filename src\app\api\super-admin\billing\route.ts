// Using standard Request for Next.js 15 compatibility
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { BillingRecord, BillingSummary, PaymentRecord } from '@/models/Billing';
import { requireSuperAdmin, getCurrentBillingCycle, getPreviousBillingCycle } from '@/middleware/auth';

// GET: Get billing data with filters
export async function GET(request: Request) {
  try {
    // Verify super admin access
    const { user, hasAccess } = await requireSuperAdmin(request);
    
    if (!hasAccess || !user) {
      return Response.json({ error: 'Unauthorized - Super Admin access required' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const adminId = searchParams.get('adminId');
    const billingCycle = searchParams.get('billingCycle') || getCurrentBillingCycle();
    const status = searchParams.get('status') || 'all';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');

    await connectDB();

    // Build query for billing records
    let query: any = {
      superAdminId: user.userId,
      billingCycle
    };

    if (adminId) {
      query.adminId = adminId;
    }

    if (status !== 'all') {
      query.status = status;
    }

    // Get billing records with pagination
    const billingRecords = await BillingRecord.find(query)
      .populate('adminId', 'name email companyName orderRate')
      .populate('userId', 'name email')
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .lean()
      .catch(err => {
        console.error('Error fetching billing records:', err);
        return [];
      });

    const total = await BillingRecord.countDocuments(query).catch(err => {
      console.error('Error counting billing records:', err);
      return 0;
    });

    // Get billing summaries for the cycle
    let summaryQuery: any = {
      superAdminId: user.userId,
      billingCycle
    };

    if (adminId) {
      summaryQuery.adminId = adminId;
    }

    const billingSummaries = await BillingSummary.find(summaryQuery)
      .populate('adminId', 'name email companyName orderRate')
      .sort({ createdAt: -1 })
      .lean()
      .catch(err => {
        console.error('Error fetching billing summaries:', err);
        return [];
      });

    // Calculate totals
    const totals = await BillingRecord.aggregate([
      {
        $match: query
      },
      {
        $group: {
          _id: null,
          totalOrders: { $sum: 1 },
          totalAmount: { $sum: '$amount' },
          pendingAmount: {
            $sum: {
              $cond: [{ $eq: ['$status', 'pending'] }, '$amount', 0]
            }
          },
          billedAmount: {
            $sum: {
              $cond: [{ $eq: ['$status', 'billed'] }, '$amount', 0]
            }
          },
          paidAmount: {
            $sum: {
              $cond: [{ $eq: ['$status', 'paid'] }, '$amount', 0]
            }
          }
        }
      }
    ]).catch(err => {
      console.error('Error calculating billing totals:', err);
      return [{ totalOrders: 0, totalAmount: 0, pendingAmount: 0, billedAmount: 0, paidAmount: 0 }];
    });

    const summary = totals[0] || {
      totalOrders: 0,
      totalAmount: 0,
      pendingAmount: 0,
      billedAmount: 0,
      paidAmount: 0
    };

    // Add totalRevenue for frontend compatibility
    summary.totalRevenue = summary.totalAmount;

    return Response.json({
      success: true,
      data: {
        billingRecords,
        billingSummaries,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        },
        summary,
        filters: {
          billingCycle,
          adminId,
          status
        }
      }
    });

  } catch (error) {
    console.error('Get Billing Data Error:', error);
    return Response.json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// POST: Generate billing summaries for a cycle
export async function POST(request: Request) {
  try {
    // Verify super admin access
    const { user, hasAccess } = await requireSuperAdmin(request);
    
    if (!hasAccess || !user) {
      return Response.json({ error: 'Unauthorized - Super Admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const { billingCycle, adminIds } = body;

    if (!billingCycle) {
      return Response.json({ error: 'Billing cycle is required' }, { status: 400 });
    }

    await connectDB();

    // Get admins to generate billing for
    let adminQuery: any = {
      superAdminId: user.userId,
      role: 'admin',
      isActive: true
    };

    if (adminIds && adminIds.length > 0) {
      adminQuery._id = { $in: adminIds };
    }

    const admins = await User.find(adminQuery);

    const generatedSummaries = [];
    const errors = [];

    for (const admin of admins) {
      try {
        // Check if summary already exists
        const existingSummary = await BillingSummary.findOne({
          adminId: admin._id,
          billingCycle
        });

        if (existingSummary) {
          console.log(`Billing summary already exists for ${admin.name} - ${billingCycle}`);
          continue;
        }

        // Get billing records for this admin and cycle
        const billingRecords = await BillingRecord.find({
          adminId: admin._id,
          billingCycle,
          status: 'pending'
        });

        if (billingRecords.length === 0) {
          console.log(`No pending billing records for ${admin.name} - ${billingCycle}`);
          continue;
        }

        const totalOrders = billingRecords.length;
        const totalAmount = billingRecords.reduce((sum, record) => sum + record.amount, 0);

        // Create billing summary
        const billingSummary = new BillingSummary({
          adminId: admin._id,
          superAdminId: user.userId,
          billingCycle,
          totalOrders,
          totalAmount,
          orderRate: admin.orderRate || 0,
          status: 'generated',
          generatedAt: new Date(),
          dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        });

        await billingSummary.save();

        // Update billing records status
        await BillingRecord.updateMany(
          {
            adminId: admin._id,
            billingCycle,
            status: 'pending'
          },
          { status: 'billed' }
        );

        generatedSummaries.push({
          adminId: admin._id,
          adminName: admin.name,
          adminEmail: admin.email,
          totalOrders,
          totalAmount,
          billingCycle
        });

        console.log(`✅ Billing summary generated for ${admin.name}: ${totalOrders} orders, ₹${totalAmount}`);

      } catch (adminError) {
        console.error(`Error generating billing for ${admin.name}:`, adminError);
        errors.push({
          adminId: admin._id,
          adminName: admin.name,
          error: adminError instanceof Error ? adminError.message : 'Unknown error'
        });
      }
    }

    return Response.json({
      success: true,
      message: `Generated ${generatedSummaries.length} billing summaries`,
      data: {
        generated: generatedSummaries,
        errors,
        billingCycle
      }
    });

  } catch (error) {
    console.error('Generate Billing Error:', error);
    return Response.json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// PUT: Update billing summary status
export async function PUT(request: Request) {
  try {
    // Verify super admin access
    const { user, hasAccess } = await requireSuperAdmin(request);
    
    if (!hasAccess || !user) {
      return Response.json({ error: 'Unauthorized - Super Admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const { summaryId, status, paymentMethod, paymentReference, notes } = body;

    if (!summaryId || !status) {
      return Response.json({ error: 'Summary ID and status are required' }, { status: 400 });
    }

    await connectDB();

    // Find and verify billing summary
    const billingSummary = await BillingSummary.findOne({
      _id: summaryId,
      superAdminId: user.userId
    });

    if (!billingSummary) {
      return Response.json({ error: 'Billing summary not found' }, { status: 404 });
    }

    // Update summary
    const updateData: any = { status };

    if (status === 'sent') {
      updateData.sentAt = new Date();
    } else if (status === 'paid') {
      updateData.paidAt = new Date();
      updateData.paymentMethod = paymentMethod;
      updateData.paymentReference = paymentReference;
      
      // Update billing records to paid
      await BillingRecord.updateMany(
        {
          adminId: billingSummary.adminId,
          billingCycle: billingSummary.billingCycle,
          status: 'billed'
        },
        { status: 'paid' }
      );

      // Create payment record
      if (paymentMethod && paymentReference) {
        const paymentRecord = new PaymentRecord({
          adminId: billingSummary.adminId,
          superAdminId: user.userId,
          billingSummaryId: summaryId,
          amount: billingSummary.totalAmount,
          paymentMethod,
          paymentReference,
          paymentDate: new Date(),
          status: 'completed'
        });

        await paymentRecord.save();
      }
    }

    if (notes) {
      updateData.notes = notes;
    }

    const updatedSummary = await BillingSummary.findByIdAndUpdate(
      summaryId,
      updateData,
      { new: true }
    ).populate('adminId', 'name email companyName');

    console.log(`💰 Billing summary updated: ${billingSummary.adminId} - ${status}`);

    return Response.json({
      success: true,
      message: 'Billing summary updated successfully',
      data: updatedSummary
    });

  } catch (error) {
    console.error('Update Billing Summary Error:', error);
    return Response.json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
