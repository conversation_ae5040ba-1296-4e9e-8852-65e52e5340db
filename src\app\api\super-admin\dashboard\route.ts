// Using standard Request for Next.js 15 compatibility
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { BillingRecord, BillingSummary } from '@/models/Billing';
import { OrderResponse } from '@/models/OrderModel';
import { requireSuperAdmin, getCurrentBillingCycle, getPreviousBillingCycle } from '@/middleware/auth';
import { withCache, cacheKeys } from '@/lib/cache';

export async function GET(request: Request) {
  try {
    console.log('🔍 Super Admin Dashboard API called');

    // Verify super admin access
    const { user, hasAccess } = await requireSuperAdmin(request);

    console.log('👤 Super Admin auth result:', { user: user?.email, hasAccess });

    if (!hasAccess || !user) {
      console.log('❌ Super Admin access denied');
      return Response.json({ error: 'Unauthorized - Super Admin access required' }, { status: 403 });
    }

    await connectDB();
    console.log('✅ Database connected');

    // Use cache for dashboard data
    const cacheKey = cacheKeys.superAdminDashboard();

    const dashboardData = await withCache(cacheKey, async () => {
      // Get current and previous billing cycles
      const currentCycle = getCurrentBillingCycle();
      const previousCycle = getPreviousBillingCycle();

      // Use Promise.all for parallel database queries to improve performance
      const [admins, currentMonthBilling, previousMonthBilling] = await Promise.all([
        // 1. Get all admins under this super admin
        User.find({
          superAdminId: user.userId,
          role: 'admin',
          isActive: true
        }).select('-password').lean(),

        // 2. Get billing summary for current month
        BillingSummary.find({
          superAdminId: user.userId,
          billingCycle: currentCycle
        }).populate('adminId', 'name email companyName').lean(),

        // 3. Get billing summary for previous month
        BillingSummary.find({
          superAdminId: user.userId,
          billingCycle: previousCycle
        }).populate('adminId', 'name email companyName').lean()
      ]);

      console.log(`📊 Found ${admins.length} admins under super admin`);

      // 4. Get all users under all admins (only if there are admins)
      const adminIds = admins.map(admin => admin._id);
      const users = adminIds.length > 0 ? await User.find({
        adminId: { $in: adminIds },
        role: 'user',
        isActive: true
      }).select('-password').lean() : [];

      console.log(`👥 Found ${users.length} users under admins`);

      return {
        admins,
        users,
        currentMonthBilling,
        previousMonthBilling,
        currentCycle,
        previousCycle
      };
    }, 120); // Cache for 2 minutes

    const { admins, users, currentMonthBilling, previousMonthBilling, currentCycle, previousCycle } = dashboardData;

    // 5. Get recent orders (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentOrders = await OrderResponse.find({
      createdAt: { $gte: thirtyDaysAgo }
    }).sort({ createdAt: -1 }).limit(100).lean();

    // 6. Get billing records for current month
    const currentMonthBillingRecords = await BillingRecord.find({
      superAdminId: user.userId,
      billingCycle: currentCycle
    }).populate('adminId', 'name email companyName')
      .populate('userId', 'name email')
      .sort({ createdAt: -1 })
      .lean();

    // 7. Calculate statistics
    const totalAdmins = admins.length;
    const totalUsers = users.length;
    const activeAdmins = admins.filter(admin => {
      const lastLogin = admin.updatedAt || admin.createdAt;
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      return lastLogin >= sevenDaysAgo;
    }).length;

    // Current month statistics
    const currentMonthStats = {
      totalOrders: currentMonthBillingRecords.length,
      totalRevenue: currentMonthBillingRecords.reduce((sum, record) => sum + record.amount, 0),
      pendingAmount: currentMonthBilling
        .filter(bill => bill.status !== 'paid')
        .reduce((sum, bill) => sum + bill.totalAmount, 0),
      paidAmount: currentMonthBilling
        .filter(bill => bill.status === 'paid')
        .reduce((sum, bill) => sum + bill.totalAmount, 0),
    };

    // Previous month statistics for comparison
    const previousMonthStats = {
      totalOrders: await BillingRecord.countDocuments({
        superAdminId: user.userId,
        billingCycle: previousCycle
      }),
      totalRevenue: previousMonthBilling.reduce((sum, bill) => sum + bill.totalAmount, 0),
    };

    // Growth calculations
    const orderGrowth = previousMonthStats.totalOrders > 0 
      ? ((currentMonthStats.totalOrders - previousMonthStats.totalOrders) / previousMonthStats.totalOrders) * 100
      : 0;

    const revenueGrowth = previousMonthStats.totalRevenue > 0
      ? ((currentMonthStats.totalRevenue - previousMonthStats.totalRevenue) / previousMonthStats.totalRevenue) * 100
      : 0;

    // Top performing admins (by order volume)
    const adminPerformance = await BillingRecord.aggregate([
      {
        $match: {
          superAdminId: user.userId,
          billingCycle: currentCycle
        }
      },
      {
        $group: {
          _id: '$adminId',
          totalOrders: { $sum: 1 },
          totalRevenue: { $sum: '$amount' }
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'admin'
        }
      },
      {
        $unwind: '$admin'
      },
      {
        $project: {
          adminId: '$_id',
          adminName: '$admin.name',
          adminEmail: '$admin.email',
          companyName: '$admin.companyName',
          totalOrders: 1,
          totalRevenue: 1
        }
      },
      {
        $sort: { totalOrders: -1 }
      },
      {
        $limit: 10
      }
    ]);

    // Recent activity (orders by day for last 30 days)
    const dailyOrderStats = await BillingRecord.aggregate([
      {
        $match: {
          superAdminId: user.userId,
          createdAt: { $gte: thirtyDaysAgo }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$createdAt' }
          },
          orders: { $sum: 1 },
          revenue: { $sum: '$amount' }
        }
      },
      {
        $sort: { '_id': 1 }
      }
    ]);

    return Response.json({
      success: true,
      data: {
        overview: {
          totalAdmins,
          totalUsers,
          activeAdmins,
          currentMonth: currentMonthStats,
          previousMonth: previousMonthStats,
          growth: {
            orders: orderGrowth,
            revenue: revenueGrowth
          }
        },
        admins: admins.map(admin => ({
          ...admin,
          userCount: users.filter(user => user.adminId?.toString() === (admin as any)._id.toString()).length
        })),
        currentMonthBilling,
        previousMonthBilling,
        recentOrders: recentOrders.slice(0, 20), // Limit to 20 most recent
        adminPerformance,
        dailyOrderStats,
        billingCycles: {
          current: currentCycle,
          previous: previousCycle
        }
      }
    });

  } catch (error) {
    console.error('Super Admin Dashboard Error:', error);
    return Response.json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
