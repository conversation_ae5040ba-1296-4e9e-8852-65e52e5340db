// Using standard Request for Next.js 15 compatibility
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { requireSuperAdmin, generateInvitationToken } from '@/middleware/auth';
import bcrypt from 'bcryptjs';
import { sendAdminInvitationEmail } from '@/lib/email';

export async function POST(request: Request) {
  try {
    // Verify super admin access
    const { user, hasAccess } = await requireSuperAdmin(request);
    
    if (!hasAccess || !user) {
      return Response.json({ error: 'Unauthorized - Super Admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const { 
      email, 
      name, 
      companyName, 
      phoneNumber, 
      address, 
      orderRate,
      temporaryPassword 
    } = body;

    // Validate required fields
    if (!email || !name || !orderRate || !temporaryPassword) {
      return Response.json({ 
        error: 'Missing required fields: email, name, orderRate, temporaryPassword' 
      }, { status: 400 });
    }

    // Validate order rate
    if (orderRate < 0 || orderRate > 1000) {
      return Response.json({ 
        error: 'Order rate must be between 0 and 1000' 
      }, { status: 400 });
    }

    await connectDB();

    // Check if user already exists
    const existingUser = await User.findOne({ email: email.toLowerCase() });
    if (existingUser) {
      return Response.json({ 
        error: 'User with this email already exists' 
      }, { status: 409 });
    }

    // Generate invitation token
    const invitationToken = generateInvitationToken();
    const invitationExpiry = new Date();
    invitationExpiry.setDate(invitationExpiry.getDate() + 7); // 7 days to accept

    // Hash temporary password
    const salt = await bcrypt.genSalt(12);
    const hashedPassword = await bcrypt.hash(temporaryPassword, salt);

    // Create admin user
    const newAdmin = new User({
      email: email.toLowerCase(),
      password: hashedPassword,
      name,
      role: 'admin',
      companyName,
      phoneNumber,
      address,
      orderRate,
      superAdminId: user.userId,
      invitedBy: user.userId,
      invitationToken,
      invitationExpiry,
      isInvitationAccepted: false,
      isActive: true, // Admin is active immediately but needs to accept invitation
      totalOrders: 0,
      totalBilling: 0,
      lastBillingDate: new Date(),
    });

    await newAdmin.save();

    // Log the invitation
    console.log(`📧 Admin invitation created:`, {
      adminId: newAdmin._id,
      email: newAdmin.email,
      name: newAdmin.name,
      companyName: newAdmin.companyName,
      orderRate: newAdmin.orderRate,
      invitedBy: user.name,
      invitationExpiry
    });

    // Send invitation email
    const invitationLink = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/accept-invitation/${invitationToken}`;

    const emailResult = await sendAdminInvitationEmail({
      email: newAdmin.email,
      name: newAdmin.name,
      companyName: newAdmin.companyName || 'Your Company',
      temporaryPassword,
      invitationLink,
      invitedBy: user.name,
      orderRate: newAdmin.orderRate || 0
    });

    console.log('📧 Email send result:', emailResult);

    return Response.json({
      success: true,
      message: 'Admin invitation created successfully',
      data: {
        adminId: newAdmin._id,
        email: newAdmin.email,
        name: newAdmin.name,
        companyName: newAdmin.companyName,
        orderRate: newAdmin.orderRate,
        invitationToken,
        invitationLink,
        invitationExpiry,
        emailSent: emailResult.success,
        emailMessage: emailResult.message || (emailResult.success ? 'Email sent successfully' : 'Email failed to send'),
        temporaryPassword, // Include for testing - remove in production
      }
    });

  } catch (error) {
    console.error('Admin Invitation Error:', error);
    return Response.json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// GET: List all pending invitations
export async function GET(request: Request) {
  try {
    // Verify super admin access
    const { user, hasAccess } = await requireSuperAdmin(request);
    
    if (!hasAccess || !user) {
      return Response.json({ error: 'Unauthorized - Super Admin access required' }, { status: 403 });
    }

    await connectDB();

    // Get all admins invited by this super admin
    const invitations = await User.find({
      superAdminId: user.userId,
      role: 'admin',
      invitedBy: user.userId
    }).select('-password').sort({ createdAt: -1 });

    // Categorize invitations
    const pending = invitations.filter(inv => 
      !inv.isInvitationAccepted && 
      inv.invitationExpiry && 
      inv.invitationExpiry > new Date()
    );

    const accepted = invitations.filter(inv => inv.isInvitationAccepted);

    const expired = invitations.filter(inv => 
      !inv.isInvitationAccepted && 
      inv.invitationExpiry && 
      inv.invitationExpiry <= new Date()
    );

    return Response.json({
      success: true,
      data: {
        total: invitations.length,
        pending: pending.length,
        accepted: accepted.length,
        expired: expired.length,
        invitations: {
          pending,
          accepted,
          expired
        }
      }
    });

  } catch (error) {
    console.error('Get Invitations Error:', error);
    return Response.json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// DELETE: Cancel/revoke an invitation
export async function DELETE(request: Request) {
  try {
    // Verify super admin access
    const { user, hasAccess } = await requireSuperAdmin(request);
    
    if (!hasAccess || !user) {
      return Response.json({ error: 'Unauthorized - Super Admin access required' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const adminId = searchParams.get('adminId');

    if (!adminId) {
      return Response.json({ error: 'Admin ID is required' }, { status: 400 });
    }

    await connectDB();

    // Find the admin and verify ownership
    const admin = await User.findOne({
      _id: adminId,
      superAdminId: user.userId,
      role: 'admin',
      invitedBy: user.userId
    });

    if (!admin) {
      return Response.json({ error: 'Admin not found or not authorized' }, { status: 404 });
    }

    // Check if invitation is already accepted
    if (admin.isInvitationAccepted) {
      return Response.json({ 
        error: 'Cannot revoke accepted invitation. Deactivate admin instead.' 
      }, { status: 400 });
    }

    // Delete the admin (since invitation not accepted yet)
    await User.deleteOne({ _id: adminId });

    console.log(`🗑️ Admin invitation revoked:`, {
      adminId,
      email: admin.email,
      name: admin.name,
      revokedBy: user.name
    });

    return Response.json({
      success: true,
      message: 'Admin invitation revoked successfully'
    });

  } catch (error) {
    console.error('Revoke Invitation Error:', error);
    return Response.json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
