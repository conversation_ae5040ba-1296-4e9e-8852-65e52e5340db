import connectDB from '@/lib/mongodb';
import { OrderResponse, SimpleOrder } from '@/models/OrderModel';
import BatchOrderHistory from '@/models/BatchOrderHistory';
import { BillingRecord } from '@/models/Billing';
import { requireSuperAdmin } from '@/middleware/auth';

// POST: Delete orders before a specific cutoff time
export async function POST(request: Request) {
  try {
    const { user, hasAccess } = await requireSuperAdmin(request);
    
    if (!hasAccess || !user) {
      return Response.json({ error: 'Unauthorized - Super Admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const { cutoffDate, orderTypes, includeSuccessful, includeFailed, dryRun = false } = body;

    if (!cutoffDate) {
      return Response.json({ error: 'Cutoff date is required' }, { status: 400 });
    }

    const cutoff = new Date(cutoffDate);
    if (isNaN(cutoff.getTime())) {
      return Response.json({ error: 'Invalid cutoff date format' }, { status: 400 });
    }

    await connectDB();

    let deletionSummary = {
      orderResponses: 0,
      simpleOrders: 0,
      batchOrderHistory: 0,
      billingRecords: 0,
      totalDeleted: 0,
      spaceSaved: 0
    };

    // Build query for order types
    let orderTypeQuery: any = {};
    if (orderTypes && orderTypes.length > 0) {
      orderTypeQuery = { orderType: { $in: orderTypes } };
    }

    // Build query for success/failure status
    let statusQuery: any = {};
    if (includeSuccessful === false && includeFailed === true) {
      statusQuery = { 'details.status': false };
    } else if (includeSuccessful === true && includeFailed === false) {
      statusQuery = { 'details.status': true };
    }

    // Query for OrderResponse collection
    const orderResponseQuery = {
      createdAt: { $lt: cutoff },
      ...orderTypeQuery,
      ...statusQuery
    };

    // Query for SimpleOrder collection
    const simpleOrderQuery = {
      createdAt: { $lt: cutoff },
      ...orderTypeQuery
    };

    // Query for BatchOrderHistory collection
    const batchOrderQuery = {
      executedAt: { $lt: cutoff },
      ...orderTypeQuery
    };

    // Query for BillingRecord collection (only delete paid/disputed records)
    const billingRecordQuery = {
      createdAt: { $lt: cutoff },
      status: { $in: ['paid', 'disputed'] },
      superAdminId: user.userId
    };

    if (dryRun) {
      // Count documents that would be deleted
      const [orderResponseCount, simpleOrderCount, batchOrderCount, billingRecordCount] = await Promise.all([
        OrderResponse.countDocuments(orderResponseQuery),
        SimpleOrder.countDocuments(simpleOrderQuery),
        BatchOrderHistory.countDocuments(batchOrderQuery),
        BillingRecord.countDocuments(billingRecordQuery)
      ]);

      // Estimate space saved (rough calculation)
      const avgOrderSize = 2048; // 2KB per order record
      const avgBillingSize = 512; // 512B per billing record
      const avgBatchSize = 4096; // 4KB per batch record

      const estimatedSpace = 
        (orderResponseCount * avgOrderSize) +
        (simpleOrderCount * avgOrderSize) +
        (batchOrderCount * avgBatchSize) +
        (billingRecordCount * avgBillingSize);

      return Response.json({
        success: true,
        dryRun: true,
        message: 'Dry run completed - no data was deleted',
        data: {
          cutoffDate: cutoff.toISOString(),
          estimatedDeletions: {
            orderResponses: orderResponseCount,
            simpleOrders: simpleOrderCount,
            batchOrderHistory: batchOrderCount,
            billingRecords: billingRecordCount,
            total: orderResponseCount + simpleOrderCount + batchOrderCount + billingRecordCount
          },
          estimatedSpaceSaved: `${(estimatedSpace / 1024 / 1024).toFixed(2)} MB`,
          query: {
            orderResponseQuery,
            simpleOrderQuery,
            batchOrderQuery,
            billingRecordQuery
          }
        }
      });
    }

    // Perform actual deletion
    console.log(`🗑️ Starting order cleanup for orders before ${cutoff.toISOString()}`);

    // Delete OrderResponse records
    const orderResponseResult = await OrderResponse.deleteMany(orderResponseQuery);
    deletionSummary.orderResponses = orderResponseResult.deletedCount || 0;
    console.log(`✅ Deleted ${deletionSummary.orderResponses} OrderResponse records`);

    // Delete SimpleOrder records
    const simpleOrderResult = await SimpleOrder.deleteMany(simpleOrderQuery);
    deletionSummary.simpleOrders = simpleOrderResult.deletedCount || 0;
    console.log(`✅ Deleted ${deletionSummary.simpleOrders} SimpleOrder records`);

    // Delete BatchOrderHistory records
    const batchOrderResult = await BatchOrderHistory.deleteMany(batchOrderQuery);
    deletionSummary.batchOrderHistory = batchOrderResult.deletedCount || 0;
    console.log(`✅ Deleted ${deletionSummary.batchOrderHistory} BatchOrderHistory records`);

    // Delete BillingRecord records (only paid/disputed)
    const billingRecordResult = await BillingRecord.deleteMany(billingRecordQuery);
    deletionSummary.billingRecords = billingRecordResult.deletedCount || 0;
    console.log(`✅ Deleted ${deletionSummary.billingRecords} BillingRecord records`);

    // Calculate totals
    deletionSummary.totalDeleted = 
      deletionSummary.orderResponses + 
      deletionSummary.simpleOrders + 
      deletionSummary.batchOrderHistory + 
      deletionSummary.billingRecords;

    // Estimate space saved
    const avgOrderSize = 2048;
    const avgBillingSize = 512;
    const avgBatchSize = 4096;

    deletionSummary.spaceSaved = 
      (deletionSummary.orderResponses * avgOrderSize) +
      (deletionSummary.simpleOrders * avgOrderSize) +
      (deletionSummary.batchOrderHistory * avgBatchSize) +
      (deletionSummary.billingRecords * avgBillingSize);

    console.log(`🎉 Order cleanup completed: ${deletionSummary.totalDeleted} records deleted`);

    return Response.json({
      success: true,
      message: `Successfully deleted ${deletionSummary.totalDeleted} order records`,
      data: {
        cutoffDate: cutoff.toISOString(),
        deletionSummary,
        spaceSavedMB: (deletionSummary.spaceSaved / 1024 / 1024).toFixed(2),
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('❌ Order cleanup error:', error);
    return Response.json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// GET: Get cleanup preview/statistics
export async function GET(request: Request) {
  try {
    const { user, hasAccess } = await requireSuperAdmin(request);
    
    if (!hasAccess || !user) {
      return Response.json({ error: 'Unauthorized - Super Admin access required' }, { status: 403 });
    }

    const url = new URL(request.url);
    const cutoffDate = url.searchParams.get('cutoffDate');
    const days = parseInt(url.searchParams.get('days') || '30');

    await connectDB();

    // Default cutoff to 30 days ago if not provided
    const cutoff = cutoffDate ? new Date(cutoffDate) : new Date(Date.now() - days * 24 * 60 * 60 * 1000);

    // Get statistics for different time periods
    const [
      totalOrderResponses,
      totalSimpleOrders,
      totalBatchHistory,
      totalBillingRecords,
      oldOrderResponses,
      oldSimpleOrders,
      oldBatchHistory,
      oldBillingRecords
    ] = await Promise.all([
      OrderResponse.countDocuments(),
      SimpleOrder.countDocuments(),
      BatchOrderHistory.countDocuments(),
      BillingRecord.countDocuments({ superAdminId: user.userId }),
      OrderResponse.countDocuments({ createdAt: { $lt: cutoff } }),
      SimpleOrder.countDocuments({ createdAt: { $lt: cutoff } }),
      BatchOrderHistory.countDocuments({ executedAt: { $lt: cutoff } }),
      BillingRecord.countDocuments({ 
        createdAt: { $lt: cutoff }, 
        status: { $in: ['paid', 'disputed'] },
        superAdminId: user.userId 
      })
    ]);

    // Calculate storage estimates
    const avgOrderSize = 2048;
    const avgBillingSize = 512;
    const avgBatchSize = 4096;

    const currentStorage = 
      (totalOrderResponses * avgOrderSize) +
      (totalSimpleOrders * avgOrderSize) +
      (totalBatchHistory * avgBatchSize) +
      (totalBillingRecords * avgBillingSize);

    const cleanupableStorage = 
      (oldOrderResponses * avgOrderSize) +
      (oldSimpleOrders * avgOrderSize) +
      (oldBatchHistory * avgBatchSize) +
      (oldBillingRecords * avgBillingSize);

    return Response.json({
      success: true,
      data: {
        cutoffDate: cutoff.toISOString(),
        statistics: {
          total: {
            orderResponses: totalOrderResponses,
            simpleOrders: totalSimpleOrders,
            batchHistory: totalBatchHistory,
            billingRecords: totalBillingRecords,
            totalRecords: totalOrderResponses + totalSimpleOrders + totalBatchHistory + totalBillingRecords
          },
          cleanupable: {
            orderResponses: oldOrderResponses,
            simpleOrders: oldSimpleOrders,
            batchHistory: oldBatchHistory,
            billingRecords: oldBillingRecords,
            totalRecords: oldOrderResponses + oldSimpleOrders + oldBatchHistory + oldBillingRecords
          },
          storage: {
            currentStorageMB: (currentStorage / 1024 / 1024).toFixed(2),
            cleanupableStorageMB: (cleanupableStorage / 1024 / 1024).toFixed(2),
            percentageCleanupable: ((cleanupableStorage / currentStorage) * 100).toFixed(1)
          }
        }
      }
    });

  } catch (error) {
    console.error('❌ Error getting cleanup statistics:', error);
    return Response.json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
