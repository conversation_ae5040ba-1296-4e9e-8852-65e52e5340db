import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { BillingRecord, BillingSummary } from '@/models/Billing';
import { requireSuperAdmin, getCurrentBillingCycle, getPreviousBillingCycle } from '@/middleware/auth';

// GET: Get settlement overview
export async function GET(request: Request) {
  try {
    const { user, hasAccess } = await requireSuperAdmin(request);
    
    if (!hasAccess || !user) {
      return Response.json({ error: 'Unauthorized - Super Admin access required' }, { status: 403 });
    }

    const url = new URL(request.url);
    const billingCycle = url.searchParams.get('billingCycle') || getCurrentBillingCycle();

    await connectDB();

    // Get all admins under this super admin
    const admins = await User.find({
      superAdminId: user.userId,
      role: 'admin',
      isActive: true
    }).select('name email companyName orderRate').lean();

    const adminIds = admins.map(admin => admin._id);

    // Get billing summaries for the cycle
    const billingSummaries = await BillingSummary.find({
      adminId: { $in: adminIds },
      billingCycle
    }).populate('adminId', 'name email companyName orderRate').lean();

    // Get overall statistics
    const overallStats = await BillingSummary.aggregate([
      {
        $match: {
          adminId: { $in: adminIds },
          billingCycle
        }
      },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalAmount: { $sum: '$totalAmount' },
          totalOrders: { $sum: '$totalOrders' }
        }
      }
    ]);

    // Get pending billing records (not yet billed)
    const pendingRecords = await BillingRecord.aggregate([
      {
        $match: {
          adminId: { $in: adminIds },
          billingCycle,
          status: 'pending'
        }
      },
      {
        $group: {
          _id: '$adminId',
          pendingOrders: { $sum: 1 },
          pendingAmount: { $sum: '$amount' }
        }
      }
    ]);

    // Calculate settlement data for each admin
    const settlementData = admins.map(admin => {
      const summary = billingSummaries.find(bs => bs.adminId._id.toString() === (admin as any)._id.toString());
      const pending = pendingRecords.find(pr => pr._id.toString() === (admin as any)._id.toString());

      return {
        adminId: (admin as any)._id,
        adminName: (admin as any).name,
        adminEmail: (admin as any).email,
        companyName: (admin as any).companyName,
        orderRate: (admin as any).orderRate || 0,
        billing: {
          status: summary?.status || 'no_billing',
          totalOrders: summary?.totalOrders || 0,
          totalAmount: summary?.totalAmount || 0,
          generatedAt: summary?.generatedAt,
          paidAt: summary?.paidAt,
          dueDate: summary?.dueDate,
          paymentMethod: summary?.paymentMethod,
          paymentReference: summary?.paymentReference
        },
        pending: {
          pendingOrders: pending?.pendingOrders || 0,
          pendingAmount: pending?.pendingAmount || 0
        }
      };
    });

    // Calculate totals by status
    const statusTotals = overallStats.reduce((acc, stat) => {
      acc[stat._id] = {
        count: stat.count,
        totalAmount: stat.totalAmount,
        totalOrders: stat.totalOrders
      };
      return acc;
    }, {} as any);

    // Calculate overall totals
    const grandTotals = {
      totalAdmins: admins.length,
      totalBilled: overallStats.reduce((sum, stat) => sum + stat.totalAmount, 0),
      totalOrders: overallStats.reduce((sum, stat) => sum + stat.totalOrders, 0),
      totalPending: pendingRecords.reduce((sum, pr) => sum + pr.pendingAmount, 0),
      pendingOrders: pendingRecords.reduce((sum, pr) => sum + pr.pendingOrders, 0)
    };

    return Response.json({
      success: true,
      data: {
        billingCycle,
        settlementData,
        statusTotals,
        grandTotals,
        availableCycles: [
          getCurrentBillingCycle(),
          getPreviousBillingCycle()
        ]
      }
    });

  } catch (error) {
    console.error('❌ Error fetching settlement data:', error);
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST: Generate settlement report
export async function POST(request: Request) {
  try {
    const { user, hasAccess } = await requireSuperAdmin(request);
    
    if (!hasAccess || !user) {
      return Response.json({ error: 'Unauthorized - Super Admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const { billingCycle, adminIds, action } = body;

    if (!billingCycle) {
      return Response.json({ error: 'Billing cycle is required' }, { status: 400 });
    }

    await connectDB();

    let results = [];

    if (action === 'generate_all_bills') {
      // Generate bills for all pending records
      let adminQuery: any = {
        superAdminId: user.userId,
        role: 'admin',
        isActive: true
      };

      if (adminIds && adminIds.length > 0) {
        adminQuery._id = { $in: adminIds };
      }

      const admins = await User.find(adminQuery);

      for (const admin of admins) {
        // Check if billing summary already exists
        const existingSummary = await BillingSummary.findOne({
          adminId: admin._id,
          billingCycle
        });

        if (existingSummary) {
          results.push({
            adminId: admin._id,
            adminName: admin.name,
            status: 'already_exists',
            message: 'Billing summary already exists'
          });
          continue;
        }

        // Get pending billing records
        const billingRecords = await BillingRecord.find({
          adminId: admin._id,
          billingCycle,
          status: 'pending'
        });

        if (billingRecords.length === 0) {
          results.push({
            adminId: admin._id,
            adminName: admin.name,
            status: 'no_records',
            message: 'No pending billing records found'
          });
          continue;
        }

        const totalOrders = billingRecords.length;
        const totalAmount = billingRecords.reduce((sum, record) => sum + record.amount, 0);

        // Create billing summary
        const billingSummary = new BillingSummary({
          adminId: admin._id,
          superAdminId: user.userId,
          billingCycle,
          totalOrders,
          totalAmount,
          orderRate: admin.orderRate || 0,
          status: 'generated',
          generatedAt: new Date(),
          dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        });

        await billingSummary.save();

        // Update billing records status
        await BillingRecord.updateMany(
          {
            adminId: admin._id,
            billingCycle,
            status: 'pending'
          },
          { status: 'billed' }
        );

        results.push({
          adminId: admin._id,
          adminName: admin.name,
          status: 'generated',
          totalOrders,
          totalAmount,
          message: 'Billing summary generated successfully'
        });
      }

    } else if (action === 'mark_all_paid') {
      // Mark all generated bills as paid
      const summaries = await BillingSummary.find({
        superAdminId: user.userId,
        billingCycle,
        status: { $in: ['generated', 'sent'] },
        ...(adminIds && adminIds.length > 0 && { adminId: { $in: adminIds } })
      }).populate('adminId', 'name email');

      for (const summary of summaries) {
        await BillingSummary.findByIdAndUpdate(summary._id, {
          status: 'paid',
          paidAt: new Date(),
          paymentMethod: 'bulk_settlement',
          paymentReference: `BULK_${Date.now()}`,
          notes: 'Marked as paid via bulk settlement'
        });

        // Mark related billing records as paid
        await BillingRecord.updateMany(
          {
            adminId: summary.adminId,
            billingCycle,
            status: 'billed'
          },
          { status: 'paid' }
        );

        results.push({
          adminId: summary.adminId._id,
          adminName: summary.adminId.name,
          status: 'paid',
          totalAmount: summary.totalAmount,
          message: 'Marked as paid'
        });
      }
    }

    console.log(`✅ Settlement action completed: ${action}, ${results.length} admins processed`);

    return Response.json({
      success: true,
      message: `Settlement action completed: ${action}`,
      data: {
        action,
        billingCycle,
        results,
        processedCount: results.length
      }
    });

  } catch (error) {
    console.error('❌ Error processing settlement:', error);
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}
