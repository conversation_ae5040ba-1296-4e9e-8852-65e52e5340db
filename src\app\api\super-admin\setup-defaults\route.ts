import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { requireSuperAdmin } from '@/middleware/auth';

// POST: Setup default rates and fix existing admins
export async function POST(request: Request) {
  try {
    const { user, hasAccess } = await requireSuperAdmin(request);
    
    if (!hasAccess || !user) {
      return Response.json({ error: 'Unauthorized - Super Admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const { defaultOrderRate = 5, fixExistingAdmins = true } = body;

    await connectDB();

    let results = {
      updatedAdmins: [] as any[],
      createdSuperAdminLinks: [] as any[],
      errors: [] as any[]
    };

    if (fixExistingAdmins) {
      // Find all admins that don't have a superAdminId set
      const orphanedAdmins = await User.find({
        role: 'admin',
        $or: [
          { superAdminId: { $exists: false } },
          { superAdminId: null }
        ]
      });

      console.log(`🔍 Found ${orphanedAdmins.length} orphaned admins`);

      // Link them to this super admin and set default rate
      for (const admin of orphanedAdmins) {
        try {
          const updated = await User.findByIdAndUpdate(
            admin._id,
            {
              superAdminId: user.userId,
              orderRate: admin.orderRate || defaultOrderRate,
              totalOrders: admin.totalOrders || 0,
              totalBilling: admin.totalBilling || 0,
              lastBillingDate: admin.lastBillingDate || new Date()
            },
            { new: true }
          );

          results.createdSuperAdminLinks.push({
            adminId: (admin as any)._id,
            adminName: (admin as any).name,
            adminEmail: (admin as any).email,
            oldOrderRate: (admin as any).orderRate,
            newOrderRate: (updated as any)?.orderRate
          });

          console.log(`✅ Linked admin to super admin: ${admin.email}`);
        } catch (error) {
          results.errors.push({
            adminId: (admin as any)._id,
            adminEmail: (admin as any).email,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      // Update existing admins under this super admin with default rate if they don't have one
      const existingAdmins = await User.find({
        superAdminId: user.userId,
        role: 'admin',
        $or: [
          { orderRate: { $exists: false } },
          { orderRate: null },
          { orderRate: 0 }
        ]
      });

      console.log(`🔍 Found ${existingAdmins.length} admins needing default rate`);

      for (const admin of existingAdmins) {
        try {
          const updated = await User.findByIdAndUpdate(
            admin._id,
            {
              orderRate: defaultOrderRate,
              totalOrders: admin.totalOrders || 0,
              totalBilling: admin.totalBilling || 0,
              lastBillingDate: admin.lastBillingDate || new Date()
            },
            { new: true }
          );

          results.updatedAdmins.push({
            adminId: admin._id,
            adminName: admin.name,
            adminEmail: admin.email,
            oldOrderRate: admin.orderRate,
            newOrderRate: updated?.orderRate
          });

          console.log(`✅ Updated admin rate: ${admin.email} -> ${defaultOrderRate}`);
        } catch (error) {
          results.errors.push({
            adminId: admin._id,
            adminEmail: admin.email,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }
    }

    // Get final statistics
    const finalStats = await User.aggregate([
      {
        $match: {
          superAdminId: user.userId,
          role: 'admin'
        }
      },
      {
        $group: {
          _id: null,
          totalAdmins: { $sum: 1 },
          activeAdmins: {
            $sum: {
              $cond: [{ $eq: ['$isActive', true] }, 1, 0]
            }
          },
          averageOrderRate: { $avg: '$orderRate' },
          totalOrderRate: { $sum: '$orderRate' }
        }
      }
    ]);

    const stats = finalStats[0] || {
      totalAdmins: 0,
      activeAdmins: 0,
      averageOrderRate: 0,
      totalOrderRate: 0
    };

    console.log(`✅ Setup completed: ${results.updatedAdmins.length + results.createdSuperAdminLinks.length} admins processed`);

    return Response.json({
      success: true,
      message: 'Default setup completed successfully',
      data: {
        defaultOrderRate,
        results,
        finalStats: stats,
        summary: {
          totalProcessed: results.updatedAdmins.length + results.createdSuperAdminLinks.length,
          updatedRates: results.updatedAdmins.length,
          linkedToSuperAdmin: results.createdSuperAdminLinks.length,
          errors: results.errors.length
        }
      }
    });

  } catch (error) {
    console.error('❌ Error setting up defaults:', error);
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// GET: Preview what would be changed
export async function GET(request: Request) {
  try {
    const { user, hasAccess } = await requireSuperAdmin(request);
    
    if (!hasAccess || !user) {
      return Response.json({ error: 'Unauthorized - Super Admin access required' }, { status: 403 });
    }

    const url = new URL(request.url);
    const defaultOrderRate = parseInt(url.searchParams.get('defaultOrderRate') || '5');

    await connectDB();

    // Find orphaned admins
    const orphanedAdmins = await User.find({
      role: 'admin',
      $or: [
        { superAdminId: { $exists: false } },
        { superAdminId: null }
      ]
    }).select('name email orderRate companyName isActive').lean();

    // Find admins under this super admin needing rate update
    const adminsNeedingRate = await User.find({
      superAdminId: user.userId,
      role: 'admin',
      $or: [
        { orderRate: { $exists: false } },
        { orderRate: null },
        { orderRate: 0 }
      ]
    }).select('name email orderRate companyName isActive').lean();

    // Current admins under this super admin
    const currentAdmins = await User.find({
      superAdminId: user.userId,
      role: 'admin'
    }).select('name email orderRate companyName isActive').lean();

    return Response.json({
      success: true,
      data: {
        preview: {
          defaultOrderRate,
          orphanedAdmins: orphanedAdmins.map(admin => ({
            ...admin,
            action: 'link_to_super_admin_and_set_rate',
            newOrderRate: admin.orderRate || defaultOrderRate
          })),
          adminsNeedingRate: adminsNeedingRate.map(admin => ({
            ...admin,
            action: 'set_default_rate',
            newOrderRate: defaultOrderRate
          })),
          currentAdmins: currentAdmins.map(admin => ({
            ...admin,
            action: admin.orderRate ? 'no_change' : 'set_default_rate',
            newOrderRate: admin.orderRate || defaultOrderRate
          }))
        },
        summary: {
          orphanedCount: orphanedAdmins.length,
          needingRateCount: adminsNeedingRate.length,
          currentAdminCount: currentAdmins.length,
          totalChanges: orphanedAdmins.length + adminsNeedingRate.length
        }
      }
    });

  } catch (error) {
    console.error('❌ Error getting setup preview:', error);
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}
