import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { verifyAuth } from '@/middleware/auth';

// GET - Fetch specific user details
export async function GET(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const user = await verifyAuth(request);

    if (!user || user.role !== 'super_admin') {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await connectDB();

    const { id: userId } = await params;
    
    // Find the user and verify they belong to this super admin's hierarchy
    const targetUser = await User.findById(userId).select('-password').populate('adminId', 'name email companyName').lean();
    
    if (!targetUser) {
      return Response.json({ error: 'User not found' }, { status: 404 });
    }

    // Verify user belongs to this super admin's hierarchy
    if ((targetUser as any).role === 'admin') {
      if ((targetUser as any).superAdminId?.toString() !== user.userId) {
        return Response.json({ error: 'Unauthorized to access this admin' }, { status: 403 });
      }
    } else if ((targetUser as any).role === 'user') {
      if ((targetUser as any).adminId) {
        const admin = await User.findById((targetUser as any).adminId);
        if (!admin || (admin as any).superAdminId?.toString() !== user.userId) {
          return Response.json({ error: 'Unauthorized to access this user' }, { status: 403 });
        }
      }
    }

    return Response.json({ user: targetUser });
  } catch (error: any) {
    console.error('Error fetching user:', error);
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PUT - Update user details
export async function PUT(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const user = await verifyAuth(request);

    if (!user || user.role !== 'super_admin') {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await connectDB();

    const { id: userId } = await params;
    const updates = await request.json();
    
    // Find the user and verify they belong to this super admin's hierarchy
    const targetUser = await User.findById(userId);
    
    if (!targetUser) {
      return Response.json({ error: 'User not found' }, { status: 404 });
    }

    // Verify user belongs to this super admin's hierarchy
    if (targetUser.role === 'admin') {
      if (targetUser.superAdminId?.toString() !== user.userId) {
        return Response.json({ error: 'Unauthorized to access this admin' }, { status: 403 });
      }
    } else if (targetUser.role === 'user') {
      if (targetUser.adminId) {
        const admin = await User.findById(targetUser.adminId);
        if (!admin || admin.superAdminId?.toString() !== user.userId) {
          return Response.json({ error: 'Unauthorized to access this user' }, { status: 403 });
        }
      }
    }

    // If changing adminId, verify the new admin belongs to this super admin
    if (updates.adminId && updates.adminId !== targetUser.adminId?.toString()) {
      const newAdmin = await User.findOne({
        _id: updates.adminId,
        superAdminId: user.userId,
        role: 'admin'
      });
      
      if (!newAdmin) {
        return Response.json({ error: 'Invalid admin selected' }, { status: 400 });
      }
    }

    // Remove password from updates if present (should be handled separately)
    delete updates.password;
    delete updates.role; // Prevent role changes
    delete updates.superAdminId; // Prevent hierarchy changes

    // Update the user
    const updatedUser = await User.findByIdAndUpdate(
      userId,
      { $set: updates },
      { new: true, runValidators: true }
    ).select('-password').populate('adminId', 'name email companyName');

    console.log(`✅ User updated: ${updatedUser.email}`);

    return Response.json({
      message: 'User updated successfully',
      user: updatedUser
    });
  } catch (error: any) {
    console.error('Error updating user:', error);
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// DELETE - Delete user
export async function DELETE(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const user = await verifyAuth(request);

    if (!user || user.role !== 'super_admin') {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await connectDB();

    const { id: userId } = await params;
    
    // Find the user and verify they belong to this super admin's hierarchy
    const targetUser = await User.findById(userId);
    
    if (!targetUser) {
      return Response.json({ error: 'User not found' }, { status: 404 });
    }

    // Verify user belongs to this super admin's hierarchy
    if (targetUser.role === 'admin') {
      if (targetUser.superAdminId?.toString() !== user.userId) {
        return Response.json({ error: 'Unauthorized to access this admin' }, { status: 403 });
      }
      
      // Check if admin has users under them
      const usersUnderAdmin = await User.countDocuments({ adminId: userId, role: 'user' });
      if (usersUnderAdmin > 0) {
        return Response.json({ 
          error: `Cannot delete admin. ${usersUnderAdmin} users are assigned to this admin. Please reassign or delete them first.` 
        }, { status: 400 });
      }
    } else if (targetUser.role === 'user') {
      if (targetUser.adminId) {
        const admin = await User.findById(targetUser.adminId);
        if (!admin || admin.superAdminId?.toString() !== user.userId) {
          return Response.json({ error: 'Unauthorized to access this user' }, { status: 403 });
        }
      }
    }

    // Delete the user
    await User.findByIdAndDelete(userId);

    console.log(`✅ User deleted: ${targetUser.email}`);

    return Response.json({ message: 'User deleted successfully' });
  } catch (error: any) {
    console.error('Error deleting user:', error);
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}
