
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { verifyAuth } from '@/middleware/auth';

// POST - Bulk operations on users
export async function POST(request: Request) {
  try {
    const user = await verifyAuth(request);
    
    if (!user || user.role !== 'super_admin') {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await connectDB();
    
    const { action, userIds, adminId } = await request.json();

    if (!action || !userIds || !Array.isArray(userIds)) {
      return Response.json({ error: 'Action and userIds array are required' }, { status: 400 });
    }

    let result;

    switch (action) {
      case 'assign_admin':
        if (!adminId) {
          return Response.json({ error: 'Admin ID is required for assignment' }, { status: 400 });
        }

        // Verify admin belongs to this super admin
        const admin = await User.findOne({
          _id: adminId,
          superAdminId: user.userId,
          role: 'admin'
        });
        
        if (!admin) {
          return Response.json({ error: 'Invalid admin selected' }, { status: 400 });
        }

        // Update users to assign them to the admin
        result = await User.updateMany(
          { 
            _id: { $in: userIds },
            role: 'user'
          },
          { $set: { adminId: adminId } }
        );

        console.log(`✅ Assigned ${result.modifiedCount} users to admin: ${admin.name}`);
        
        return Response.json({
          message: `Successfully assigned ${result.modifiedCount} users to ${admin.name}`,
          modifiedCount: result.modifiedCount
        });

      case 'unassign_admin':
        // Remove admin assignment from users
        result = await User.updateMany(
          { 
            _id: { $in: userIds },
            role: 'user'
          },
          { $unset: { adminId: 1 } }
        );

        console.log(`✅ Unassigned ${result.modifiedCount} users from their admins`);
        
        return Response.json({
          message: `Successfully unassigned ${result.modifiedCount} users from their admins`,
          modifiedCount: result.modifiedCount
        });

      case 'activate':
        // Activate users
        result = await User.updateMany(
          { _id: { $in: userIds } },
          { $set: { isActive: true } }
        );

        console.log(`✅ Activated ${result.modifiedCount} users`);
        
        return Response.json({
          message: `Successfully activated ${result.modifiedCount} users`,
          modifiedCount: result.modifiedCount
        });

      case 'deactivate':
        // Deactivate users
        result = await User.updateMany(
          { _id: { $in: userIds } },
          { $set: { isActive: false } }
        );

        console.log(`✅ Deactivated ${result.modifiedCount} users`);
        
        return Response.json({
          message: `Successfully deactivated ${result.modifiedCount} users`,
          modifiedCount: result.modifiedCount
        });

      case 'delete':
        // Delete users (only if they don't have critical data)
        const usersToDelete = await User.find({ _id: { $in: userIds } });
        
        // Check if any users are admins with users under them
        for (const userToDelete of usersToDelete) {
          if (userToDelete.role === 'admin') {
            const usersUnderAdmin = await User.countDocuments({ 
              adminId: userToDelete._id, 
              role: 'user' 
            });
            if (usersUnderAdmin > 0) {
              return Response.json({ 
                error: `Cannot delete admin ${userToDelete.name}. ${usersUnderAdmin} users are assigned to this admin.` 
              }, { status: 400 });
            }
          }
        }

        result = await User.deleteMany({ _id: { $in: userIds } });

        console.log(`✅ Deleted ${result.deletedCount} users`);
        
        return Response.json({
          message: `Successfully deleted ${result.deletedCount} users`,
          deletedCount: result.deletedCount
        });

      default:
        return Response.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error: any) {
    console.error('Error performing bulk operation:', error);
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// GET - Get bulk operation options
export async function GET(request: Request) {
  try {
    const user = await verifyAuth(request);
    
    if (!user || user.role !== 'super_admin') {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await connectDB();

    // Get all admins under this super admin for assignment options
    const admins = await User.find({
      superAdminId: user.userId,
      role: 'admin',
      isActive: true
    }).select('_id name email companyName').lean();

    return Response.json({
      availableActions: [
        { id: 'assign_admin', name: 'Assign to Admin', requiresAdmin: true },
        { id: 'unassign_admin', name: 'Unassign from Admin', requiresAdmin: false },
        { id: 'activate', name: 'Activate Users', requiresAdmin: false },
        { id: 'deactivate', name: 'Deactivate Users', requiresAdmin: false },
        { id: 'delete', name: 'Delete Users', requiresAdmin: false }
      ],
      availableAdmins: admins
    });
  } catch (error: any) {
    console.error('Error fetching bulk options:', error);
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}
