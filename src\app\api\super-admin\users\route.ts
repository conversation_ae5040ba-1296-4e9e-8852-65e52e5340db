
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { verifyAuth } from '@/middleware/auth';

// GET - Fetch all users under super admin
export async function GET(request: Request) {
  try {
    const user = await verifyAuth(request);
    
    if (!user || user.role !== 'super_admin') {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('🔍 Super Admin Users API called');
    console.log(`👤 Super Admin auth result: { user: '${user.email}', hasAccess: true }`);

    await connectDB();
    console.log('✅ Database connected');

    // Get all admins under this super admin
    const admins = await User.find({
      superAdminId: user.userId,
      role: 'admin',
    }).select('-password').lean();

    // Get all users under all admins
    const adminIds = admins.map(admin => admin._id);
    const users = adminIds.length > 0 ? await User.find({
      adminId: { $in: adminIds },
      role: 'user',
    }).select('-password').populate('adminId', 'name email companyName').lean() : [];

    // Also get orphaned users (users without adminId but should be under this super admin)
    const orphanedUsers = await User.find({
      role: 'user',
      adminId: { $exists: false }
    }).select('-password').lean();

    console.log(`📊 Found ${admins.length} admins, ${users.length} users, ${orphanedUsers.length} orphaned users`);

    return Response.json({
      admins: admins.map(admin => ({
        ...admin,
        userCount: users.filter(user => (user as any).adminId?.toString() === (admin as any)._id.toString()).length
      })),
      users: users.map(user => ({
        ...user,
        adminName: (user as any).adminId?.name || 'Unknown',
        adminEmail: (user as any).adminId?.email || 'Unknown',
        adminCompany: (user as any).adminId?.companyName || 'Unknown'
      })),
      orphanedUsers,
      totalAdmins: admins.length,
      totalUsers: users.length + orphanedUsers.length,
      totalOrphanedUsers: orphanedUsers.length
    });
  } catch (error: any) {
    console.error('Error fetching super admin users:', error);
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST - Create new user and assign to admin
export async function POST(request: Request) {
  try {
    const user = await verifyAuth(request);
    
    if (!user || user.role !== 'super_admin') {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await connectDB();
    
    const { name, email, password, adminId, companyName, phoneNumber, address } = await request.json();

    // Validate required fields
    if (!name || !email || !password) {
      return Response.json({ error: 'Name, email, and password are required' }, { status: 400 });
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email: email.toLowerCase() });
    if (existingUser) {
      return Response.json({ error: 'User with this email already exists' }, { status: 409 });
    }

    // If adminId provided, verify it belongs to this super admin
    if (adminId) {
      const admin = await User.findOne({
        _id: adminId,
        superAdminId: user.userId,
        role: 'admin'
      });
      
      if (!admin) {
        return Response.json({ error: 'Invalid admin selected' }, { status: 400 });
      }
    }

    // Create new user
    const newUser = new User({
      email: email.toLowerCase(),
      password,
      name,
      role: 'user',
      adminId: adminId || undefined,
      companyName,
      phoneNumber,
      address,
      isActive: true,
    });

    await newUser.save();

    console.log(`✅ User created: ${newUser.email} under admin: ${adminId || 'None'}`);

    return Response.json({
      message: 'User created successfully',
      user: {
        id: newUser._id,
        email: newUser.email,
        name: newUser.name,
        role: newUser.role,
        adminId: newUser.adminId,
        companyName: newUser.companyName,
        phoneNumber: newUser.phoneNumber,
        address: newUser.address,
        isActive: newUser.isActive,
      }
    }, { status: 201 });
  } catch (error: any) {
    console.error('Error creating user:', error);
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}
