import { MongoClient } from 'mongodb';

const MONGODB_URI = process.env.TESTLIST || process.env.MONGODB_URI || 'mongodb://localhost:27017';

export async function GET(request: Request) {
  // Security check - only allow in development
  if (process.env.NODE_ENV === 'production') {
    return Response.json({ error: 'Not available in production' }, { status: 403 });
  }

  let client: MongoClient | null = null;

  try {
    const url = new URL(request.url);
    const action = url.searchParams.get('action') || 'explore';
    const limit = parseInt(url.searchParams.get('limit') || '10');
    
    console.log(`🔍 Database exploration request: ${action}`);
    
    client = new MongoClient(MONGODB_URI);
    await client.connect();
    console.log('✅ Connected to MongoDB for exploration');
    
    const database = client.db("Angel_api");
    
    switch (action) {
      case 'collections':
        // List all collections
        const collections = await database.listCollections().toArray();
        console.log('📋 Collections found:', collections.map(c => c.name));
        
        return Response.json({
          success: true,
          action: 'collections',
          collections: collections.map(c => ({
            name: c.name,
            type: c.type
          }))
        });
        
      case 'sample':
        // Get sample documents from totalscript collection
        const collection = database.collection("totalscript");
        const count = await collection.countDocuments();
        const samples = await collection.find({}).limit(limit).toArray();
        
        console.log(`📊 Found ${count} total documents, showing ${samples.length} samples`);
        
        return Response.json({
          success: true,
          action: 'sample',
          totalCount: count,
          sampleCount: samples.length,
          samples: samples
        });
        
      case 'structure':
        // Analyze document structure
        const structureCollection = database.collection("totalscript");
        const sampleDocs = await structureCollection.find({}).limit(100).toArray();
        
        // Analyze field types and patterns
        const fieldAnalysis: any = {};
        
        sampleDocs.forEach(doc => {
          Object.keys(doc).forEach(key => {
            if (!fieldAnalysis[key]) {
              fieldAnalysis[key] = {
                type: typeof doc[key],
                samples: new Set(),
                count: 0
              };
            }
            fieldAnalysis[key].count++;
            if (fieldAnalysis[key].samples.size < 5) {
              fieldAnalysis[key].samples.add(doc[key]);
            }
          });
        });
        
        // Convert Sets to Arrays for JSON serialization
        Object.keys(fieldAnalysis).forEach(key => {
          fieldAnalysis[key].samples = Array.from(fieldAnalysis[key].samples);
        });
        
        console.log('🔬 Field analysis completed');
        
        return Response.json({
          success: true,
          action: 'structure',
          analyzedDocuments: sampleDocs.length,
          fieldAnalysis: fieldAnalysis
        });
        
      case 'search':
        // Search for specific patterns
        const searchTerm = url.searchParams.get('term') || 'SBIN';
        const searchCollection = database.collection("totalscript");
        
        const searchResults = await searchCollection.find({
          $or: [
            { name: { $regex: searchTerm, $options: 'i' } },
            { symbol: { $regex: searchTerm, $options: 'i' } }
          ]
        }).limit(limit).toArray();
        
        console.log(`🔍 Search for "${searchTerm}" found ${searchResults.length} results`);
        
        return Response.json({
          success: true,
          action: 'search',
          searchTerm: searchTerm,
          resultCount: searchResults.length,
          results: searchResults
        });
        
      case 'exchanges':
        // Analyze exchange segments
        const exchangeCollection = database.collection("totalscript");
        const exchanges = await exchangeCollection.distinct("exch_seg");
        
        const exchangeStats: any = {};
        for (const exchange of exchanges) {
          const count = await exchangeCollection.countDocuments({ exch_seg: exchange });
          exchangeStats[exchange] = count;
        }
        
        console.log('📈 Exchange analysis:', exchangeStats);
        
        return Response.json({
          success: true,
          action: 'exchanges',
          exchanges: exchanges,
          stats: exchangeStats
        });
        
      default:
        return Response.json({
          success: true,
          action: 'help',
          availableActions: [
            'collections - List all collections',
            'sample - Get sample documents',
            'structure - Analyze document structure',
            'search?term=SYMBOL - Search for symbols',
            'exchanges - Analyze exchange segments'
          ],
          examples: [
            '/api/test/database-explorer?action=collections',
            '/api/test/database-explorer?action=sample&limit=5',
            '/api/test/database-explorer?action=structure',
            '/api/test/database-explorer?action=search&term=SBIN',
            '/api/test/database-explorer?action=exchanges'
          ]
        });
    }
    
  } catch (error) {
    console.error('❌ Database exploration error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return Response.json({
      error: 'Database exploration failed',
      message: errorMessage
    }, { status: 500 });
  } finally {
    if (client) {
      await client.close();
      console.log('🔌 Database connection closed');
    }
  }
}
