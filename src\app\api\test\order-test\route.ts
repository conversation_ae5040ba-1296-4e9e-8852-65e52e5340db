// Test endpoint to verify order placement APIs are working without JWT errors
export async function GET(request: Request) {
  // Security check - only allow in development
  if (process.env.NODE_ENV === 'production') {
    return Response.json({ error: 'Not available in production' }, { status: 403 });
  }

  try {
    const url = new URL(request.url);
    const broker = url.searchParams.get('broker') || 'angel';
    
    console.log(`🧪 Testing ${broker} order placement API`);
    
    // Test order data with valid SBIN security ID
    const testOrderData = {
      clientCode: 'test-client',
      orderType: 'BUY',
      quantity: 1,
      price: 100,
      productType: 'INTRADAY',
      validity: 'DAY',
      symbolToken: '3045', // SBIN security ID that we know works
      tradingSymbol: 'SBIN-EQ'
    };
    
    console.log(`📤 Test order data:`, testOrderData);
    
    // Make request to the order API
    const apiUrl = broker === 'angel' 
      ? 'http://localhost:3000/api/orders/angel'
      : 'http://localhost:3000/api/orders/motilal';
      
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // No Authorization header to test JWT bypass
      },
      body: JSON.stringify(testOrderData)
    });
    
    const result = await response.json();
    
    console.log(`📨 ${broker} API Response:`, {
      status: response.status,
      statusText: response.statusText,
      data: result
    });
    
    return Response.json({
      success: true,
      broker: broker,
      testData: testOrderData,
      apiResponse: {
        status: response.status,
        statusText: response.statusText,
        data: result
      }
    });
    
  } catch (error) {
    console.error('Error in order test:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return Response.json({
      error: 'Test failed',
      message: errorMessage
    }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const { broker, orderData } = await request.json();
    
    console.log(`🧪 Testing ${broker} order placement with custom data`);
    console.log(`📤 Custom order data:`, orderData);
    
    // Make request to the order API
    const apiUrl = broker === 'angel' 
      ? 'http://localhost:3000/api/orders/angel'
      : 'http://localhost:3000/api/orders/motilal';
      
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // No Authorization header to test JWT bypass
      },
      body: JSON.stringify(orderData)
    });
    
    const result = await response.json();
    
    console.log(`📨 ${broker} API Response:`, {
      status: response.status,
      statusText: response.statusText,
      data: result
    });
    
    return Response.json({
      success: true,
      broker: broker,
      testData: orderData,
      apiResponse: {
        status: response.status,
        statusText: response.statusText,
        data: result
      }
    });
    
  } catch (error) {
    console.error('Error in custom order test:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return Response.json({
      error: 'Test failed',
      message: errorMessage
    }, { status: 500 });
  }
}
