import { findSymbolInAngelDB, findSymbolByToken, findSymbolBySecurityId, searchSymbols } from '@/lib/symbolUtils';

export async function GET(request: Request) {
  // Security check - only allow in development
  if (process.env.NODE_ENV === 'production') {
    return Response.json({ error: 'Not available in production' }, { status: 403 });
  }

  try {
    const url = new URL(request.url);
    const symbol = url.searchParams.get('symbol');
    const token = url.searchParams.get('token');
    const search = url.searchParams.get('search');

    console.log('🔍 Symbol lookup request:', { symbol, token, search });

    let result = null;

    if (token) {
      // Use security ID lookup (this is the correct way)
      result = await findSymbolBySecurityId(token);
    } else if (symbol) {
      // Use symbol name lookup
      result = await findSymbolInAngelDB(symbol);
    } else if (search) {
      // Use search functionality
      result = await searchSymbols(search, 10);
    } else {
      return Response.json({
        error: 'Please provide symbol, token (security ID), or search parameter',
        examples: [
          '/api/test/symbol-lookup?symbol=SBIN (search by company name)',
          '/api/test/symbol-lookup?token=3045 (search by security ID)',
          '/api/test/symbol-lookup?search=RELIANCE (search multiple)'
        ]
      }, { status: 400 });
    }

    return Response.json({
      success: true,
      query: { symbol, token, search },
      result: result
    });

  } catch (error) {
    console.error('Error in symbol lookup:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return Response.json({
      error: 'Internal server error',
      message: errorMessage
    }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const { symbols } = await request.json();

    if (!symbols || !Array.isArray(symbols)) {
      return Response.json({ 
        error: 'Please provide an array of symbols',
        example: { symbols: ['SBIN', 'RELIANCE', 'TCS'] }
      }, { status: 400 });
    }

    console.log('🔍 Bulk symbol lookup request:', symbols);

    const results = [];

    for (const symbol of symbols) {
      try {
        const symbolData = await findSymbolInAngelDB(symbol);
        results.push({
          symbol: symbol,
          found: !!symbolData,
          data: symbolData
        });
      } catch (error) {
        results.push({
          symbol: symbol,
          found: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return Response.json({
      success: true,
      query: symbols,
      results: results,
      summary: {
        total: symbols.length,
        found: results.filter(r => r.found).length,
        notFound: results.filter(r => !r.found).length
      }
    });

  } catch (error) {
    console.error('Error in bulk symbol lookup:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return Response.json({
      error: 'Internal server error',
      message: errorMessage
    }, { status: 500 });
  }
}
