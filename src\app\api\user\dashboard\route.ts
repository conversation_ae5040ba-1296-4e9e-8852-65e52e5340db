
import { cookies } from 'next/headers';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { BillingRecord } from '@/models/Billing';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export async function GET() {
  try {
    console.log('🔍 User Dashboard API called');

    // Get token from cookies
    const cookieStore = await cookies();
    const token = cookieStore.get('token')?.value;
    
    if (!token) {
      return Response.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Verify token
    let decoded: any;
    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (error) {
      return Response.json({ error: 'Invalid token' }, { status: 401 });
    }

    if (decoded.role !== 'user') {
      return Response.json({ error: 'User access required' }, { status: 403 });
    }

    await connectDB();
    console.log('✅ Database connected');

    // Get the user
    const user = await User.findById(decoded.userId).select('-password -userOtp');
    
    if (!user) {
      return Response.json({ error: 'User not found' }, { status: 404 });
    }

    console.log(`👤 User found: ${user.name} (${user.email})`);

    // Get the admin
    const admin = await User.findById(user.adminId).select('name email companyName orderRate');
    
    if (!admin) {
      return Response.json({ error: 'Admin not found' }, { status: 404 });
    }

    console.log(`👤 Admin found: ${admin.name} (${admin.email})`);

    // Get all billing records for this user (which represent orders placed for them)
    const billingRecords = await BillingRecord.find({
      userId: user._id
    }).sort({ createdAt: -1 });

    console.log(`📊 Found ${billingRecords.length} billing records (orders) for user`);

    // Calculate statistics
    const totalOrders = billingRecords.length;
    const totalBilling = billingRecords.reduce((sum, record) => sum + (record.amount || 0), 0);

    // Calculate current month data
    const currentMonth = new Date();
    const startOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
    const endOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0);

    const currentMonthRecords = billingRecords.filter(record => {
      const recordDate = new Date(record.createdAt);
      return recordDate >= startOfMonth && recordDate <= endOfMonth;
    });

    const currentMonthOrders = currentMonthRecords.length;
    const currentMonthBilling = currentMonthRecords.reduce((sum, record) => sum + (record.amount || 0), 0);

    // Format orders for display
    const orders = billingRecords.map(record => ({
      _id: record._id,
      orderId: record.orderId,
      symbol: record.symbol,
      quantity: record.quantity,
      price: record.price,
      orderType: record.orderType,
      broker: record.broker,
      status: record.status,
      createdAt: record.createdAt,
      amount: record.amount
    }));

    const dashboardData = {
      user: {
        name: user.name,
        email: user.email,
        userCode: user.userCode,
        companyName: user.companyName
      },
      admin: {
        name: admin.name,
        email: admin.email,
        companyName: admin.companyName
      },
      orders,
      statistics: {
        totalOrders,
        totalBilling,
        currentMonthOrders,
        currentMonthBilling
      }
    };

    console.log(`📊 Dashboard data prepared for user: ${user.name}`);
    console.log(`   - Total Orders: ${totalOrders}`);
    console.log(`   - Total Billing: ₹${totalBilling}`);
    console.log(`   - Current Month Orders: ${currentMonthOrders}`);

    return Response.json(dashboardData);

  } catch (error: any) {
    console.error('❌ Error in user dashboard API:', error);
    return Response.json({ 
      error: 'Internal server error',
      details: error.message 
    }, { status: 500 });
  }
}
