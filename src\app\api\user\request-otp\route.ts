
import connectDB from '@/lib/mongodb';
import User from '@/models/User';

export async function POST(request: Request) {
  try {
    const { userId } = await request.json();

    if (!userId) {
      return Response.json({ error: 'User ID is required' }, { status: 400 });
    }

    await connectDB();

    // Find user by userCode
    const user = await User.findOne({
      userCode: userId.toUpperCase(),
      role: 'user'
    });

    if (!user) {
      return Response.json({ error: 'User ID not found. Please check your User ID or contact your admin.' }, { status: 404 });
    }

    // Check if user has an admin
    if (!user.adminId) {
      return Response.json({ error: 'User not assigned to any admin. Please contact support.' }, { status: 400 });
    }

    // Get the admin to check if user has OTP set
    const admin = await User.findById(user.adminId);
    if (!admin) {
      return Response.json({ error: 'Admin not found. Please contact support.' }, { status: 404 });
    }

    // Check if user has OTP set by admin
    if (!user.userOtp) {
      return Response.json({ 
        error: 'No OTP set for your account. Please ask your admin to set an OTP for you.' 
      }, { status: 400 });
    }

    console.log(`📧 OTP requested for user: ${user.userCode} - ${user.name} (${user.email})`);
    console.log(`🔑 User OTP: ${user.userOtp}`);

    // In a real system, you would send the OTP via SMS/Email
    // For now, we'll just log it and return success
    // The admin-set OTP will be used for verification

    return Response.json({
      success: true,
      message: 'OTP request received. Use the OTP provided by your admin.',
      // In development, you might want to return the OTP for testing
      // Remove this in production
      otp: user.userOtp
    });

  } catch (error: any) {
    console.error('Error requesting OTP:', error);
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}
