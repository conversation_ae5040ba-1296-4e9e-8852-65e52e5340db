
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export async function POST(request: Request) {
  try {
    const { userId, otp } = await request.json();

    if (!userId || !otp) {
      return Response.json({ error: 'User ID and OTP are required' }, { status: 400 });
    }

    await connectDB();

    // Find user by userCode
    const user = await User.findOne({
      userCode: userId.toUpperCase(),
      role: 'user'
    });

    if (!user) {
      return Response.json({ error: 'User not found' }, { status: 404 });
    }

    // Verify OTP
    if (user.userOtp !== otp) {
      console.log(`❌ Invalid OTP for user ${user.email}: provided=${otp}, expected=${user.userOtp}`);
      return Response.json({ error: 'Invalid OTP' }, { status: 400 });
    }

    console.log(`✅ OTP verified for user: ${user.userCode} - ${user.name} (${user.email})`);

    // Create JWT token for user session
    const token = jwt.sign(
      { 
        userId: user._id,
        email: user.email,
        role: user.role,
        name: user.name
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    // Set HTTP-only cookie
    const response = Response.json({
      success: true,
      message: 'OTP verified successfully',
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
        companyName: user.companyName
      }
    });

    response.headers.set('Set-Cookie', `token=${token}; HttpOnly; Path=/; Max-Age=86400; SameSite=Strict`);

    return response;

  } catch (error: any) {
    console.error('Error verifying OTP:', error);
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}
