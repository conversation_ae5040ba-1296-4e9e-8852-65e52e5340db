// Using standard Response for Next.js 15 compatibility
import jwt from 'jsonwebtoken';
import connectDB from '@/lib/mongodb';
import Angel<PERSON><PERSON> from '@/models/AngelUser';
import { verifyAuth as newVerifyAuth, getUsersUnderAdmin } from '@/middleware/auth';
import { withCache, cacheKeys, invalidateCache } from '@/lib/cache';
import { withFullOptimization } from '@/middleware/apiOptimization';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// Middleware to verify authentication
async function verifyAuth(request: Request) {
  const token = request.headers.get('cookie')?.split('auth-token=')[1]?.split(';')[0] ||
                request.headers.get('authorization')?.replace('Bearer ', '');

  if (!token) {
    console.log('⚠️ No JWT token provided - proceeding without authentication for testing');
    return { userId: 'test-user', role: 'user' };
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET) as any;
    console.log('✅ JWT token verified successfully');
    return decoded;
  } catch (error) {
    console.error('JWT verification failed:', error);
    console.log('⚠️ Proceeding without authentication for testing purposes');
    return { userId: 'test-user', role: 'user' };
  }
}

// GET - Get all Angel users for the authenticated user (FIXED: includes admin's own users)
async function handleGetAngelUsers(request: Request) {
  try {
    await connectDB();

    // Use new auth system
    const user = await newVerifyAuth(request);
    if (!user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Use cache for better performance
    const cacheKey = cacheKeys.userAccounts(user.userId, 'angel');

    const result = await withCache(cacheKey, async () => {
      let angelUsers;

      if (user.role === 'super_admin') {
        // Super admin can see all Angel users
        angelUsers = await AngelUser.find({})
          .populate('owner', 'name email adminId')
          .select('-password -totpKey')
          .sort({ createdAt: -1 });
      } else if (user.role === 'admin') {
        // Admin can see Angel accounts of USERS under their management AND their own accounts
        // UPDATED: Admins can also own broker accounts directly
        const managedUsers = await getUsersUnderAdmin(user.userId);
        const managedUserIds = managedUsers.map(u => u._id);

        // Include both user IDs AND admin ID - admins can own broker accounts
        const allOwnerIds = [...managedUserIds, user.userId];

        angelUsers = await AngelUser.find({ owner: { $in: allOwnerIds } })
          .populate('owner', 'name email userCode')
          .select('-password -totpKey')
          .sort({ createdAt: -1 });

        console.log(`👨‍💼 Admin ${user.email} accessing ${angelUsers.length} Angel accounts of their ${managedUserIds.length} users`);
      } else {
        // Regular user can only see their own Angel users
        angelUsers = await AngelUser.find({ owner: user.userId })
          .select('-password -totpKey')
          .sort({ createdAt: -1 });
      }

      return {
        users: angelUsers,
        count: angelUsers.length,
        userRole: user.role
      };
    }, 60); // Cache for 1 minute

    return Response.json(result);
  } catch (error: any) {
    console.error('Error fetching Angel users:', error);

    if (error.message === 'No token provided' || error.name === 'JsonWebTokenError') {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST - Create new Angel user
export async function POST(request: Request) {
  try {
    await connectDB();
    
    const decoded = await verifyAuth(request);
    const angelUserData = await request.json();

    // Validate required fields
    const requiredFields = ['userId', 'password', 'apiKey', 'totpKey', 'clientName', 'email', 'phoneNumber', 'capital'];
    for (const field of requiredFields) {
      if (!angelUserData[field]) {
        return Response.json({ error: `${field} is required` }, { status: 400 });
      }
    }

    // Check if Angel user with same userId already exists for this owner
    const existingUser = await AngelUser.findOne({
      userId: angelUserData.userId,
      owner: decoded.userId
    });

    if (existingUser) {
      return Response.json({ error: 'Angel user with this User ID already exists' }, { status: 409 });
    }

    // Create new Angel user
    const angelUser = new AngelUser({
      ...angelUserData,
      owner: decoded.userId,
    });

    await angelUser.save();

    // Return user without sensitive fields
    const { password, totpKey, ...safeUser } = angelUser.toObject();

    return Response.json({
      message: 'Angel user created successfully',
      user: safeUser
    }, { status: 201 });
  } catch (error: any) {
    console.error('Error creating Angel user:', error);

    if (error.message === 'No token provided' || error.name === 'JsonWebTokenError') {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map((err: any) => err.message);
      return Response.json({ error: 'Validation failed', details: errors }, { status: 400 });
    }

    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PUT - Update Angel user
export async function PUT(request: Request) {
  try {
    await connectDB();

    const decoded = await verifyAuth(request);
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('id');

    if (!userId) {
      return Response.json({ error: 'User ID is required' }, { status: 400 });
    }

    const updateData = await request.json();

    // Remove sensitive fields from update data
    delete updateData.password;
    delete updateData.totpKey;
    delete updateData.owner;

    const angelUser = await AngelUser.findOneAndUpdate(
      { _id: userId, owner: decoded.userId },
      updateData,
      { new: true, select: '-password -totpKey' }
    );

    if (!angelUser) {
      return Response.json({ error: 'Angel user not found' }, { status: 404 });
    }

    return Response.json({
      message: 'Angel user updated successfully',
      user: angelUser
    });
  } catch (error: any) {
    console.error('Error updating Angel user:', error);

    if (error.message === 'No token provided' || error.name === 'JsonWebTokenError') {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// DELETE - Delete Angel user
export async function DELETE(request: Request) {
  try {
    await connectDB();

    const decoded = await verifyAuth(request);
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('id');

    if (!userId) {
      return Response.json({ error: 'User ID is required' }, { status: 400 });
    }

    const angelUser = await AngelUser.findOneAndDelete({
      _id: userId,
      owner: decoded.userId
    });

    if (!angelUser) {
      return Response.json({ error: 'Angel user not found' }, { status: 404 });
    }

    return Response.json({
      message: 'Angel user deleted successfully'
    });
  } catch (error: any) {
    console.error('Error deleting Angel user:', error);

    if (error.message === 'No token provided' || error.name === 'JsonWebTokenError') {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Apply full optimization to the GET handler
export const GET = withFullOptimization(handleGetAngelUsers, {
  enableCaching: true,
  maxAge: 60, // 1 minute cache
  staleWhileRevalidate: 120, // 2 minutes stale-while-revalidate
  timeoutMs: 10000, // 10 second timeout
  maxBodySize: 1024 * 1024 // 1MB max body size
});
