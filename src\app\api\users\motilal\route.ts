// Using standard Response for Next.js 15 compatibility
import jwt from 'jsonwebtoken';
import connectDB from '@/lib/mongodb';
import MotilalUser from '@/models/MotilalUser';
import { verifyAuth as newVerifyAuth, getUsersUnderAdmin } from '@/middleware/auth';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// Middleware to verify authentication
async function verifyAuth(request: Request) {
  const token = request.headers.get('cookie')?.split('auth-token=')[1]?.split(';')[0] ||
                request.headers.get('authorization')?.replace('Bearer ', '');

  if (!token) {
    console.log('⚠️ No JWT token provided - proceeding without authentication for testing');
    return { userId: 'test-user', role: 'user' };
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET) as any;
    console.log('✅ JWT token verified successfully');
    return decoded;
  } catch (error) {
    console.error('JWT verification failed:', error);
    console.log('⚠️ Proceeding without authentication for testing purposes');
    return { userId: 'test-user', role: 'user' };
  }
}

// GET - Get all Motilal users for the authenticated user
export async function GET(request: Request) {
  try {
    await connectDB();

    // Use new auth system
    const user = await newVerifyAuth(request);
    if (!user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    let motilalUsers;

    if (user.role === 'super_admin') {
      // Super admin can see all Motilal users
      motilalUsers = await MotilalUser.find({})
        .populate('owner', 'name email adminId')
        .select('-password -totpKey')
        .sort({ createdAt: -1 });
    } else if (user.role === 'admin') {
      // Admin can see Motilal accounts of USERS under their management AND their own accounts
      // UPDATED: Admins can also own broker accounts directly
      const managedUsers = await getUsersUnderAdmin(user.userId);
      const managedUserIds = managedUsers.map(u => u._id);

      // Include both user IDs AND admin ID - admins can own broker accounts
      const allOwnerIds = [...managedUserIds, user.userId];

      motilalUsers = await MotilalUser.find({ owner: { $in: allOwnerIds } })
        .populate('owner', 'name email userCode')
        .select('-password -totpKey')
        .sort({ createdAt: -1 });

      console.log(`👨‍💼 Admin ${user.email} accessing ${motilalUsers.length} Motilal accounts of their ${managedUserIds.length} users`);
    } else {
      // Regular user can only see their own Motilal users
      motilalUsers = await MotilalUser.find({ owner: user.userId })
        .select('-password -totpKey')
        .sort({ createdAt: -1 });
    }

    return Response.json({
      users: motilalUsers,
      count: motilalUsers.length,
      userRole: user.role
    });
  } catch (error: any) {
    console.error('Error fetching Motilal users:', error);
    
    if (error.message === 'No token provided' || error.name === 'JsonWebTokenError') {
      return Response.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Create new Motilal user
export async function POST(request: Request) {
  try {
    await connectDB();
    
    const decoded = await verifyAuth(request);
    const motilalUserData = await request.json();

    // Validate required fields
    const requiredFields = ['userId', 'password', 'apiKey', 'twoFA', 'totpKey', 'clientName', 'email', 'phoneNumber', 'capital'];
    for (const field of requiredFields) {
      if (!motilalUserData[field]) {
        return Response.json(
          { error: `${field} is required` },
          { status: 400 }
        );
      }
    }

    // Check if Motilal user with same userId already exists for this owner
    const existingUser = await MotilalUser.findOne({ 
      userId: motilalUserData.userId,
      owner: decoded.userId 
    });
    
    if (existingUser) {
      return Response.json(
        { error: 'Motilal user with this User ID already exists' },
        { status: 409 }
      );
    }

    // Create new Motilal user
    const motilalUser = new MotilalUser({
      ...motilalUserData,
      owner: decoded.userId,
    });

    await motilalUser.save();

    // Return user without sensitive fields
    const { password, totpKey, ...safeUser } = motilalUser.toObject();

    return Response.json(
      {
        message: 'Motilal user created successfully',
        user: safeUser
      },
      { status: 201 }
    );
  } catch (error: any) {
    console.error('Error creating Motilal user:', error);
    
    if (error.message === 'No token provided' || error.name === 'JsonWebTokenError') {
      return Response.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map((err: any) => err.message);
      return Response.json(
        { error: 'Validation failed', details: errors },
        { status: 400 }
      );
    }

    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT - Update Motilal user
export async function PUT(request: Request) {
  try {
    await connectDB();

    const decoded = await verifyAuth(request);
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('id');

    if (!userId) {
      return Response.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    const updateData = await request.json();

    // Remove sensitive fields from update data
    delete updateData.password;
    delete updateData.totpKey;
    delete updateData.owner;

    const motilalUser = await MotilalUser.findOneAndUpdate(
      { _id: userId, owner: decoded.userId },
      updateData,
      { new: true, select: '-password -totpKey' }
    );

    if (!motilalUser) {
      return Response.json(
        { error: 'Motilal user not found' },
        { status: 404 }
      );
    }

    return Response.json({
      message: 'Motilal user updated successfully',
      user: motilalUser
    });
  } catch (error: any) {
    console.error('Error updating Motilal user:', error);

    if (error.message === 'No token provided' || error.name === 'JsonWebTokenError') {
      return Response.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE - Delete Motilal user
export async function DELETE(request: Request) {
  try {
    await connectDB();

    const decoded = await verifyAuth(request);
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('id');

    if (!userId) {
      return Response.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    const motilalUser = await MotilalUser.findOneAndDelete({
      _id: userId,
      owner: decoded.userId
    });

    if (!motilalUser) {
      return Response.json(
        { error: 'Motilal user not found' },
        { status: 404 }
      );
    }

    return Response.json({
      message: 'Motilal user deleted successfully'
    });
  } catch (error: any) {
    console.error('Error deleting Motilal user:', error);

    if (error.message === 'No token provided' || error.name === 'JsonWebTokenError') {
      return Response.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
