import React from "react";
import "./globals.css";
import type { Metadata } from "next";
import { AuthProvider } from "@/contexts/AuthContext";
import { ToastProvider } from "@/components/Toast";
import Navigation from "@/components/Navigation";

export const metadata: Metadata = {
  title: "Dhan Market Dashboard",
  description: "Ultra-fast market data dashboard with user authentication",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body>
        <AuthProvider>
          <ToastProvider>
            <Navigation />
            {children}
          </ToastProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
