'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

interface DashboardData {
  overview: {
    totalAdmins: number;
    totalUsers: number;
    activeAdmins: number;
    currentMonth: {
      totalOrders: number;
      totalRevenue: number;
      pendingAmount: number;
      paidAmount: number;
    };
    previousMonth: {
      totalOrders: number;
      totalRevenue: number;
    };
    growth: {
      orders: number;
      revenue: number;
    };
  };
  admins: any[];
  currentMonthBilling: any[];
  adminPerformance: any[];
  billingCycles: {
    current: string;
    previous: string;
  };
}

interface Admin {
  _id: string;
  name: string;
  email: string;
  companyName: string;
  orderRate: number;
  totalOrders: number;
  totalBilling: number;
  isActive: boolean;
  createdAt: string;
  invitationToken?: string;
  invitationExpiry?: string;
  isInvitationAccepted?: boolean;
}

export default function SuperAdminDashboard() {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [isDataLoading, setIsDataLoading] = useState(true);
  const [error, setError] = useState('');
  const [isRedirecting, setIsRedirecting] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [admins, setAdmins] = useState<Admin[]>([]);
  const [users, setUsers] = useState<any[]>([]);
  const [orphanedUsers, setOrphanedUsers] = useState<any[]>([]);
  const [selectedAdmin, setSelectedAdmin] = useState<Admin | null>(null);
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showEditUserModal, setShowEditUserModal] = useState(false);
  const [showAddUserModal, setShowAddUserModal] = useState(false);
  const [showBulkModal, setShowBulkModal] = useState(false);
  const [showBillingModal, setShowBillingModal] = useState(false);
  const [showSettlementModal, setShowSettlementModal] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);
  const [bulkOptions, setBulkOptions] = useState<any>(null);

  // Redirect if not super admin
  useEffect(() => {
    if (authLoading || isRedirecting) return; // Wait for auth to complete
    
    if (!user) {
      setIsRedirecting(true);
      router.replace('/login');
      return;
    }
    
    if (user.role !== 'super_admin') {
      setIsRedirecting(true);
      router.replace('/admin'); // Redirect admin to admin page, others to home
      return;
    }
  }, [user, authLoading, router, isRedirecting]);



  // Fetch admins list
  const fetchAdmins = async () => {
    try {
      const response = await fetch('/api/super-admin/admins', {
        credentials: 'include',
      });
      if (response.ok) {
        const result = await response.json();
        setAdmins(result.data || []);
      }
    } catch (error) {
      console.error('Error fetching admins:', error);
    }
  };

  // Fetch users
  const fetchUsers = async () => {
    try {
      const response = await fetch('/api/super-admin/users', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        setUsers(data.users || []);
        setOrphanedUsers(data.orphanedUsers || []);
        // Update admins with user counts
        setAdmins(data.admins || []);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
    }
  };

  // Fetch bulk options
  const fetchBulkOptions = async () => {
    try {
      const response = await fetch('/api/super-admin/users/bulk', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        setBulkOptions(data);
      }
    } catch (error) {
      console.error('Error fetching bulk options:', error);
    }
  };

  // Setup default rates for existing admins
  const setupDefaults = async () => {
    try {
      setActionLoading(true);
      const response = await fetch('/api/super-admin/setup-defaults', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({ defaultOrderRate: 5, fixExistingAdmins: true })
      });

      if (response.ok) {
        const result = await response.json();
        alert(`Setup completed! Updated ${result.data.summary.totalProcessed} admins.`);
        fetchAdmins();
        fetchDashboardData();
      } else {
        alert('Failed to setup defaults');
      }
    } catch (error) {
      console.error('Error setting up defaults:', error);
      alert('Error setting up defaults');
    } finally {
      setActionLoading(false);
    }
  };

  // Update admin
  const updateAdmin = async (adminId: string, updates: any) => {
    try {
      setActionLoading(true);
      const response = await fetch(`/api/super-admin/admins/${adminId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(updates)
      });

      if (response.ok) {
        alert('Admin updated successfully');
        fetchAdmins();
        setShowEditModal(false);
        setSelectedAdmin(null);
      } else {
        const error = await response.json();
        alert(`Failed to update admin: ${error.error}`);
      }
    } catch (error) {
      console.error('Error updating admin:', error);
      alert('Error updating admin');
    } finally {
      setActionLoading(false);
    }
  };

  // Clear billing
  const clearBilling = async (adminId?: string, billingCycle?: string) => {
    try {
      setActionLoading(true);
      const response = await fetch('/api/super-admin/billing/clear', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          adminId,
          billingCycle,
          clearType: 'all'
        })
      });

      if (response.ok) {
        const result = await response.json();
        alert(`Billing cleared! ${result.data.clearedRecords} records removed.`);
        fetchDashboardData();
      } else {
        alert('Failed to clear billing');
      }
    } catch (error) {
      console.error('Error clearing billing:', error);
      alert('Error clearing billing');
    } finally {
      setActionLoading(false);
    }
  };

  // Mark payment
  const markPayment = async (adminId: string, billingCycle: string) => {
    try {
      setActionLoading(true);
      const response = await fetch('/api/super-admin/billing/payment', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          adminId,
          billingCycle,
          paymentMethod: 'manual',
          paymentReference: `MANUAL_${Date.now()}`,
          notes: 'Marked as paid by super admin'
        })
      });

      if (response.ok) {
        alert('Payment marked successfully');
        fetchDashboardData();
      } else {
        alert('Failed to mark payment');
      }
    } catch (error) {
      console.error('Error marking payment:', error);
      alert('Error marking payment');
    } finally {
      setActionLoading(false);
    }
  };

  // Update user
  const updateUser = async (userId: string, updates: any) => {
    try {
      setActionLoading(true);
      const response = await fetch(`/api/super-admin/users/${userId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(updates)
      });

      if (response.ok) {
        alert('User updated successfully');
        fetchUsers();
        setShowEditUserModal(false);
        setSelectedUser(null);
      } else {
        const error = await response.json();
        alert(error.error || 'Failed to update user');
      }
    } catch (error) {
      console.error('Error updating user:', error);
      alert('Failed to update user');
    } finally {
      setActionLoading(false);
    }
  };

  // Delete user
  const deleteUser = async (userId: string) => {
    if (!confirm('Are you sure you want to delete this user?')) return;

    try {
      setActionLoading(true);
      const response = await fetch(`/api/super-admin/users/${userId}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      if (response.ok) {
        alert('User deleted successfully');
        fetchUsers();
      } else {
        const error = await response.json();
        alert(error.error || 'Failed to delete user');
      }
    } catch (error) {
      console.error('Error deleting user:', error);
      alert('Failed to delete user');
    } finally {
      setActionLoading(false);
    }
  };

  // Bulk operations
  const performBulkOperation = async (action: string, adminId?: string) => {
    if (selectedUsers.length === 0) {
      alert('Please select users first');
      return;
    }

    if (!confirm(`Are you sure you want to ${action} ${selectedUsers.length} selected users?`)) return;

    try {
      setActionLoading(true);
      const response = await fetch('/api/super-admin/users/bulk', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          action,
          userIds: selectedUsers,
          adminId
        })
      });

      if (response.ok) {
        const result = await response.json();
        alert(result.message);
        fetchUsers();
        setSelectedUsers([]);
        setShowBulkModal(false);
      } else {
        const error = await response.json();
        alert(error.error || 'Failed to perform bulk operation');
      }
    } catch (error) {
      console.error('Error performing bulk operation:', error);
      alert('Failed to perform bulk operation');
    } finally {
      setActionLoading(false);
    }
  };

  // Fetch dashboard data function
  const fetchDashboardData = async () => {
    try {
      setIsDataLoading(true);
      const response = await fetch('/api/super-admin/dashboard', {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch dashboard data');
      }

      const result = await response.json();
      setDashboardData(result.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsDataLoading(false);
    }
  };

  // Load data on mount
  useEffect(() => {
    if (!authLoading && user?.role === 'super_admin' && !isRedirecting) {
      fetchDashboardData();
      fetchAdmins();
      fetchUsers();
      fetchBulkOptions();
    }
  }, [user, authLoading, isRedirecting]);

  // Show loading while checking auth
  if (authLoading || isRedirecting) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">{isRedirecting ? 'Redirecting...' : 'Loading...'}</p>
        </div>
      </div>
    );
  }

  // Show error if user is not authenticated
  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
          <p className="text-gray-600">Please log in to access this page.</p>
        </div>
      </div>
    );
  }

  if (isDataLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Error</h1>
          <p className="text-gray-600">{error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Super Admin Dashboard</h1>
              <p className="text-gray-600">Welcome back, {user.name || user.email}</p>
            </div>
            <div className="flex space-x-4">
              <Link
                href="/super-admin/invite-admin"
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
              >
                Invite Admin
              </Link>
              <Link
                href="/trading"
                className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
              >
                Trading
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Dashboard Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Quick Actions */}
        <div className="mb-8 bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="flex flex-wrap gap-4">
            <button
              onClick={setupDefaults}
              disabled={actionLoading}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {actionLoading ? 'Processing...' : 'Setup Default Rates (₹5)'}
            </button>
            <button
              onClick={() => clearBilling()}
              disabled={actionLoading}
              className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 disabled:opacity-50"
            >
              Clear All Billing
            </button>
            <button
              onClick={() => setShowSettlementModal(true)}
              className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
            >
              Settlement Management
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {[
                { id: 'overview', name: 'Overview' },
                { id: 'admins', name: 'Admin Management' },
                { id: 'users', name: 'User Management' },
                { id: 'invitations', name: 'Invitations' },
                { id: 'billing', name: 'Billing & Payments' },
                { id: 'settlement', name: 'Settlement' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && dashboardData && (
          <div className="space-y-8">
            {/* Overview Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white p-6 rounded-lg shadow">
                <h3 className="text-lg font-semibold text-gray-900">Total Admins</h3>
                <p className="text-3xl font-bold text-blue-600">{dashboardData.overview.totalAdmins}</p>
                <p className="text-sm text-gray-500">Active: {dashboardData.overview.activeAdmins}</p>
              </div>

              <div className="bg-white p-6 rounded-lg shadow">
                <h3 className="text-lg font-semibold text-gray-900">Total Users</h3>
                <p className="text-3xl font-bold text-green-600">{dashboardData.overview.totalUsers}</p>
              </div>

              <div className="bg-white p-6 rounded-lg shadow">
                <h3 className="text-lg font-semibold text-gray-900">Current Month Orders</h3>
                <p className="text-3xl font-bold text-purple-600">{dashboardData.overview.currentMonth.totalOrders}</p>
                <p className="text-sm text-gray-500">
                  Growth: {dashboardData.overview.growth.orders > 0 ? '+' : ''}{dashboardData.overview.growth.orders}%
                </p>
              </div>

              <div className="bg-white p-6 rounded-lg shadow">
                <h3 className="text-lg font-semibold text-gray-900">Current Month Revenue</h3>
                <p className="text-3xl font-bold text-orange-600">₹{dashboardData.overview.currentMonth.totalRevenue}</p>
                <p className="text-sm text-gray-500">
                  Growth: {dashboardData.overview.growth.revenue > 0 ? '+' : ''}{dashboardData.overview.growth.revenue}%
                </p>
              </div>
            </div>

            {/* Billing Summary */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Billing Summary</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Pending Amount:</span>
                    <span className="font-semibold text-red-600">₹{dashboardData.overview.currentMonth.pendingAmount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Paid Amount:</span>
                    <span className="font-semibold text-green-600">₹{dashboardData.overview.currentMonth.paidAmount}</span>
                  </div>
                  <div className="border-t pt-3">
                    <div className="flex justify-between">
                      <span className="text-gray-900 font-semibold">Total Revenue:</span>
                      <span className="font-bold text-blue-600">₹{dashboardData.overview.currentMonth.totalRevenue}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Billing Cycles</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Current Cycle:</span>
                    <span className="font-semibold">{dashboardData.billingCycles.current}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Previous Cycle:</span>
                    <span className="font-semibold">{dashboardData.billingCycles.previous}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Recent Admins */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900">Recent Admins</h3>
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Admin</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Orders</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Billing</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {dashboardData.admins.map((admin) => (
                      <tr key={admin._id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{admin.name}</div>
                            <div className="text-sm text-gray-500">{admin.email}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{admin.companyName}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">₹{admin.orderRate}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{admin.totalOrders}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">₹{admin.totalBilling}</td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            admin.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                          }`}>
                            {admin.isActive ? 'Active' : 'Inactive'}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {/* Admin Management Tab */}
        {activeTab === 'admins' && (
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Admin Management</h3>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Admin</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Orders</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Billing</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {admins.map((admin) => (
                    <tr key={admin._id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{admin.name}</div>
                          <div className="text-sm text-gray-500">{admin.email}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{admin.companyName || 'N/A'}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">₹{admin.orderRate || 0}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{admin.totalOrders || 0}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">₹{admin.totalBilling || 0}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          admin.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {admin.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                        <button
                          onClick={() => {
                            setSelectedAdmin(admin);
                            setShowEditModal(true);
                          }}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => clearBilling(admin._id)}
                          disabled={actionLoading}
                          className="text-red-600 hover:text-red-900 disabled:opacity-50"
                        >
                          Clear Billing
                        </button>
                        <button
                          onClick={() => markPayment(admin._id, dashboardData?.billingCycles.current || '')}
                          disabled={actionLoading}
                          className="text-green-600 hover:text-green-900 disabled:opacity-50"
                        >
                          Mark Paid
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* User Management Tab */}
        {activeTab === 'users' && (
          <div className="space-y-6">
            {/* User Management Header */}
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-gray-900">User Management</h3>
                <div className="flex space-x-3">
                  <button
                    onClick={() => setShowAddUserModal(true)}
                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                  >
                    Add User
                  </button>
                  {selectedUsers.length > 0 && (
                    <button
                      onClick={() => setShowBulkModal(true)}
                      className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
                    >
                      Bulk Actions ({selectedUsers.length})
                    </button>
                  )}
                </div>
              </div>

              {/* Statistics */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-blue-900">Total Users</h4>
                  <p className="text-2xl font-bold text-blue-600">{users.length + orphanedUsers.length}</p>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-green-900">Assigned Users</h4>
                  <p className="text-2xl font-bold text-green-600">{users.length}</p>
                </div>
                <div className="bg-yellow-50 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-yellow-900">Unassigned Users</h4>
                  <p className="text-2xl font-bold text-yellow-600">{orphanedUsers.length}</p>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-purple-900">Active Admins</h4>
                  <p className="text-2xl font-bold text-purple-600">{admins.filter(a => a.isActive).length}</p>
                </div>
              </div>
            </div>

            {/* Users Table */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h4 className="text-lg font-semibold text-gray-900">All Users</h4>
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <input
                          type="checkbox"
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedUsers([...users.map(u => u._id), ...orphanedUsers.map(u => u._id)]);
                            } else {
                              setSelectedUsers([]);
                            }
                          }}
                          checked={selectedUsers.length === users.length + orphanedUsers.length && (users.length + orphanedUsers.length) > 0}
                        />
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User Code</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Admin</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {[...users, ...orphanedUsers].map((user) => (
                      <tr key={user._id} className={selectedUsers.includes(user._id) ? 'bg-blue-50' : ''}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <input
                            type="checkbox"
                            checked={selectedUsers.includes(user._id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedUsers([...selectedUsers, user._id]);
                              } else {
                                setSelectedUsers(selectedUsers.filter(id => id !== user._id));
                              }
                            }}
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-mono font-bold text-blue-600">{user.userCode || 'N/A'}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{user.name}</div>
                            <div className="text-sm text-gray-500">{user.email}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {user.adminName || 'Unassigned'}
                          {user.adminEmail && <div className="text-xs text-gray-500">{user.adminEmail}</div>}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {user.companyName || user.adminCompany || 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                          }`}>
                            {user.isActive ? 'Active' : 'Inactive'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(user.createdAt).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                          <button
                            onClick={() => {
                              setSelectedUser(user);
                              setShowEditUserModal(true);
                            }}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            Edit
                          </button>
                          <button
                            onClick={() => deleteUser(user._id)}
                            disabled={actionLoading}
                            className="text-red-600 hover:text-red-900 disabled:opacity-50"
                          >
                            Delete
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {/* Invitations Tab */}
        {activeTab === 'invitations' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-semibold text-gray-900">Admin Invitations</h3>
                <a
                  href="/super-admin/invite-admin"
                  className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                >
                  Send New Invitation
                </a>
              </div>

              {/* Invitation Statistics */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-blue-900">Total Invitations</h4>
                  <p className="text-2xl font-bold text-blue-600">
                    {admins.filter(a => a.invitationToken || a.isInvitationAccepted).length}
                  </p>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-green-900">Accepted</h4>
                  <p className="text-2xl font-bold text-green-600">
                    {admins.filter(a => a.isInvitationAccepted).length}
                  </p>
                </div>
                <div className="bg-yellow-50 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-yellow-900">Pending</h4>
                  <p className="text-2xl font-bold text-yellow-600">
                    {admins.filter(a => a.invitationToken && !a.isInvitationAccepted).length}
                  </p>
                </div>
                <div className="bg-red-50 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-red-900">Expired</h4>
                  <p className="text-2xl font-bold text-red-600">
                    {admins.filter(a => a.invitationExpiry && new Date() > new Date(a.invitationExpiry) && !a.isInvitationAccepted).length}
                  </p>
                </div>
              </div>

              {/* Invitations Table */}
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Admin</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sent Date</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expires</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {admins.filter(admin => admin.invitationToken || admin.isInvitationAccepted).map((admin) => {
                      const isExpired = admin.invitationExpiry && new Date() > new Date(admin.invitationExpiry);
                      const isPending = admin.invitationToken && !admin.isInvitationAccepted && !isExpired;
                      const isAccepted = admin.isInvitationAccepted;

                      return (
                        <tr key={admin._id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900">{admin.name}</div>
                              <div className="text-sm text-gray-500">{admin.email}</div>
                              <div className="text-sm text-gray-500">{admin.companyName}</div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              isAccepted
                                ? 'bg-green-100 text-green-800'
                                : isPending
                                ? 'bg-yellow-100 text-yellow-800'
                                : isExpired
                                ? 'bg-red-100 text-red-800'
                                : 'bg-gray-100 text-gray-800'
                            }`}>
                              {isAccepted ? 'Accepted' : isPending ? 'Pending' : isExpired ? 'Expired' : 'Unknown'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {admin.createdAt ? new Date(admin.createdAt).toLocaleDateString() : 'N/A'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {admin.invitationExpiry ? new Date(admin.invitationExpiry).toLocaleDateString() : 'N/A'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                            {isPending && (
                              <>
                                <button
                                  onClick={() => {
                                    const inviteLink = `${window.location.origin}/accept-invitation/${admin.invitationToken}`;
                                    navigator.clipboard.writeText(inviteLink);
                                    alert('Invitation link copied to clipboard!');
                                  }}
                                  className="text-blue-600 hover:text-blue-900"
                                >
                                  Copy Link
                                </button>
                                <button
                                  onClick={async () => {
                                    if (confirm('Are you sure you want to resend this invitation?')) {
                                      try {
                                        const response = await fetch('/api/super-admin/invite-admin', {
                                          method: 'POST',
                                          headers: { 'Content-Type': 'application/json' },
                                          credentials: 'include',
                                          body: JSON.stringify({
                                            email: admin.email,
                                            name: admin.name,
                                            companyName: admin.companyName,
                                            orderRate: admin.orderRate || 5
                                          })
                                        });
                                        if (response.ok) {
                                          alert('Invitation resent successfully!');
                                          fetchAdmins();
                                        } else {
                                          alert('Failed to resend invitation');
                                        }
                                      } catch (error) {
                                        alert('Failed to resend invitation');
                                      }
                                    }
                                  }}
                                  className="text-green-600 hover:text-green-900"
                                >
                                  Resend
                                </button>
                              </>
                            )}
                            {isExpired && (
                              <button
                                onClick={async () => {
                                  if (confirm('Are you sure you want to send a new invitation?')) {
                                    try {
                                      const response = await fetch('/api/super-admin/invite-admin', {
                                        method: 'POST',
                                        headers: { 'Content-Type': 'application/json' },
                                        credentials: 'include',
                                        body: JSON.stringify({
                                          email: admin.email,
                                          name: admin.name,
                                          companyName: admin.companyName,
                                          orderRate: admin.orderRate || 5
                                        })
                                      });
                                      if (response.ok) {
                                        alert('New invitation sent successfully!');
                                        fetchAdmins();
                                      } else {
                                        alert('Failed to send new invitation');
                                      }
                                    } catch (error) {
                                      alert('Failed to send new invitation');
                                    }
                                  }
                                }}
                                className="text-blue-600 hover:text-blue-900"
                              >
                                Send New
                              </button>
                            )}
                            {isAccepted && (
                              <span className="text-green-600">✓ Active</span>
                            )}
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>

              {admins.filter(admin => admin.invitationToken || admin.isInvitationAccepted).length === 0 && (
                <div className="text-center py-8">
                  <p className="text-gray-500">No invitations sent yet.</p>
                  <a
                    href="/super-admin/invite-admin"
                    className="text-blue-600 hover:text-blue-800 font-medium"
                  >
                    Send your first invitation →
                  </a>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Billing Tab */}
        {activeTab === 'billing' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Billing Management</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button
                  onClick={() => clearBilling()}
                  disabled={actionLoading}
                  className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 disabled:opacity-50"
                >
                  Clear All Billing
                </button>
                <button
                  onClick={() => setShowBillingModal(true)}
                  className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                >
                  Generate Bills
                </button>
                <button
                  onClick={() => setShowSettlementModal(true)}
                  className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
                >
                  Settlement
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Settlement Tab */}
        {activeTab === 'settlement' && (
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Settlement Management</h3>
            <p className="text-gray-600 mb-4">Manage billing cycles, generate reports, and handle settlements.</p>
            <div className="space-y-4">
              <button
                onClick={() => setShowSettlementModal(true)}
                className="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700"
              >
                Open Settlement Dashboard
              </button>
            </div>
          </div>
        )}

        {!dashboardData && !isDataLoading && (
          <div className="text-center py-12">
            <p className="text-gray-500">No dashboard data available</p>
          </div>
        )}
      </div>

      {/* Edit Admin Modal */}
      {showEditModal && selectedAdmin && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <h3 className="text-lg font-bold text-gray-900 mb-4">Edit Admin</h3>
            <form onSubmit={(e) => {
              e.preventDefault();
              const formData = new FormData(e.target as HTMLFormElement);
              const updates = {
                name: formData.get('name'),
                email: formData.get('email'),
                companyName: formData.get('companyName'),
                orderRate: Number(formData.get('orderRate')),
                isActive: formData.get('isActive') === 'on'
              };
              updateAdmin(selectedAdmin._id, updates);
            }}>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Name</label>
                  <input
                    type="text"
                    name="name"
                    defaultValue={selectedAdmin.name}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Email</label>
                  <input
                    type="email"
                    name="email"
                    defaultValue={selectedAdmin.email}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Company Name</label>
                  <input
                    type="text"
                    name="companyName"
                    defaultValue={selectedAdmin.companyName}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Order Rate (₹)</label>
                  <input
                    type="number"
                    name="orderRate"
                    defaultValue={selectedAdmin.orderRate}
                    min="0"
                    step="0.01"
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    required
                  />
                </div>
                <div>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      name="isActive"
                      defaultChecked={selectedAdmin.isActive}
                      className="mr-2"
                    />
                    <span className="text-sm font-medium text-gray-700">Active</span>
                  </label>
                </div>
              </div>
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={() => {
                    setShowEditModal(false);
                    setSelectedAdmin(null);
                  }}
                  className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={actionLoading}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  {actionLoading ? 'Updating...' : 'Update'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Edit User Modal */}
      {showEditUserModal && selectedUser && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <h3 className="text-lg font-bold text-gray-900 mb-4">Edit User</h3>
            <form onSubmit={(e) => {
              e.preventDefault();
              const formData = new FormData(e.target as HTMLFormElement);
              const updates = {
                name: formData.get('name'),
                email: formData.get('email'),
                companyName: formData.get('companyName'),
                phoneNumber: formData.get('phoneNumber'),
                address: formData.get('address'),
                adminId: formData.get('adminId') || undefined,
                isActive: formData.get('isActive') === 'on'
              };
              updateUser(selectedUser._id, updates);
            }}>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Name</label>
                  <input
                    type="text"
                    name="name"
                    defaultValue={selectedUser.name}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Email</label>
                  <input
                    type="email"
                    name="email"
                    defaultValue={selectedUser.email}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Admin</label>
                  <select
                    name="adminId"
                    defaultValue={selectedUser.adminId || ''}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                  >
                    <option value="">Unassigned</option>
                    {admins.map((admin) => (
                      <option key={admin._id} value={admin._id}>
                        {admin.name} ({admin.email})
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Company Name</label>
                  <input
                    type="text"
                    name="companyName"
                    defaultValue={selectedUser.companyName}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Phone Number</label>
                  <input
                    type="text"
                    name="phoneNumber"
                    defaultValue={selectedUser.phoneNumber}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Address</label>
                  <textarea
                    name="address"
                    defaultValue={selectedUser.address}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    rows={3}
                  />
                </div>
                <div>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      name="isActive"
                      defaultChecked={selectedUser.isActive}
                      className="mr-2"
                    />
                    <span className="text-sm font-medium text-gray-700">Active</span>
                  </label>
                </div>
              </div>
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={() => {
                    setShowEditUserModal(false);
                    setSelectedUser(null);
                  }}
                  className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={actionLoading}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  {actionLoading ? 'Updating...' : 'Update'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Add User Modal */}
      {showAddUserModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <h3 className="text-lg font-bold text-gray-900 mb-4">Add New User</h3>
            <form onSubmit={async (e) => {
              e.preventDefault();
              const formData = new FormData(e.target as HTMLFormElement);
              const userData = {
                name: formData.get('name'),
                email: formData.get('email'),
                password: formData.get('password'),
                companyName: formData.get('companyName'),
                phoneNumber: formData.get('phoneNumber'),
                address: formData.get('address'),
                adminId: formData.get('adminId') || undefined,
              };

              try {
                setActionLoading(true);
                const response = await fetch('/api/super-admin/users', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  credentials: 'include',
                  body: JSON.stringify(userData)
                });

                if (response.ok) {
                  alert('User created successfully');
                  fetchUsers();
                  setShowAddUserModal(false);
                } else {
                  const error = await response.json();
                  alert(error.error || 'Failed to create user');
                }
              } catch (error) {
                console.error('Error creating user:', error);
                alert('Failed to create user');
              } finally {
                setActionLoading(false);
              }
            }}>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Name *</label>
                  <input
                    type="text"
                    name="name"
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Email *</label>
                  <input
                    type="email"
                    name="email"
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Password *</label>
                  <input
                    type="password"
                    name="password"
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    required
                    minLength={6}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Assign to Admin</label>
                  <select
                    name="adminId"
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                  >
                    <option value="">Unassigned</option>
                    {admins.map((admin) => (
                      <option key={admin._id} value={admin._id}>
                        {admin.name} ({admin.email})
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={() => setShowAddUserModal(false)}
                  className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={actionLoading}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  {actionLoading ? 'Creating...' : 'Create User'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Bulk Operations Modal */}
      {showBulkModal && bulkOptions && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <h3 className="text-lg font-bold text-gray-900 mb-4">
              Bulk Operations ({selectedUsers.length} users selected)
            </h3>
            <div className="space-y-4">
              {bulkOptions.availableActions.map((action: any) => (
                <div key={action.id}>
                  {action.requiresAdmin ? (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {action.name}
                      </label>
                      <select
                        className="w-full border border-gray-300 rounded-md px-3 py-2 mb-2"
                        onChange={(e) => {
                          if (e.target.value) {
                            performBulkOperation(action.id, e.target.value);
                          }
                        }}
                        defaultValue=""
                      >
                        <option value="">Select Admin...</option>
                        {bulkOptions.availableAdmins.map((admin: any) => (
                          <option key={admin._id} value={admin._id}>
                            {admin.name} ({admin.email})
                          </option>
                        ))}
                      </select>
                    </div>
                  ) : (
                    <button
                      onClick={() => performBulkOperation(action.id)}
                      disabled={actionLoading}
                      className={`w-full px-4 py-2 rounded-md text-white disabled:opacity-50 ${
                        action.id === 'delete'
                          ? 'bg-red-600 hover:bg-red-700'
                          : action.id === 'activate'
                          ? 'bg-green-600 hover:bg-green-700'
                          : action.id === 'deactivate'
                          ? 'bg-yellow-600 hover:bg-yellow-700'
                          : 'bg-blue-600 hover:bg-blue-700'
                      }`}
                    >
                      {action.name}
                    </button>
                  )}
                </div>
              ))}
            </div>
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowBulkModal(false);
                  setSelectedUsers([]);
                }}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
