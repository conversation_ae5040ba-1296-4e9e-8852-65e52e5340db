'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';

import BillingLogs from '@/components/BillingLogs';
import AdminRateEditor from '@/components/AdminRateEditor';
import BillingAnalytics from '@/components/BillingAnalytics';
import BillingManagement from '@/components/BillingManagement';
import PaymentHistory from '@/components/PaymentHistory';
import OrderCleanup from '@/components/OrderCleanup';
import AdminManagement from '@/components/AdminManagement';
import ActivityLogs from '@/components/ActivityLogs';
import ErrorBoundary from '@/components/ErrorBoundary';

interface AccountData {
  angelAccounts: any[];
  motilalAccounts: any[];
  pagination: {
    page: number;
    limit: number;
    totalAngel: number;
    totalMotilal: number;
    totalPages: number;
  };
  summary: {
    totalAngel: number;
    totalMotilal: number;
    totalAccounts: number;
    activeAngel: number;
    activeMotilal: number;
  };
}

interface DashboardData {
  overview: {
    totalAdmins: number;
    totalUsers: number;
    activeAdmins: number;
    currentMonth: {
      totalOrders: number;
      totalRevenue: number;
      pendingAmount: number;
      paidAmount: number;
    };
    previousMonth: {
      totalOrders: number;
      totalRevenue: number;
    };
    growth: {
      orders: number;
      revenue: number;
    };
  };
  admins: any[];
  currentMonthBilling: any[];
  recentOrders: any[];
  adminPerformance: any[];
}

interface AdminData {
  admins: any[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

interface UserData {
  users: any[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

interface BillingData {
  billingSummaries: any[];
  billingRecords: any[];
  summary: {
    totalRevenue: number;
    pendingAmount: number;
    paidAmount: number;
    totalOrders: number;
  };
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export default function SuperAdminDashboard() {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [accountData, setAccountData] = useState<AccountData | null>(null);
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [adminData, setAdminData] = useState<AdminData | null>(null);
  const [userData, setUserData] = useState<UserData | null>(null);
  const [billingData, setBillingData] = useState<BillingData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Order placement state
  const [showOrderForm, setShowOrderForm] = useState(false);
  const [orderType, setOrderType] = useState<'BUY' | 'SELL'>('BUY');
  const [selectedScript, setSelectedScript] = useState<any>(null);
  const [showAdminRateEditor, setShowAdminRateEditor] = useState(false);

  // Redirect if not super admin
  useEffect(() => {
    if (authLoading) return; // Wait for auth to complete

    if (!user) {
      router.push('/login');
      return;
    }

    if (user.role !== 'super_admin') {
      router.push('/');
      return;
    }
  }, [user, authLoading, router]);

  // Fetch dashboard data
  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError('');

      const response = await fetch('/api/super-admin/dashboard', {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch dashboard data: ${response.status}`);
      }

      const result = await response.json();
      if (result.success) {
        setDashboardData(result.data);
      } else {
        setError(result.error || 'Failed to load dashboard data');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch dashboard data');
    } finally {
      setLoading(false);
    }
  };

  // Fetch account data
  const fetchAccountData = async (type: string = 'all') => {
    try {
      setLoading(true);
      setError('');

      const response = await fetch(`/api/super-admin/accounts?type=${type}`, {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch accounts: ${response.status}`);
      }

      const result = await response.json();
      if (result.success) {
        setAccountData(result.data);
      } else {
        setError(result.error || 'Failed to load account data');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch account data');
    } finally {
      setLoading(false);
    }
  };

  // Fetch admin data
  const fetchAdminData = async () => {
    try {
      setLoading(true);
      setError('');

      const response = await fetch('/api/super-admin/admins', {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch admin data: ${response.status}`);
      }

      const result = await response.json();
      if (result.success) {
        setAdminData(result.data);
      } else {
        setError(result.error || 'Failed to load admin data');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch admin data');
    } finally {
      setLoading(false);
    }
  };

  // Fetch user data
  const fetchUserData = async () => {
    try {
      setLoading(true);
      setError('');
      setUserData(null); // Clear existing data

      const response = await fetch('/api/super-admin/users', {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('User Data API Error Response:', errorText);
        throw new Error(`Failed to fetch user data: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success && result.data) {
        // Validate data structure
        const userData = {
          users: Array.isArray(result.data.users) ? result.data.users : [],
          admins: Array.isArray(result.data.admins) ? result.data.admins : [],
          orphanedUsers: Array.isArray(result.data.orphanedUsers) ? result.data.orphanedUsers : [],
          totalAdmins: result.data.totalAdmins || 0,
          totalUsers: result.data.totalUsers || 0,
          totalOrphanedUsers: result.data.totalOrphanedUsers || 0,
          pagination: {
            page: 1,
            limit: 50,
            total: result.data.totalUsers || 0,
            totalPages: 1,
          },
        };
        setUserData(userData);

        if (userData.users.length === 0 && userData.orphanedUsers.length === 0) {
          setError('No user data found');
        }
      } else {
        const errorMsg = result.error || 'Invalid response format';
        console.error('Invalid user data response:', result);
        setError(errorMsg);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch user data';
      console.error('Error fetching user data:', err);
      setError(errorMessage);
      setUserData(null);
    } finally {
      setLoading(false);
    }
  };

  // Fetch billing data
  const fetchBillingData = async () => {
    try {
      setLoading(true);
      setError('');

      console.log('🔄 Fetching billing data...');
      const response = await fetch('/api/super-admin/billing', {
        credentials: 'include'
      });

      console.log('📡 Billing API response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Billing API error:', errorText);
        throw new Error(`Failed to fetch billing data: ${response.status}`);
      }

      const result = await response.json();
      console.log('📊 Billing API result:', result);

      if (result.success) {
        setBillingData(result.data);
        console.log('✅ Billing data set successfully');
      } else {
        console.error('❌ Billing API returned error:', result.error);
        setError(result.error || 'Failed to load billing data');
      }
    } catch (err) {
      console.error('💥 Error fetching billing data:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch billing data');
    } finally {
      setLoading(false);
    }
  };

  // Order placement functions
  const handlePlaceOrder = (type: 'BUY' | 'SELL', script?: any) => {
    setOrderType(type);
    setSelectedScript(script || {
      ticker: 'CUSTOM',
      securityId: 'CUSTOM',
      ltp: 0,
      exchange: 'NSE'
    });
    setShowOrderForm(true);
  };

  const handleCloseOrderForm = () => {
    setShowOrderForm(false);
    setSelectedScript(null);
  };

  // Load data when tab changes
  useEffect(() => {
    if (user && user.role === 'super_admin') {
      switch (activeTab) {
        case 'dashboard':
          fetchDashboardData();
          break;
        case 'admins':
          fetchAdminData();
          break;
        case 'users':
          fetchUserData();
          break;
        case 'billing':
          fetchBillingData();
          break;
        case 'angel-accounts':
          fetchAccountData('angel');
          break;
        case 'motilal-accounts':
          fetchAccountData('motilal');
          break;
        case 'all-accounts':
          fetchAccountData('all');
          break;
      }
    }
  }, [activeTab, user]);

  // Show loading while checking authentication
  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  // Don't render anything if user is not super admin (will redirect)
  if (!user || user.role !== 'super_admin') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Redirecting...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Super Admin Dashboard</h1>
          <p className="text-gray-600">Manage all accounts and system overview</p>
        </div>

        {/* Navigation Tabs */}
        <div className="mb-6">
          <nav className="flex flex-wrap gap-2">
            {[
              { id: 'dashboard', label: 'Dashboard', icon: '📊' },
              { id: 'admins', label: 'Admin Management', icon: '👨‍💼' },
              { id: 'users', label: 'User Management', icon: '👥' },
              { id: 'billing', label: 'Billing Management', icon: '💰' },
              { id: 'payment-history', label: 'Payment History', icon: '💸' },
              { id: 'billing-logs', label: 'Billing Logs', icon: '📋' },
              { id: 'billing-analytics', label: 'Billing Analytics', icon: '📊' },
              { id: 'order-cleanup', label: 'Order Cleanup', icon: '🗑️' },
              { id: 'activity-logs', label: 'Activity Logs', icon: '📝' },
              { id: 'all-accounts', label: 'All Accounts', icon: '🏦' },
              { id: 'angel-accounts', label: 'Angel Accounts', icon: '👼' },
              { id: 'motilal-accounts', label: 'Motilal Accounts', icon: '🏢' },
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                  activeTab === tab.id
                    ? 'bg-indigo-100 text-indigo-700 border border-indigo-200'
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Content */}
        {activeTab === 'dashboard' && (
          <ErrorBoundary
            fallback={
              <div className="bg-white shadow rounded-lg p-6">
                <div className="text-center">
                  <div className="text-red-600 text-4xl mb-4">⚠️</div>
                  <h3 className="text-lg font-semibold text-red-800 mb-2">
                    Dashboard Error
                  </h3>
                  <p className="text-red-600 mb-4">
                    Failed to load dashboard data. Please try refreshing the page.
                  </p>
                  <button
                    onClick={() => window.location.reload()}
                    className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
                  >
                    Reload
                  </button>
                </div>
              </div>
            }
          >
            <div className="space-y-6">
            {/* Overview Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                        <span className="text-white font-bold">👨‍💼</span>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          Total Admins
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {dashboardData?.overview?.totalAdmins || 0}
                        </dd>
                        <dd className="text-xs text-green-600">
                          {dashboardData?.overview?.activeAdmins || 0} active
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                        <span className="text-white font-bold">👥</span>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          Total Users
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {dashboardData?.overview?.totalUsers || 0}
                        </dd>
                        <dd className="text-xs text-blue-600">
                          All user accounts
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                        <span className="text-white font-bold">📈</span>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          Monthly Orders
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {dashboardData?.overview?.currentMonth?.totalOrders || 0}
                        </dd>
                        <dd className={`text-xs ${
                          (dashboardData?.overview?.growth?.orders || 0) >= 0
                            ? 'text-green-600'
                            : 'text-red-600'
                        }`}>
                          {(dashboardData?.overview?.growth?.orders || 0) >= 0 ? '+' : ''}
                          {dashboardData?.overview?.growth?.orders || 0}% from last month
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                        <span className="text-white font-bold">💰</span>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          Monthly Revenue
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          ₹{(dashboardData?.overview?.currentMonth?.totalRevenue || 0).toLocaleString()}
                        </dd>
                        <dd className={`text-xs ${
                          (dashboardData?.overview?.growth?.revenue || 0) >= 0
                            ? 'text-green-600'
                            : 'text-red-600'
                        }`}>
                          {(dashboardData?.overview?.growth?.revenue || 0) >= 0 ? '+' : ''}
                          {dashboardData?.overview?.growth?.revenue || 0}% from last month
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Billing Summary */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-white shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Billing Overview</h3>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Pending Amount</span>
                      <span className="text-lg font-semibold text-red-600">
                        ₹{(dashboardData?.overview?.currentMonth?.pendingAmount || 0).toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Paid Amount</span>
                      <span className="text-lg font-semibold text-green-600">
                        ₹{(dashboardData?.overview?.currentMonth?.paidAmount || 0).toLocaleString()}
                      </span>
                    </div>
                    <div className="border-t pt-4">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-gray-900">Total Revenue</span>
                        <span className="text-xl font-bold text-gray-900">
                          ₹{(dashboardData?.overview?.currentMonth?.totalRevenue || 0).toLocaleString()}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
                  <div className="space-y-3">
                    {dashboardData?.recentOrders?.slice(0, 5).map((order, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            Order #{order.orderId || 'N/A'}
                          </p>
                          <p className="text-xs text-gray-500">
                            {order.createdAt ? new Date(order.createdAt).toLocaleDateString() : 'N/A'}
                          </p>
                        </div>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          order.status === 'complete'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {order.status || 'pending'}
                        </span>
                      </div>
                    )) || (
                      <p className="text-sm text-gray-500">No recent orders</p>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Admin Performance */}
            {dashboardData?.adminPerformance && dashboardData.adminPerformance.length > 0 && (
              <div className="bg-white shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Admin Performance</h3>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Admin
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Users
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Orders
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Revenue
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {dashboardData.adminPerformance.map((admin, index) => (
                          <tr key={index}>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div>
                                <div className="text-sm font-medium text-gray-900">{admin.name}</div>
                                <div className="text-sm text-gray-500">{admin.email}</div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {admin.userCount || 0}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {admin.totalOrders || 0}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              ₹{(admin.totalRevenue || 0).toLocaleString()}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}
          </div>
          </ErrorBoundary>
        )}

        {/* Admin Management */}
        {activeTab === 'admins' && (
          <div className="space-y-6">
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <AdminManagement onEditRates={() => setShowAdminRateEditor(true)} />
              </div>
            </div>
          </div>
        )}

        {/* User Management */}
        {activeTab === 'users' && (
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-medium text-gray-900">User Management</h3>
                <button
                  onClick={fetchUserData}
                  disabled={loading}
                  className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 disabled:opacity-50"
                >
                  {loading ? 'Loading...' : 'Refresh'}
                </button>
              </div>

              {error && (
                <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-4">
                  <div className="text-red-700">{error}</div>
                </div>
              )}

              {loading ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                </div>
              ) : userData ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          User Details
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Admin
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          User Code
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Created
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {userData?.users?.length > 0 ? userData.users.map((user) => (
                        <tr key={user._id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900">{user.name}</div>
                              <div className="text-sm text-gray-500">{user.email}</div>
                              <div className="text-xs text-gray-400">{user.companyName || 'No company'}</div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">
                              {user.adminId?.name || 'No Admin'}
                            </div>
                            <div className="text-sm text-gray-500">
                              {user.adminId?.email || 'N/A'}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {user.userCode || 'N/A'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              user.isActive
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {user.isActive ? 'Active' : 'Inactive'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {user.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'N/A'}
                          </td>
                        </tr>
                      )) : (
                        <tr>
                          <td colSpan={5} className="px-6 py-4 text-center text-gray-500">
                            No users found
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="text-gray-500">No user data available</div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Billing Management */}
        {activeTab === 'billing' && (
          <div className="space-y-6">
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-lg font-medium text-gray-900">Billing Management</h3>
                  <div className="flex space-x-3">
                    <button
                      onClick={() => setShowAdminRateEditor(true)}
                      className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700"
                    >
                      Edit Admin Rates
                    </button>
                    <button
                      onClick={fetchBillingData}
                      disabled={loading}
                      className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 disabled:opacity-50"
                    >
                      {loading ? 'Loading...' : 'Refresh'}
                    </button>
                  </div>
                </div>

                {/* Error Display */}
                {error && (
                  <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-4">
                    <div className="text-red-700">{error}</div>
                  </div>
                )}

                {error && (
                  <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-4">
                    <div className="text-red-700">{error}</div>
                  </div>
                )}

                {billingData && billingData.summary && (
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <h4 className="font-medium text-blue-900">Total Orders</h4>
                      <p className="text-2xl font-bold text-blue-600">
                        {typeof billingData.summary.totalOrders === 'number' ? billingData.summary.totalOrders : 0}
                      </p>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg">
                      <h4 className="font-medium text-green-900">Total Revenue</h4>
                      <p className="text-2xl font-bold text-green-600">
                        ₹{(typeof billingData.summary.totalRevenue === 'number' ? billingData.summary.totalRevenue : 0).toLocaleString()}
                      </p>
                    </div>
                    <div className="bg-yellow-50 p-4 rounded-lg">
                      <h4 className="font-medium text-yellow-900">Pending Amount</h4>
                      <p className="text-2xl font-bold text-yellow-600">
                        ₹{(typeof billingData.summary.pendingAmount === 'number' ? billingData.summary.pendingAmount : 0).toLocaleString()}
                      </p>
                    </div>
                    <div className="bg-purple-50 p-4 rounded-lg">
                      <h4 className="font-medium text-purple-900">Paid Amount</h4>
                      <p className="text-2xl font-bold text-purple-600">
                        ₹{(typeof billingData.summary.paidAmount === 'number' ? billingData.summary.paidAmount : 0).toLocaleString()}
                      </p>
                    </div>
                  </div>
                )}

                {/* Billing Management Component */}
                <BillingManagement
                  billingData={billingData}
                  loading={loading}
                  onRefresh={fetchBillingData}
                />
              </div>
            </div>
          </div>
        )}

        {/* Payment History */}
        {activeTab === 'payment-history' && (
          <div className="space-y-6">
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <PaymentHistory userRole="super-admin" />
              </div>
            </div>
          </div>
        )}

        {/* Billing Logs */}
        {activeTab === 'billing-logs' && (
          <div className="space-y-6">
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-lg font-medium text-gray-900">Billing Logs</h3>
                </div>
                <BillingLogs userRole="super-admin" />
              </div>
            </div>
          </div>
        )}

        {/* Order Cleanup */}
        {activeTab === 'order-cleanup' && (
          <div className="space-y-6">
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <OrderCleanup />
              </div>
            </div>
          </div>
        )}

        {/* Activity Logs */}
        {activeTab === 'activity-logs' && (
          <div className="space-y-6">
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <ActivityLogs />
              </div>
            </div>
          </div>
        )}

        {/* Billing Analytics */}
        {activeTab === 'billing-analytics' && (
          <div className="space-y-6">
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-lg font-medium text-gray-900">Billing Analytics</h3>
                </div>
                <BillingAnalytics userRole="super-admin" />
              </div>
            </div>
          </div>
        )}

        {/* Account Management Sections */}
        {(activeTab === 'all-accounts' || activeTab === 'angel-accounts' || activeTab === 'motilal-accounts') && (
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <div className="flex justify-between items-center mb-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">
                    {activeTab === 'all-accounts' && 'All Broker Accounts'}
                    {activeTab === 'angel-accounts' && 'Angel Broker Accounts'}
                    {activeTab === 'motilal-accounts' && 'Motilal Oswal Accounts'}
                  </h3>
                  <p className="text-sm text-gray-600 mt-1">
                    {activeTab === 'all-accounts' && 'Place orders for all broker accounts under all admins'}
                    {activeTab === 'angel-accounts' && 'Place orders for all Angel Broking accounts under all admins'}
                    {activeTab === 'motilal-accounts' && 'Place orders for all Motilal Oswal accounts under all admins'}
                  </p>
                </div>
                <div className="flex space-x-3">
                  {/* Order Placement Buttons */}
                  <button
                    onClick={() => handlePlaceOrder('BUY')}
                    className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 focus:ring-2 focus:ring-green-500"
                  >
                    🛒 Place BUY Order
                  </button>
                  <button
                    onClick={() => handlePlaceOrder('SELL')}
                    className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 focus:ring-2 focus:ring-red-500"
                  >
                    💰 Place SELL Order
                  </button>
                  <button
                    onClick={() => fetchAccountData(activeTab.replace('-accounts', ''))}
                    disabled={loading}
                    className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 disabled:opacity-50"
                  >
                    {loading ? 'Loading...' : 'Refresh'}
                  </button>
                </div>
              </div>

              {error && (
                <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-4">
                  <div className="text-red-700">{error}</div>
                </div>
              )}

              {loading ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                </div>
              ) : (
                <>
                  {/* Summary Cards */}
                  {accountData && (
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                      {(activeTab === 'all-accounts' || activeTab === 'angel-accounts') && (
                        <div className="bg-blue-50 p-4 rounded-lg">
                          <h4 className="font-medium text-blue-900">Angel Accounts</h4>
                          <p className="text-2xl font-bold text-blue-600">{accountData.summary.totalAngel}</p>
                          <p className="text-sm text-blue-700">{accountData.summary.activeAngel} active</p>
                        </div>
                      )}
                      {(activeTab === 'all-accounts' || activeTab === 'motilal-accounts') && (
                        <div className="bg-green-50 p-4 rounded-lg">
                          <h4 className="font-medium text-green-900">Motilal Accounts</h4>
                          <p className="text-2xl font-bold text-green-600">{accountData.summary.totalMotilal}</p>
                          <p className="text-sm text-green-700">{accountData.summary.activeMotilal} active</p>
                        </div>
                      )}
                      {activeTab === 'all-accounts' && (
                        <div className="bg-purple-50 p-4 rounded-lg">
                          <h4 className="font-medium text-purple-900">Total Accounts</h4>
                          <p className="text-2xl font-bold text-purple-600">{accountData.summary.totalAccounts}</p>
                          <p className="text-sm text-purple-700">All brokers combined</p>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Angel Accounts Table */}
                  {accountData && (activeTab === 'all-accounts' || activeTab === 'angel-accounts') && accountData.angelAccounts.length > 0 && (
                    <div className="mb-8">
                      <h4 className="text-lg font-medium text-gray-900 mb-4">Angel Broker Accounts</h4>
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Client Details
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Owner
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Capital
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Created
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {accountData.angelAccounts.map((account) => (
                              <tr key={account._id}>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <div>
                                    <div className="text-sm font-medium text-gray-900">{account.clientName}</div>
                                    <div className="text-sm text-gray-500">{account.userId}</div>
                                    <div className="text-xs text-gray-400">{account.email}</div>
                                  </div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <div>
                                    <div className="text-sm font-medium text-gray-900">
                                      {account.owner?.name || 'No Owner'}
                                    </div>
                                    <div className="text-sm text-gray-500">
                                      {account.owner?.userCode || 'N/A'}
                                    </div>
                                    <div className="text-xs text-gray-400">
                                      {account.owner?.role || 'N/A'}
                                    </div>
                                  </div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                  ₹{account.capital?.toLocaleString() || 0}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                    account.state === 'live'
                                      ? 'bg-green-100 text-green-800'
                                      : 'bg-red-100 text-red-800'
                                  }`}>
                                    {account.state || 'inactive'}
                                  </span>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                  {account.createdAt ? new Date(account.createdAt).toLocaleDateString() : 'N/A'}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}

                  {/* Motilal Accounts Table */}
                  {accountData && (activeTab === 'all-accounts' || activeTab === 'motilal-accounts') && accountData.motilalAccounts.length > 0 && (
                    <div className="mb-8">
                      <h4 className="text-lg font-medium text-gray-900 mb-4">Motilal Oswal Accounts</h4>
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Client Details
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Owner
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Capital
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Created
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {accountData.motilalAccounts.map((account) => (
                              <tr key={account._id}>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <div>
                                    <div className="text-sm font-medium text-gray-900">{account.clientName}</div>
                                    <div className="text-sm text-gray-500">{account.userId}</div>
                                    <div className="text-xs text-gray-400">{account.email}</div>
                                  </div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <div>
                                    <div className="text-sm font-medium text-gray-900">
                                      {account.owner?.name || 'No Owner'}
                                    </div>
                                    <div className="text-sm text-gray-500">
                                      {account.owner?.userCode || 'N/A'}
                                    </div>
                                    <div className="text-xs text-gray-400">
                                      {account.owner?.role || 'N/A'}
                                    </div>
                                  </div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                  ₹{account.capital?.toLocaleString() || 0}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                    account.status === 'active'
                                      ? 'bg-green-100 text-green-800'
                                      : 'bg-red-100 text-red-800'
                                  }`}>
                                    {account.status || 'inactive'}
                                  </span>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                  {account.createdAt ? new Date(account.createdAt).toLocaleDateString() : 'N/A'}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}

                  {/* No Data Message */}
                  {accountData &&
                   ((activeTab === 'angel-accounts' && accountData.angelAccounts.length === 0) ||
                    (activeTab === 'motilal-accounts' && accountData.motilalAccounts.length === 0) ||
                    (activeTab === 'all-accounts' && accountData.angelAccounts.length === 0 && accountData.motilalAccounts.length === 0)) && (
                    <div className="text-center py-8">
                      <div className="text-gray-500">
                        <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <h3 className="mt-2 text-sm font-medium text-gray-900">No accounts found</h3>
                        <p className="mt-1 text-sm text-gray-500">
                          {activeTab === 'angel-accounts' && 'No Angel broker accounts are currently configured.'}
                          {activeTab === 'motilal-accounts' && 'No Motilal Oswal accounts are currently configured.'}
                          {activeTab === 'all-accounts' && 'No broker accounts are currently configured.'}
                        </p>
                      </div>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        )}

        {/* Order Placement Form */}
        {showOrderForm && selectedScript && (
          <TradingOrderForm
            isOpen={showOrderForm}
            onClose={handleCloseOrderForm}
            orderType={orderType}
            scriptData={selectedScript}
            bulkOrderContext={
              activeTab === 'all-accounts' ? 'all-accounts' :
              activeTab === 'angel-accounts' ? 'angel-accounts' :
              activeTab === 'motilal-accounts' ? 'motilal-accounts' :
              null
            }
          />
        )}

        {/* Admin Rate Editor */}
        {showAdminRateEditor && (
          <AdminRateEditor
            isOpen={showAdminRateEditor}
            onClose={() => setShowAdminRateEditor(false)}
            onRateUpdated={() => {
              fetchDashboardData();
              if (activeTab === 'admins') {
                fetchAdminData();
              }
            }}
          />
        )}
      </div>
    </div>
  );
}
