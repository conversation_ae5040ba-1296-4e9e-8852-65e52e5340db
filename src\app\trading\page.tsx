"use client";

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { io } from "socket.io-client";
import { useAuth } from '@/contexts/AuthContext';
import MarketDataTable from '@/components/MarketDataTable';
import TradingOrderForm from '@/components/TradingOrderForm';
import OrderManagement from '@/components/OrderManagement';
import BrokerLoginManager from '@/components/BrokerLoginManager';
import UserManagement from '@/components/UserManagement';
import { useRouter } from 'next/navigation';
import { CLIENT_CONFIG } from '@/config/client';

interface MarketData {
  ticker: string;
  securityId: string;
  exchange: string;
  exchangeCode: number;
  ltp: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  close: number;
  timestamp: number;
}

export default function TradingPage() {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [socket, setSocket] = useState<any>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [marketData, setMarketData] = useState<Map<string, MarketData>>(new Map());
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedScript, setSelectedScript] = useState<MarketData | null>(null);
  const [selectedOrderType, setSelectedOrderType] = useState<'BUY' | 'SELL'>('BUY');
  const [activeTab, setActiveTab] = useState<'market' | 'user-management'>('market');
  const [showOrderForm, setShowOrderForm] = useState(false);
  const [showOrderManagement, setShowOrderManagement] = useState(false);
  const [showBrokerLogin, setShowBrokerLogin] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(25);
  const [sortBy, setSortBy] = useState<'ticker' | 'ltp' | 'change' | 'volume'>('ticker');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // Optimized WebSocket functions for market data updates with throttling
  const updateMarketDataSingle = useCallback((data: MarketData) => {
    setMarketData((prev) => {
      const existing = prev.get(data.securityId);
      if (existing && existing.timestamp >= data.timestamp) {
        return prev; // Skip if we already have newer data
      }

      // Create new map only when necessary
      const newData = new Map(prev);
      newData.set(data.securityId, data);
      return newData;
    });
  }, []);

  // Batch processing with throttling for better performance
  const [pendingUpdates, setPendingUpdates] = useState<MarketData[]>([]);
  const [updateTimer, setUpdateTimer] = useState<NodeJS.Timeout | null>(null);

  const updateMarketDataBatch = useCallback((batch: MarketData[]) => {
    if (batch.length === 0) return;

    // Add to pending updates
    setPendingUpdates(prev => [...prev, ...batch]);

    // Clear existing timer
    if (updateTimer) {
      clearTimeout(updateTimer);
    }

    // Set new timer for batch processing
    const timer = setTimeout(() => {
      setPendingUpdates(currentPending => {
        if (currentPending.length === 0) return currentPending;

        setMarketData((prev) => {
          const newData = new Map(prev);
          let hasChanges = false;

          // Process all pending updates
          currentPending.forEach((data) => {
            const existing = newData.get(data.securityId);
            if (!existing || existing.timestamp < data.timestamp) {
              newData.set(data.securityId, data);
              hasChanges = true;
            }
          });

          return hasChanges ? newData : prev;
        });

        return []; // Clear pending updates
      });
    }, 50); // 50ms throttle

    setUpdateTimer(timer);
  }, [updateTimer]);

  // Optimized filter, sort and paginate market data with better memoization
  const filteredData = useMemo(() => {
    const allData = Array.from(marketData.values());

    if (!searchTerm) return allData;

    const search = searchTerm.toLowerCase();
    return allData.filter(item =>
      item.ticker.toLowerCase().includes(search) ||
      item.securityId.includes(search)
    );
  }, [marketData, searchTerm]);

  const sortedData = useMemo(() => {
    if (sortBy === 'ticker') {
      return [...filteredData].sort((a, b) => {
        const comparison = a.ticker.localeCompare(b.ticker);
        return sortOrder === 'asc' ? comparison : -comparison;
      });
    } else {
      // Numeric sorting with optimized getValue function
      const getValue = sortBy === 'ltp' ? (item: MarketData) => item.ltp :
                      sortBy === 'change' ? (item: MarketData) => item.change :
                      sortBy === 'volume' ? (item: MarketData) => item.volume :
                      (item: MarketData) => item.ltp;

      return [...filteredData].sort((a, b) => {
        const comparison = getValue(a) - getValue(b);
        return sortOrder === 'asc' ? comparison : -comparison;
      });
    }
  }, [filteredData, sortBy, sortOrder]);

  const processedData = useMemo(() => {
    const startTime = performance.now();

    return filtered;
  }, [marketData, searchTerm, sortBy, sortOrder]);

  // Paginated data
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return processedData.slice(startIndex, endIndex);
  }, [processedData, currentPage, itemsPerPage]);

  // Pagination info
  const totalPages = Math.ceil(processedData.length / itemsPerPage);
  const startItem = processedData.length > 0 ? (currentPage - 1) * itemsPerPage + 1 : 0;
  const endItem = Math.min(currentPage * itemsPerPage, processedData.length);

  // WebSocket connection effect
  useEffect(() => {
    const socketInstance = io(CLIENT_CONFIG.WS_SERVER_URL, {
      transports: ["websocket", "polling"] as const,
      upgrade: true,
      rememberUpgrade: false,
      timeout: 20000,
      forceNew: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 5000,
    });

    socketInstance.on(
      "initialData",
      (data: { instruments: any[]; liveData: MarketData[] }) => {
        console.log(`📊 Received ${data.liveData.length} initial market data items`);
        setMarketData((prev) => {
          const newMarketData = new Map(prev);
          data.liveData.forEach((item: MarketData) => {
            newMarketData.set(item.securityId, item);
          });
          return newMarketData;
        });
        setLoading(false);
      }
    );

    socketInstance.on("marketData", (data: MarketData) => {
      updateMarketDataSingle(data);
    });

    socketInstance.on("marketDataBatch", (batch: MarketData[]) => {
      updateMarketDataBatch(batch);
    });

    socketInstance.on("connect", () => {
      console.log("✅ Trading page connected to WebSocket server");
      setIsConnected(true);
    });

    socketInstance.on("disconnect", () => {
      console.log("❌ Trading page disconnected from WebSocket server");
      setIsConnected(false);
    });

    socketInstance.on("connect_error", (error) => {
      console.error("❌ Trading page connection error:", error);
      setIsConnected(false);
      setError("Failed to connect to market data server");
    });

    setSocket(socketInstance);

    return () => {
      socketInstance.disconnect();
    };
  }, []); // Empty dependency array since useCallback functions have stable references

  // Authentication check
  useEffect(() => {
    if (authLoading) return;

    if (!user) {
      router.push('/login');
      return;
    }
  }, [user, authLoading, router]);

  const handleRefresh = async () => {
    if (socket) {
      console.log('🔄 Refreshing market data connection...');
      socket.disconnect();
      socket.connect();
    }
  };

  const handleOrderPlacement = (script: MarketData, orderType: 'BUY' | 'SELL') => {
    setSelectedScript(script);
    setSelectedOrderType(orderType);
    setShowOrderForm(true);
  };

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading market data...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect to login
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8 flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Trading Dashboard</h1>
            <p className="text-gray-600 mt-2">
              Welcome back, {user.name || user.email}
            </p>
          </div>
          <div className="flex gap-3">
            <button
              onClick={() => setShowBrokerLogin(true)}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
            >
              Broker Login
            </button>
            <button
              onClick={() => setShowOrderManagement(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Order Management
            </button>
            {/* User Management button - only for admins and super admins */}
            {(user.role === 'admin' || user.role === 'super_admin') && (
              <button
                onClick={() => setActiveTab('user-management')}
                className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors"
              >
                User Management
              </button>
            )}
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="bg-white rounded-lg shadow mb-6">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              <button
                onClick={() => setActiveTab('market')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'market'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                📊 Market Dashboard
              </button>
              {(user.role === 'admin' || user.role === 'super_admin') && (
                <button
                  onClick={() => setActiveTab('user-management')}
                  className={`py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'user-management'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  👥 User Management
                </button>
              )}
            </nav>
          </div>
        </div>

        {/* Error Alert */}
        {error && activeTab === 'market' && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{error}</p>
                </div>
              </div>
            </div>
          </div>
        )}



        {/* Market Data Tab Content */}
        {activeTab === 'market' && (
          <>
            {/* Search, Filter, Sort and Pagination Controls */}
            <div className="bg-white rounded-lg shadow p-4 mb-6">
              {/* First Row: Search and Sort */}
              <div className="flex flex-col lg:flex-row gap-4 mb-4">
                <div className="flex-1">
                  <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-2">
                    Search Instruments
                  </label>
                  <input
                    type="text"
                    id="search"
                    placeholder="Search by ticker or security ID..."
                    value={searchTerm}
                    onChange={(e) => {
                      setSearchTerm(e.target.value);
                      setCurrentPage(1); // Reset to first page on search
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
            <div className="md:w-40">
              <label htmlFor="sortBy" className="block text-sm font-medium text-gray-700 mb-2">
                Sort By
              </label>
              <select
                id="sortBy"
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="ticker">Ticker</option>
                <option value="ltp">Price</option>
                <option value="change">Change</option>
                <option value="volume">Volume</option>
              </select>
            </div>
            <div className="md:w-32">
              <label htmlFor="sortOrder" className="block text-sm font-medium text-gray-700 mb-2">
                Order
              </label>
              <select
                id="sortOrder"
                value={sortOrder}
                onChange={(e) => setSortOrder(e.target.value as any)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="asc">Ascending</option>
                <option value="desc">Descending</option>
              </select>
            </div>
          </div>

          {/* Second Row: Items per page, Clear button, Status */}
          <div className="flex flex-col md:flex-row gap-4 items-end">
            <div className="md:w-32">
              <label htmlFor="itemsPerPage" className="block text-sm font-medium text-gray-700 mb-2">
                Show
              </label>
              <select
                id="itemsPerPage"
                value={itemsPerPage}
                onChange={(e) => {
                  setItemsPerPage(Number(e.target.value));
                  setCurrentPage(1); // Reset to first page
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value={10}>10</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
                <option value={100}>100</option>
                <option value={200}>200</option>
              </select>
              <span className="text-xs text-gray-500 mt-1">per page</span>
            </div>

            <div className="flex items-center gap-2 text-sm text-gray-600">
              <span>
                Showing {processedData.length > 0 ? startItem : 0} to {endItem} of {processedData.length} instruments
              </span>
              {processedData.length !== marketData.size && (
                <span className="text-gray-500">
                  (filtered from {marketData.size} total)
                </span>
              )}
            </div>

            <div className="flex items-center gap-2">
              {!isConnected && (
                <span className="text-red-500 text-sm">• Disconnected</span>
              )}
              {isConnected && (
                <span className="text-green-500 text-sm">• Connected</span>
              )}

              <button
                type="button"
                onClick={() => {
                  setSearchTerm("");
                  setSortBy("ticker");
                  setSortOrder("asc");
                  setCurrentPage(1);
                }}
                className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500"
              >
                Clear All
              </button>
            </div>
          </div>
        </div>

        {/* Market Data Table */}
        <MarketDataTable
          data={paginatedData}
          onRefresh={handleRefresh}
          onOrderPlacement={handleOrderPlacement}
        />

        {/* Pagination Controls */}
        {totalPages > 1 && (
          <div className="bg-white rounded-lg shadow p-4 mt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Previous
                </button>

                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(7, totalPages) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 7) {
                      pageNum = i + 1;
                    } else if (currentPage <= 4) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 3) {
                      pageNum = totalPages - 6 + i;
                    } else {
                      pageNum = currentPage - 3 + i;
                    }

                    return (
                      <button
                        key={pageNum}
                        onClick={() => setCurrentPage(pageNum)}
                        className={`px-3 py-2 text-sm rounded-md ${
                          currentPage === pageNum
                            ? 'bg-blue-500 text-white'
                            : 'border border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}

                  {totalPages > 7 && currentPage < totalPages - 3 && (
                    <>
                      <span className="px-2 text-gray-500">...</span>
                      <button
                        onClick={() => setCurrentPage(totalPages)}
                        className="px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50"
                      >
                        {totalPages}
                      </button>
                    </>
                  )}
                </div>

                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Next
                </button>
              </div>

              <div className="text-sm text-gray-600">
                Page {currentPage} of {totalPages}
              </div>
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-md p-6">
          <h3 className="text-lg font-medium text-blue-900 mb-4">Trading Instructions</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
            <div>
              <h4 className="font-medium mb-2">Keyboard Shortcuts:</h4>
              <ul className="space-y-1">
                <li><kbd className="px-2 py-1 bg-white rounded border">↑</kbd> / <kbd className="px-2 py-1 bg-white rounded border">↓</kbd> Navigate rows</li>
                <li><kbd className="px-2 py-1 bg-white rounded border">+</kbd> Place Buy order</li>
                <li><kbd className="px-2 py-1 bg-white rounded border">-</kbd> Place Sell order</li>
                <li><kbd className="px-2 py-1 bg-white rounded border">Esc</kbd> Close modal</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Client Code Options:</h4>
              <ul className="space-y-1">
                <li><strong>Individual accounts:</strong> Select specific client codes</li>
                <li><strong>allang:</strong> Place orders for all Angel One accounts</li>
                <li><strong>allMo:</strong> Place orders for all Motilal Oswal accounts</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-sm font-medium text-gray-500">Total Scripts</div>
            <div className="text-2xl font-bold text-gray-900">{marketData.size}</div>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-sm font-medium text-gray-500">Gainers</div>
            <div className="text-2xl font-bold text-green-600">
              {Array.from(marketData.values()).filter(item => item.change > 0).length}
            </div>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-sm font-medium text-gray-500">Losers</div>
            <div className="text-2xl font-bold text-red-600">
              {Array.from(marketData.values()).filter(item => item.change < 0).length}
            </div>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-sm font-medium text-gray-500">Unchanged</div>
            <div className="text-2xl font-bold text-gray-600">
              {Array.from(marketData.values()).filter(item => item.change === 0).length}
            </div>
          </div>
        </div>
        </>
        )}

        {/* User Management Tab Content */}
        {activeTab === 'user-management' && (
          <div className="bg-white rounded-lg shadow">
            <UserManagement />
          </div>
        )}

        {/* Order Form Modal */}
        {showOrderForm && selectedScript && (
          <TradingOrderForm
            isOpen={showOrderForm}
            scriptData={selectedScript}
            orderType={selectedOrderType}
            onClose={() => {
              setShowOrderForm(false);
              setSelectedScript(null);
            }}
          />
        )}

        {/* Order Management Modal */}
        {showOrderManagement && (
          <OrderManagement
            isOpen={showOrderManagement}
            onClose={() => setShowOrderManagement(false)}
          />
        )}

        {/* Broker Login Manager Modal */}
        {showBrokerLogin && (
          <BrokerLoginManager
            isOpen={showBrokerLogin}
            onClose={() => setShowBrokerLogin(false)}
          />
        )}


      </div>
    </div>
  );
}
