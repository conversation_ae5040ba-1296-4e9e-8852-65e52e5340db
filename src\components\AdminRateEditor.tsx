"use client";

import React, { useState, useEffect } from 'react';
import { useToast } from './Toast';
import ErrorBoundary from './ErrorBoundary';

interface Admin {
  _id: string;
  name: string;
  email: string;
  companyName: string;
  orderRate: number;
  isActive: boolean;
  createdAt: string;
  statistics?: {
    userCount: number;
    currentMonth?: {
      totalOrders: number;
      totalRevenue: number;
      billingStatus: string;
      lastBillingDate?: string;
    };
    totalOrders?: number;
    totalRevenue?: number;
    pendingAmount?: number;
    paidAmount?: number;
  };
}

interface AdminRateEditorProps {
  isOpen: boolean;
  onClose: () => void;
  onRateUpdated: () => void;
}

const AdminRateEditor: React.FC<AdminRateEditorProps> = ({ isOpen, onClose, onRateUpdated }) => {
  const { showSuccess, showError, showInfo } = useToast();
  const [admins, setAdmins] = useState<Admin[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState<string | null>(null);
  const [editingRates, setEditingRates] = useState<{[key: string]: number}>({});

  const fetchAdmins = async () => {
    try {
      setLoading(true);
      setAdmins([]); // Clear existing data
      setEditingRates({}); // Clear existing rates

      const response = await fetch('/api/super-admin/admins', {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API Error Response:', errorText);
        throw new Error(`Failed to fetch admins: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      // Handle different response structures
      let adminsList: Admin[] = [];
      if (data.success && data.data) {
        adminsList = data.data.admins || data.data || [];
      } else if (Array.isArray(data)) {
        adminsList = data;
      } else if (data.admins) {
        adminsList = data.admins;
      }

      // Validate that adminsList is an array
      if (!Array.isArray(adminsList)) {
        console.warn('Invalid admin data structure:', data);
        adminsList = [];
      }

      setAdmins(adminsList);

      // Initialize editing rates with current rates
      const initialRates: {[key: string]: number} = {};
      adminsList.forEach((admin: Admin) => {
        if (admin && admin._id) {
          initialRates[admin._id] = admin.orderRate || 5;
        }
      });
      setEditingRates(initialRates);

      if (adminsList.length === 0) {
        showInfo('Info', 'No admin data found');
      }
    } catch (error) {
      console.error('Error fetching admins:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      showError('Error', `Failed to fetch admin data: ${errorMessage}`);
      setAdmins([]);
      setEditingRates({});
    } finally {
      setLoading(false);
    }
  };

  const updateAdminRate = async (adminId: string, newRate: number) => {
    if (!adminId || newRate < 0) {
      showError('Error', 'Invalid admin ID or rate value');
      return;
    }

    try {
      setSaving(adminId);

      const response = await fetch(`/api/super-admin/admins/${adminId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ orderRate: newRate })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Update API Error Response:', errorText);
        throw new Error(`Failed to update admin rate: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Update failed');
      }

      // Update local state only if API call was successful
      setAdmins(prev => prev.map(admin =>
        admin._id === adminId ? { ...admin, orderRate: newRate } : admin
      ));

      const adminName = data.data?.name || 'Admin';
      showSuccess('Success', `Order rate updated to ₹${newRate} for ${adminName}`);
      onRateUpdated();
    } catch (error) {
      console.error('Error updating admin rate:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      showError('Error', `Failed to update order rate: ${errorMessage}`);

      // Reset the editing rate to original value on error
      setEditingRates(prev => {
        const admin = admins.find(a => a._id === adminId);
        if (admin) {
          return { ...prev, [adminId]: admin.orderRate || 5 };
        }
        return prev;
      });
    } finally {
      setSaving(null);
    }
  };

  const bulkUpdateRates = async () => {
    try {
      setLoading(true);

      // Validate updates before sending
      const updates = Object.entries(editingRates)
        .filter(([adminId, rate]) => {
          const admin = admins.find(a => a._id === adminId);
          return admin && rate >= 0 && rate !== admin.orderRate;
        })
        .map(([adminId, rate]) => ({
          adminId,
          orderRate: rate
        }));

      if (updates.length === 0) {
        showInfo('Info', 'No changes to save');
        return;
      }

      const response = await fetch('/api/super-admin/admins/bulk-update', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ updates })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Bulk Update API Error Response:', errorText);
        throw new Error(`Failed to bulk update rates: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Bulk update failed');
      }

      const { successful, failed } = data.data?.summary || { successful: 0, failed: 0 };

      if (failed > 0) {
        showError('Warning', `${successful} updates successful, ${failed} failed`);
      } else {
        showSuccess('Success', `All ${successful} admin rates updated successfully`);
      }

      // Refresh data to ensure consistency
      fetchAdmins();
      onRateUpdated();
    } catch (error) {
      console.error('Error bulk updating rates:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      showError('Error', `Failed to update rates: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const resetRates = () => {
    const initialRates: {[key: string]: number} = {};
    if (Array.isArray(admins)) {
      admins.forEach((admin) => {
        initialRates[admin._id] = admin.orderRate || 5;
      });
    }
    setEditingRates(initialRates);
    showInfo('Info', 'Rates reset to original values');
  };

  const setUniformRate = (rate: number) => {
    const uniformRates: {[key: string]: number} = {};
    if (Array.isArray(admins)) {
      admins.forEach((admin) => {
        uniformRates[admin._id] = rate;
      });
    }
    setEditingRates(uniformRates);
    showInfo('Info', `All rates set to ₹${rate}`);
  };

  useEffect(() => {
    if (isOpen) {
      fetchAdmins();
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold text-gray-900">Admin Order Rate Management</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* Bulk Actions */}
          <div className="mb-6 bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Bulk Actions</h3>
            <div className="flex flex-wrap gap-3">
              <button
                onClick={() => setUniformRate(5)}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
              >
                Set All to ₹5
              </button>
              <button
                onClick={() => setUniformRate(10)}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
              >
                Set All to ₹10
              </button>
              <button
                onClick={() => setUniformRate(15)}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
              >
                Set All to ₹15
              </button>
              <button
                onClick={resetRates}
                className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700"
              >
                Reset to Original
              </button>
              <button
                onClick={bulkUpdateRates}
                disabled={loading}
                className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 disabled:opacity-50"
              >
                {loading ? 'Updating...' : 'Apply All Changes'}
              </button>
            </div>
          </div>

          {loading && admins.length === 0 ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Admin</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Current Rate</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">New Rate</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statistics</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {Array.isArray(admins) ? admins.map((admin) => (
                    <tr key={admin._id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{admin.name}</div>
                          <div className="text-sm text-gray-500">{admin.email}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {admin.companyName || 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-lg font-semibold text-blue-600">₹{admin.orderRate || 5}</span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="number"
                          min="0"
                          step="0.5"
                          value={editingRates[admin._id] || admin.orderRate || 5}
                          onChange={(e) => setEditingRates(prev => ({
                            ...prev,
                            [admin._id]: parseFloat(e.target.value) || 0
                          }))}
                          className="w-20 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {admin.statistics && (
                          <div>
                            <div>Users: {admin.statistics.userCount || 0}</div>
                            <div>Orders: {admin.statistics.currentMonth?.totalOrders || 0}</div>
                            <div>Revenue: ₹{(admin.statistics.currentMonth?.totalRevenue || 0).toLocaleString()}</div>
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button
                          onClick={() => updateAdminRate(admin._id, editingRates[admin._id] || admin.orderRate || 5)}
                          disabled={saving === admin._id || editingRates[admin._id] === admin.orderRate}
                          className="text-blue-600 hover:text-blue-900 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {saving === admin._id ? 'Saving...' : 'Update'}
                        </button>
                      </td>
                    </tr>
                  )) : (
                    <tr>
                      <td colSpan={6} className="px-6 py-4 text-center text-gray-500">
                        No admin data available
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          )}
        </div>

        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-600">
              Total Admins: {Array.isArray(admins) ? admins.length : 0} |
              Average Rate: ₹{Array.isArray(admins) && admins.length > 0 ? (Object.values(editingRates).reduce((a, b) => a + b, 0) / admins.length).toFixed(2) : 0}
            </div>
            <div className="flex space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Close
              </button>
              <button
                onClick={bulkUpdateRates}
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? 'Saving...' : 'Save All Changes'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Wrap with error boundary for better error handling
const AdminRateEditorWithErrorBoundary: React.FC<AdminRateEditorProps> = (props) => (
  <ErrorBoundary
    fallback={
      <div className="min-h-[400px] flex items-center justify-center bg-red-50 border border-red-200 rounded-lg">
        <div className="text-center">
          <div className="text-red-600 text-4xl mb-4">⚠️</div>
          <h3 className="text-lg font-semibold text-red-800 mb-2">
            Admin Rate Editor Error
          </h3>
          <p className="text-red-600 mb-4">
            Failed to load the admin rate editor. Please try refreshing the page.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
          >
            Reload
          </button>
        </div>
      </div>
    }
  >
    <AdminRateEditor {...props} />
  </ErrorBoundary>
);

export default AdminRateEditorWithErrorBoundary;
