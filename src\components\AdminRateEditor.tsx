"use client";

import React, { useState, useEffect } from 'react';
import { useToast } from './Toast';

interface Admin {
  _id: string;
  name: string;
  email: string;
  companyName: string;
  orderRate: number;
  isActive: boolean;
  createdAt: string;
  statistics?: {
    userCount: number;
    totalOrders: number;
    totalRevenue: number;
    pendingAmount: number;
    paidAmount: number;
  };
}

interface AdminRateEditorProps {
  isOpen: boolean;
  onClose: () => void;
  onRateUpdated: () => void;
}

const AdminRateEditor: React.FC<AdminRateEditorProps> = ({ isOpen, onClose, onRateUpdated }) => {
  const { showSuccess, showError, showInfo } = useToast();
  const [admins, setAdmins] = useState<Admin[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState<string | null>(null);
  const [editingRates, setEditingRates] = useState<{[key: string]: number}>({});

  const fetchAdmins = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/super-admin/admins', {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to fetch admins');
      }

      const data = await response.json();
      setAdmins(data.data || []);
      
      // Initialize editing rates with current rates
      const initialRates: {[key: string]: number} = {};
      data.data.forEach((admin: Admin) => {
        initialRates[admin._id] = admin.orderRate || 5;
      });
      setEditingRates(initialRates);
    } catch (error) {
      console.error('Error fetching admins:', error);
      showError('Error', 'Failed to fetch admin data');
    } finally {
      setLoading(false);
    }
  };

  const updateAdminRate = async (adminId: string, newRate: number) => {
    try {
      setSaving(adminId);
      const response = await fetch(`/api/super-admin/admins/${adminId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({ orderRate: newRate })
      });

      if (!response.ok) {
        throw new Error('Failed to update admin rate');
      }

      const data = await response.json();
      
      // Update local state
      setAdmins(prev => prev.map(admin => 
        admin._id === adminId ? { ...admin, orderRate: newRate } : admin
      ));

      showSuccess('Success', `Order rate updated to ₹${newRate} for ${data.data.name}`);
      onRateUpdated();
    } catch (error) {
      console.error('Error updating admin rate:', error);
      showError('Error', 'Failed to update order rate');
    } finally {
      setSaving(null);
    }
  };

  const bulkUpdateRates = async () => {
    try {
      setLoading(true);
      const updates = Object.entries(editingRates).map(([adminId, rate]) => ({
        adminId,
        orderRate: rate
      }));

      const response = await fetch('/api/super-admin/admins/bulk-update', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({ updates })
      });

      if (!response.ok) {
        throw new Error('Failed to bulk update rates');
      }

      showSuccess('Success', 'All admin rates updated successfully');
      fetchAdmins();
      onRateUpdated();
    } catch (error) {
      console.error('Error bulk updating rates:', error);
      showError('Error', 'Failed to update rates');
    } finally {
      setLoading(false);
    }
  };

  const resetRates = () => {
    const initialRates: {[key: string]: number} = {};
    admins.forEach((admin) => {
      initialRates[admin._id] = admin.orderRate || 5;
    });
    setEditingRates(initialRates);
    showInfo('Info', 'Rates reset to original values');
  };

  const setUniformRate = (rate: number) => {
    const uniformRates: {[key: string]: number} = {};
    admins.forEach((admin) => {
      uniformRates[admin._id] = rate;
    });
    setEditingRates(uniformRates);
    showInfo('Info', `All rates set to ₹${rate}`);
  };

  useEffect(() => {
    if (isOpen) {
      fetchAdmins();
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold text-gray-900">Admin Order Rate Management</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* Bulk Actions */}
          <div className="mb-6 bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Bulk Actions</h3>
            <div className="flex flex-wrap gap-3">
              <button
                onClick={() => setUniformRate(5)}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
              >
                Set All to ₹5
              </button>
              <button
                onClick={() => setUniformRate(10)}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
              >
                Set All to ₹10
              </button>
              <button
                onClick={() => setUniformRate(15)}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
              >
                Set All to ₹15
              </button>
              <button
                onClick={resetRates}
                className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700"
              >
                Reset to Original
              </button>
              <button
                onClick={bulkUpdateRates}
                disabled={loading}
                className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 disabled:opacity-50"
              >
                {loading ? 'Updating...' : 'Apply All Changes'}
              </button>
            </div>
          </div>

          {loading && admins.length === 0 ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Admin</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Current Rate</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">New Rate</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statistics</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {admins.map((admin) => (
                    <tr key={admin._id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{admin.name}</div>
                          <div className="text-sm text-gray-500">{admin.email}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {admin.companyName || 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-lg font-semibold text-blue-600">₹{admin.orderRate || 5}</span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="number"
                          min="0"
                          step="0.5"
                          value={editingRates[admin._id] || admin.orderRate || 5}
                          onChange={(e) => setEditingRates(prev => ({
                            ...prev,
                            [admin._id]: parseFloat(e.target.value) || 0
                          }))}
                          className="w-20 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {admin.statistics && (
                          <div>
                            <div>Orders: {admin.statistics.totalOrders}</div>
                            <div>Revenue: ₹{admin.statistics.totalRevenue.toLocaleString()}</div>
                            <div>Pending: ₹{admin.statistics.pendingAmount.toLocaleString()}</div>
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button
                          onClick={() => updateAdminRate(admin._id, editingRates[admin._id] || admin.orderRate || 5)}
                          disabled={saving === admin._id || editingRates[admin._id] === admin.orderRate}
                          className="text-blue-600 hover:text-blue-900 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {saving === admin._id ? 'Saving...' : 'Update'}
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-600">
              Total Admins: {admins.length} | 
              Average Rate: ₹{admins.length > 0 ? (Object.values(editingRates).reduce((a, b) => a + b, 0) / admins.length).toFixed(2) : 0}
            </div>
            <div className="flex space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Close
              </button>
              <button
                onClick={bulkUpdateRates}
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? 'Saving...' : 'Save All Changes'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminRateEditor;
