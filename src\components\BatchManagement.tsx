'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface ClientAccount {
  id: string;
  clientCode: string;
  clientName: string;
  broker: 'angel' | 'motilal';
  isActive: boolean;
}

interface UserBatch {
  _id: string;
  name: string;
  description: string;
  accounts: ClientAccount[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function BatchManagement() {
  const { user } = useAuth();
  const [batches, setBatches] = useState<UserBatch[]>([]);
  const [availableAccounts, setAvailableAccounts] = useState<ClientAccount[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingBatch, setEditingBatch] = useState<UserBatch | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    selectedAccounts: [] as string[]
  });

  // Fetch batches and available accounts
  useEffect(() => {
    if (user) {
      fetchBatches();
      fetchAvailableAccounts();
    }
  }, [user]);

  const fetchBatches = async () => {
    try {
      setLoading(true);
      setError('');
      
      console.log('🔄 Fetching user batches...');
      const response = await fetch('/api/batches', {
        credentials: 'include'
      });

      console.log(`📡 Batches API response status: ${response.status}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`❌ Batches API error: ${response.status} - ${errorText}`);
        throw new Error(`Failed to fetch batches: ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ Batches data received:', data);

      if (data.success) {
        setBatches(data.batches || []);
      } else {
        console.error('❌ Batches API returned error:', data.error);
        setError(data.error || 'Failed to load batches');
      }
    } catch (error) {
      console.error('💥 Error fetching batches:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch batches');
    } finally {
      setLoading(false);
    }
  };

  const fetchAvailableAccounts = async () => {
    try {
      console.log('🔄 Fetching available client accounts...');
      const response = await fetch('/api/client-accounts', {
        credentials: 'include'
      });

      console.log(`📡 Client accounts API response status: ${response.status}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`❌ Client accounts API error: ${response.status} - ${errorText}`);
        throw new Error(`Failed to fetch client accounts: ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ Client accounts data received:', data);

      if (data.success && data.accounts) {
        setAvailableAccounts(data.accounts);
        console.log(`✅ Loaded ${data.accounts.length} client accounts for batch management`);
      } else {
        console.error('❌ Client accounts API returned error:', data.error);
        setError(data.error || 'Failed to load client accounts');
      }
    } catch (error) {
      console.error('💥 Error fetching client accounts:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch client accounts');
    }
  };

  const handleCreateBatch = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      setError('Batch name is required');
      return;
    }

    if (formData.selectedAccounts.length === 0) {
      setError('Please select at least one account');
      return;
    }

    try {
      setLoading(true);
      setError('');

      const selectedAccountObjects = availableAccounts.filter(acc => 
        formData.selectedAccounts.includes(acc.id)
      );

      const batchData = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        accounts: selectedAccountObjects
      };

      console.log('🔄 Creating batch:', batchData);

      const response = await fetch('/api/batches', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify(batchData)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to create batch: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      
      if (data.success) {
        console.log('✅ Batch created successfully');
        setFormData({ name: '', description: '', selectedAccounts: [] });
        setShowCreateForm(false);
        fetchBatches(); // Refresh the list
      } else {
        setError(data.error || 'Failed to create batch');
      }
    } catch (error) {
      console.error('💥 Error creating batch:', error);
      setError(error instanceof Error ? error.message : 'Failed to create batch');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateBatch = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!editingBatch) return;

    try {
      setLoading(true);
      setError('');

      const selectedAccountObjects = availableAccounts.filter(acc => 
        formData.selectedAccounts.includes(acc.id)
      );

      const batchData = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        accounts: selectedAccountObjects
      };

      const response = await fetch(`/api/batches/${editingBatch._id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify(batchData)
      });

      if (!response.ok) {
        throw new Error('Failed to update batch');
      }

      const data = await response.json();
      
      if (data.success) {
        setEditingBatch(null);
        setFormData({ name: '', description: '', selectedAccounts: [] });
        fetchBatches(); // Refresh the list
      } else {
        setError(data.error || 'Failed to update batch');
      }
    } catch (error) {
      console.error('Error updating batch:', error);
      setError(error instanceof Error ? error.message : 'Failed to update batch');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteBatch = async (batchId: string) => {
    if (!confirm('Are you sure you want to delete this batch?')) return;

    try {
      setLoading(true);
      const response = await fetch(`/api/batches/${batchId}`, {
        method: 'DELETE',
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to delete batch');
      }

      fetchBatches(); // Refresh the list
    } catch (error) {
      console.error('Error deleting batch:', error);
      setError(error instanceof Error ? error.message : 'Failed to delete batch');
    } finally {
      setLoading(false);
    }
  };

  const startEdit = (batch: UserBatch) => {
    setEditingBatch(batch);
    setFormData({
      name: batch.name,
      description: batch.description,
      selectedAccounts: batch.accounts.map(acc => acc.id)
    });
    setShowCreateForm(true);
  };

  const cancelEdit = () => {
    setEditingBatch(null);
    setFormData({ name: '', description: '', selectedAccounts: [] });
    setShowCreateForm(false);
  };

  const toggleAccountSelection = (accountId: string) => {
    setFormData(prev => ({
      ...prev,
      selectedAccounts: prev.selectedAccounts.includes(accountId)
        ? prev.selectedAccounts.filter(id => id !== accountId)
        : [...prev.selectedAccounts, accountId]
    }));
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Batch Management</h2>
          <p className="text-sm text-gray-600 mt-1">
            Create and manage batches of client accounts for bulk order execution
          </p>
        </div>
        <button
          onClick={() => setShowCreateForm(true)}
          className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
        >
          + Create New Batch
        </button>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-red-700">{error}</div>
        </div>
      )}

      {/* Create/Edit Batch Form */}
      {showCreateForm && (
        <div className="mb-6 bg-gray-50 p-4 rounded-lg border">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            {editingBatch ? 'Edit Batch' : 'Create New Batch'}
          </h3>
          
          <form onSubmit={editingBatch ? handleUpdateBatch : handleCreateBatch}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Batch Name *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  placeholder="Enter batch name"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <input
                  type="text"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  placeholder="Enter batch description"
                />
              </div>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Client Accounts *
              </label>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 max-h-40 overflow-y-auto border border-gray-200 rounded-md p-3">
                {availableAccounts.map((account) => (
                  <label key={account.id} className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={formData.selectedAccounts.includes(account.id)}
                      onChange={() => toggleAccountSelection(account.id)}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm">
                      {account.clientName} ({account.clientCode}) - {account.broker.toUpperCase()}
                    </span>
                  </label>
                ))}
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Selected: {formData.selectedAccounts.length} accounts
              </p>
            </div>

            <div className="flex space-x-3">
              <button
                type="submit"
                disabled={loading}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? 'Saving...' : (editingBatch ? 'Update Batch' : 'Create Batch')}
              </button>
              <button
                type="button"
                onClick={cancelEdit}
                className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Batches List */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Your Batches</h3>
          
          {loading && !showCreateForm ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
            </div>
          ) : batches.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">No batches created yet</p>
              <button
                onClick={() => setShowCreateForm(true)}
                className="mt-2 text-blue-600 hover:text-blue-800"
              >
                Create your first batch
              </button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {batches.map((batch) => (
                <div key={batch._id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="font-medium text-gray-900">{batch.name}</h4>
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      batch.isActive 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {batch.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                  
                  {batch.description && (
                    <p className="text-sm text-gray-600 mb-3">{batch.description}</p>
                  )}
                  
                  <div className="mb-3">
                    <p className="text-sm font-medium text-gray-700">
                      Accounts ({batch.accounts.length}):
                    </p>
                    <div className="text-xs text-gray-600 mt-1">
                      {batch.accounts.slice(0, 3).map(acc => acc.clientCode).join(', ')}
                      {batch.accounts.length > 3 && ` +${batch.accounts.length - 3} more`}
                    </div>
                  </div>
                  
                  <div className="flex space-x-2">
                    <button
                      onClick={() => startEdit(batch)}
                      className="text-blue-600 hover:text-blue-800 text-sm"
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => handleDeleteBatch(batch._id)}
                      className="text-red-600 hover:text-red-800 text-sm"
                    >
                      Delete
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
