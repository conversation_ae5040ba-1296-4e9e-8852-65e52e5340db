'use client';

import { useState, useEffect } from 'react';

interface BatchOrderResult {
  account: string;
  broker: 'angel' | 'motilal';
  success: boolean;
  orderId?: string;
  message?: string;
  error?: string;
}

interface BatchOrderHistoryRecord {
  id: string;
  batchId: string;
  batchName: string;
  symbol: string;
  orderType: 'BUY' | 'SELL';
  quantity: number;
  price: number;
  productType: string;
  validity: string;
  variety: string;
  exchange: string;
  totalAccounts: number;
  successfulOrders: number;
  failedOrders: number;
  results: BatchOrderResult[];
  executedAt: Date;
  createdAt: Date;
}

interface BatchOrderHistoryProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function BatchOrderHistory({ isOpen, onClose }: BatchOrderHistoryProps) {
  const [history, setHistory] = useState<BatchOrderHistoryRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<BatchOrderHistoryRecord | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    if (isOpen) {
      fetchHistory();
    }
  }, [isOpen, currentPage]);

  const fetchHistory = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/batch-order-history?page=${currentPage}&limit=10`, {
        credentials: 'include'
      });
      const data = await response.json();
      
      if (data.success) {
        setHistory(data.history);
        setTotalPages(data.pagination.pages);
      }
    } catch (error) {
      console.error('Error fetching batch order history:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleString();
  };

  const getSuccessRate = (record: BatchOrderHistoryRecord) => {
    return ((record.successfulOrders / record.totalAccounts) * 100).toFixed(1);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-3">
            <span className="text-2xl">📊</span>
            <h2 className="text-xl font-semibold text-gray-900">Batch Order History</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors text-2xl"
          >
            ✕
          </button>
        </div>

        <div className="flex h-[calc(90vh-120px)]">
          {/* Left Panel - History List */}
          <div className="w-2/3 border-r">
            <div className="p-4 border-b bg-gray-50">
              <h3 className="font-medium text-gray-900">Order History</h3>
            </div>

            <div className="overflow-y-auto h-full">
              {loading ? (
                <div className="text-center py-8 text-gray-500">Loading history...</div>
              ) : history.length === 0 ? (
                <div className="text-center py-8 text-gray-500">No batch orders found</div>
              ) : (
                <div className="divide-y divide-gray-200">
                  {history.map((record) => (
                    <div
                      key={record.id}
                      className={`p-4 cursor-pointer hover:bg-gray-50 transition-colors ${
                        selectedRecord?.id === record.id ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                      }`}
                      onClick={() => setSelectedRecord(record)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium text-gray-900">
                            {record.batchName} • {record.symbol}
                          </div>
                          <div className="text-sm text-gray-500">
                            {record.orderType} {record.quantity} @ ₹{record.price}
                          </div>
                          <div className="text-xs text-gray-400">
                            {formatDate(record.executedAt)}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className={`text-sm font-medium ${
                            record.successfulOrders === record.totalAccounts 
                              ? 'text-green-600' 
                              : record.successfulOrders > 0 
                                ? 'text-yellow-600' 
                                : 'text-red-600'
                          }`}>
                            {record.successfulOrders}/{record.totalAccounts} Success
                          </div>
                          <div className="text-xs text-gray-500">
                            {getSuccessRate(record)}% Success Rate
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="p-4 border-t bg-gray-50">
                  <div className="flex items-center justify-between">
                    <button
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      disabled={currentPage === 1}
                      className="px-3 py-1 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed"
                    >
                      Previous
                    </button>
                    <span className="text-sm text-gray-600">
                      Page {currentPage} of {totalPages}
                    </span>
                    <button
                      onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                      disabled={currentPage === totalPages}
                      className="px-3 py-1 border border-gray-300 rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed"
                    >
                      Next
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Right Panel - Order Details */}
          <div className="w-1/3">
            <div className="p-4 border-b bg-gray-50">
              <h3 className="font-medium text-gray-900">Order Details</h3>
            </div>

            <div className="p-4 overflow-y-auto h-full">
              {selectedRecord ? (
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Order Information</h4>
                    <div className="space-y-2 text-sm">
                      <div><span className="font-medium">Batch:</span> {selectedRecord.batchName}</div>
                      <div><span className="font-medium">Symbol:</span> {selectedRecord.symbol}</div>
                      <div><span className="font-medium">Type:</span> {selectedRecord.orderType}</div>
                      <div><span className="font-medium">Quantity:</span> {selectedRecord.quantity}</div>
                      <div><span className="font-medium">Price:</span> ₹{selectedRecord.price}</div>
                      <div><span className="font-medium">Product:</span> {selectedRecord.productType}</div>
                      <div><span className="font-medium">Validity:</span> {selectedRecord.validity}</div>
                      <div><span className="font-medium">Exchange:</span> {selectedRecord.exchange}</div>
                      <div><span className="font-medium">Executed:</span> {formatDate(selectedRecord.executedAt)}</div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Execution Results</h4>
                    <div className="space-y-2">
                      {selectedRecord.results.map((result, index) => (
                        <div
                          key={index}
                          className={`p-2 rounded border ${
                            result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                          }`}
                        >
                          <div className="flex items-center justify-between">
                            <span className="font-medium">{result.account}</span>
                            <span className={`text-xs px-2 py-1 rounded ${
                              result.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                            }`}>
                              {result.broker.toUpperCase()}
                            </span>
                          </div>
                          {result.success ? (
                            <div className="text-sm text-green-600">
                              ✓ Order ID: {result.orderId || 'N/A'}
                            </div>
                          ) : (
                            <div className="text-sm text-red-600">
                              ✗ {result.message || result.error || 'Order failed'}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Summary</h4>
                    <div className="bg-gray-50 p-3 rounded">
                      <div className="text-sm space-y-1">
                        <div>Total Accounts: {selectedRecord.totalAccounts}</div>
                        <div className="text-green-600">Successful: {selectedRecord.successfulOrders}</div>
                        <div className="text-red-600">Failed: {selectedRecord.failedOrders}</div>
                        <div className="font-medium">Success Rate: {getSuccessRate(selectedRecord)}%</div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  Select an order to view details
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
