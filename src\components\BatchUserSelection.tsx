'use client';

import { useState, useEffect } from 'react';

interface ClientAccount {
  id: string;
  clientCode: string;
  broker: 'angel' | 'motilal';
  isActive: boolean;
  clientName?: string;
  capital?: number;
}

interface UserBatch {
  id: string;
  name: string;
  description: string;
  accounts: ClientAccount[];
  createdAt: Date;
  isActive: boolean;
}

interface BatchUserSelectionProps {
  isOpen: boolean;
  onClose: () => void;
  onBatchCreated: (batch: UserBatch) => void;
  existingBatches?: UserBatch[];
}

export default function BatchUserSelection({ 
  isOpen, 
  onClose, 
  onBatchCreated,
  existingBatches = []
}: BatchUserSelectionProps) {
  const [availableAccounts, setAvailableAccounts] = useState<ClientAccount[]>([]);
  const [selectedAccounts, setSelectedAccounts] = useState<ClientAccount[]>([]);
  const [batches, setBatches] = useState<UserBatch[]>(existingBatches);
  const [isCreatingBatch, setIsCreatingBatch] = useState(false);
  const [editingBatch, setEditingBatch] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);

  // New batch form data
  const [newBatch, setNewBatch] = useState({
    name: '',
    description: ''
  });

  // Fetch available accounts
  useEffect(() => {
    if (isOpen) {
      fetchAvailableAccounts();
    }
  }, [isOpen]);

  const fetchAvailableAccounts = async () => {
    setLoading(true);
    try {
      const [angelResponse, motilalResponse] = await Promise.all([
        fetch('/api/users/angel', { credentials: 'include' }),
        fetch('/api/users/motilal', { credentials: 'include' })
      ]);

      const angelData = await angelResponse.json();
      const motilalData = await motilalResponse.json();

      const angelAccounts = (angelData.users || []).map((acc: any) => ({
        id: acc._id,
        clientCode: acc.userId || acc.clientCode,
        broker: 'angel' as const,
        isActive: acc.state === 'live',
        clientName: acc.clientName,
        capital: acc.capital
      }));

      const motilalAccounts = (motilalData.users || []).map((acc: any) => ({
        id: acc._id,
        clientCode: acc.userId || acc.clientCode,
        broker: 'motilal' as const,
        isActive: acc.status === 'active',
        clientName: acc.clientName,
        capital: acc.capital
      }));

      setAvailableAccounts([...angelAccounts, ...motilalAccounts]);
    } catch (error) {
      console.error('Error fetching accounts:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAccountToggle = (account: ClientAccount) => {
    setSelectedAccounts(prev => {
      const isSelected = prev.some(acc => acc.id === account.id);
      if (isSelected) {
        return prev.filter(acc => acc.id !== account.id);
      } else {
        return [...prev, account];
      }
    });
  };

  const handleCreateBatch = async () => {
    if (!newBatch.name.trim() || selectedAccounts.length === 0) {
      alert('Please provide a batch name and select at least one account');
      return;
    }

    const batch: UserBatch = {
      id: `batch_${Date.now()}`,
      name: newBatch.name.trim(),
      description: newBatch.description.trim(),
      accounts: selectedAccounts,
      createdAt: new Date(),
      isActive: true
    };

    try {
      // Save batch to backend
      const response = await fetch('/api/batches', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(batch)
      });

      if (response.ok) {
        setBatches(prev => [...prev, batch]);
        onBatchCreated(batch);
        
        // Reset form
        setNewBatch({ name: '', description: '' });
        setSelectedAccounts([]);
        setIsCreatingBatch(false);
      } else {
        throw new Error('Failed to create batch');
      }
    } catch (error) {
      console.error('Error creating batch:', error);
      alert('Failed to create batch. Please try again.');
    }
  };

  const handleDeleteBatch = async (batchId: string) => {
    if (!confirm('Are you sure you want to delete this batch?')) return;

    try {
      const response = await fetch(`/api/batches/${batchId}`, {
        method: 'DELETE',
        credentials: 'include'
      });

      if (response.ok) {
        setBatches(prev => prev.filter(batch => batch.id !== batchId));
      }
    } catch (error) {
      console.error('Error deleting batch:', error);
    }
  };

  const filteredAccounts = availableAccounts.filter(account =>
    account.clientCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
    account.clientName?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-3">
            <span className="text-2xl">👥</span>
            <h2 className="text-xl font-semibold text-gray-900">Batch Management</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors text-2xl"
          >
            ✕
          </button>
        </div>

        <div className="flex h-[calc(90vh-120px)]">
          {/* Left Panel - Available Accounts */}
          <div className="w-1/2 border-r">
            <div className="p-4 border-b bg-gray-50">
              <h3 className="font-medium text-gray-900 mb-3">Available Accounts</h3>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">🔍</span>
                <input
                  type="text"
                  placeholder="Search accounts..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            <div className="p-4 overflow-y-auto h-full">
              {loading ? (
                <div className="text-center py-8 text-gray-500">Loading accounts...</div>
              ) : filteredAccounts.length === 0 ? (
                <div className="text-center py-8 text-gray-500">No accounts found</div>
              ) : (
                <div className="space-y-2">
                  {filteredAccounts.map((account) => (
                    <div
                      key={account.id}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        selectedAccounts.some(acc => acc.id === account.id)
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => handleAccountToggle(account)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium text-gray-900">
                            {account.clientCode}
                          </div>
                          <div className="text-sm text-gray-500">
                            {account.clientName} • {account.broker.toUpperCase()}
                          </div>
                          {account.capital && (
                            <div className="text-xs text-gray-400">
                              Capital: ₹{account.capital.toLocaleString()}
                            </div>
                          )}
                        </div>
                        <div className={`w-3 h-3 rounded-full ${
                          account.isActive ? 'bg-green-500' : 'bg-red-500'
                        }`} />
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Right Panel - Batch Management */}
          <div className="w-1/2">
            <div className="p-4 border-b bg-gray-50">
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-gray-900">Batch Management</h3>
                <button
                  onClick={() => setIsCreatingBatch(true)}
                  className="flex items-center space-x-2 px-3 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  <span>➕</span>
                  <span>New Batch</span>
                </button>
              </div>
            </div>

            <div className="p-4 overflow-y-auto h-full">
              {/* Create New Batch Form */}
              {isCreatingBatch && (
                <div className="mb-6 p-4 border border-blue-200 rounded-lg bg-blue-50">
                  <h4 className="font-medium text-gray-900 mb-3">Create New Batch</h4>
                  <div className="space-y-3">
                    <input
                      type="text"
                      placeholder="Batch name"
                      value={newBatch.name}
                      onChange={(e) => setNewBatch(prev => ({ ...prev, name: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <textarea
                      placeholder="Description (optional)"
                      value={newBatch.description}
                      onChange={(e) => setNewBatch(prev => ({ ...prev, description: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      rows={2}
                    />
                    <div className="text-sm text-gray-600">
                      Selected accounts: {selectedAccounts.length}
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={handleCreateBatch}
                        disabled={!newBatch.name.trim() || selectedAccounts.length === 0}
                        className="flex items-center space-x-2 px-3 py-1.5 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                      >
                        <span>💾</span>
                        <span>Create Batch</span>
                      </button>
                      <button
                        onClick={() => {
                          setIsCreatingBatch(false);
                          setNewBatch({ name: '', description: '' });
                          setSelectedAccounts([]);
                        }}
                        className="px-3 py-1.5 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {/* Existing Batches */}
              <div className="space-y-4">
                {batches.map((batch) => (
                  <div key={batch.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900">{batch.name}</h4>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => setEditingBatch(batch.id)}
                          className="text-blue-600 hover:text-blue-800"
                        >
                          ✏️
                        </button>
                        <button
                          onClick={() => handleDeleteBatch(batch.id)}
                          className="text-red-600 hover:text-red-800"
                        >
                          🗑️
                        </button>
                      </div>
                    </div>
                    {batch.description && (
                      <p className="text-sm text-gray-600 mb-2">{batch.description}</p>
                    )}
                    <div className="text-sm text-gray-500">
                      {batch.accounts.length} accounts • Created {batch.createdAt.toLocaleDateString()}
                    </div>
                    <div className="mt-2 flex flex-wrap gap-1">
                      {batch.accounts.slice(0, 5).map((account) => (
                        <span
                          key={account.id}
                          className="inline-block px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded"
                        >
                          {account.clientCode}
                        </span>
                      ))}
                      {batch.accounts.length > 5 && (
                        <span className="inline-block px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                          +{batch.accounts.length - 5} more
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
