"use client";

import React, { useState, useEffect } from 'react';
import { useToast } from './Toast';

interface BillingAnalyticsData {
  overview: {
    totalRevenue: number;
    monthlyRevenue: number;
    pendingAmount: number;
    paidAmount: number;
    totalOrders: number;
    monthlyOrders: number;
    averageOrderValue: number;
    collectionRate: number;
  };
  monthlyTrends: Array<{
    month: string;
    revenue: number;
    orders: number;
    pendingAmount: number;
    paidAmount: number;
  }>;
  adminPerformance: Array<{
    adminId: string;
    adminName: string;
    adminEmail: string;
    totalRevenue: number;
    totalOrders: number;
    pendingAmount: number;
    paidAmount: number;
    averageOrderValue: number;
    collectionRate: number;
  }>;
  brokerBreakdown: {
    angel: {
      revenue: number;
      orders: number;
      pendingAmount: number;
      paidAmount: number;
    };
    motilal: {
      revenue: number;
      orders: number;
      pendingAmount: number;
      paidAmount: number;
    };
  };
}

interface BillingAnalyticsProps {
  userRole: 'super-admin' | 'admin';
}

const BillingAnalytics: React.FC<BillingAnalyticsProps> = ({ userRole }) => {
  const { showSuccess, showError } = useToast();
  const [analyticsData, setAnalyticsData] = useState<BillingAnalyticsData | null>(null);
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().getFullYear(), new Date().getMonth() - 5, 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        startDate: dateRange.startDate,
        endDate: dateRange.endDate
      });

      const endpoint = userRole === 'super-admin' 
        ? `/api/super-admin/billing/analytics?${params}`
        : `/api/admin/billing/analytics?${params}`;

      const response = await fetch(endpoint, {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to fetch analytics data');
      }

      const data = await response.json();
      setAnalyticsData(data.data);
    } catch (error) {
      console.error('Error fetching analytics:', error);
      showError('Error', 'Failed to fetch billing analytics');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalytics();
  }, [dateRange]);

  const formatCurrency = (amount: number) => `₹${amount.toLocaleString()}`;
  const formatPercentage = (value: number) => `${value.toFixed(1)}%`;

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="text-center py-8 text-gray-500">
        No analytics data available
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Date Range Filter */}
      <div className="bg-white p-4 rounded-lg shadow">
        <div className="flex items-center space-x-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
            <input
              type="date"
              value={dateRange.startDate}
              onChange={(e) => setDateRange({...dateRange, startDate: e.target.value})}
              className="p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
            <input
              type="date"
              value={dateRange.endDate}
              onChange={(e) => setDateRange({...dateRange, endDate: e.target.value})}
              className="p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div className="flex items-end">
            <button
              onClick={fetchAnalytics}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
            >
              Update
            </button>
          </div>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-6 rounded-lg text-white">
          <h3 className="text-sm font-medium opacity-90">Total Revenue</h3>
          <p className="text-3xl font-bold">{formatCurrency(analyticsData.overview.totalRevenue)}</p>
          <p className="text-sm opacity-75">All time</p>
        </div>
        
        <div className="bg-gradient-to-r from-green-500 to-green-600 p-6 rounded-lg text-white">
          <h3 className="text-sm font-medium opacity-90">Monthly Revenue</h3>
          <p className="text-3xl font-bold">{formatCurrency(analyticsData.overview.monthlyRevenue)}</p>
          <p className="text-sm opacity-75">Current month</p>
        </div>
        
        <div className="bg-gradient-to-r from-purple-500 to-purple-600 p-6 rounded-lg text-white">
          <h3 className="text-sm font-medium opacity-90">Total Orders</h3>
          <p className="text-3xl font-bold">{analyticsData.overview.totalOrders.toLocaleString()}</p>
          <p className="text-sm opacity-75">All time</p>
        </div>
        
        <div className="bg-gradient-to-r from-orange-500 to-orange-600 p-6 rounded-lg text-white">
          <h3 className="text-sm font-medium opacity-90">Collection Rate</h3>
          <p className="text-3xl font-bold">{formatPercentage(analyticsData.overview.collectionRate)}</p>
          <p className="text-sm opacity-75">Paid vs Total</p>
        </div>
      </div>

      {/* Payment Status Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Payment Status</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Paid Amount</span>
              <span className="text-lg font-semibold text-green-600">
                {formatCurrency(analyticsData.overview.paidAmount)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Pending Amount</span>
              <span className="text-lg font-semibold text-yellow-600">
                {formatCurrency(analyticsData.overview.pendingAmount)}
              </span>
            </div>
            <div className="border-t pt-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-900 font-semibold">Total Revenue</span>
                <span className="text-xl font-bold text-blue-600">
                  {formatCurrency(analyticsData.overview.totalRevenue)}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Key Metrics</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Average Order Value</span>
              <span className="text-lg font-semibold text-blue-600">
                {formatCurrency(analyticsData.overview.averageOrderValue)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Monthly Orders</span>
              <span className="text-lg font-semibold text-purple-600">
                {analyticsData.overview.monthlyOrders.toLocaleString()}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Collection Efficiency</span>
              <span className="text-lg font-semibold text-green-600">
                {formatPercentage(analyticsData.overview.collectionRate)}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Broker Performance */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Broker Performance</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="border rounded-lg p-4">
            <h4 className="font-medium text-blue-900 mb-3">Angel One</h4>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Revenue:</span>
                <span className="font-semibold">{formatCurrency(analyticsData.brokerBreakdown.angel.revenue)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Orders:</span>
                <span className="font-semibold">{analyticsData.brokerBreakdown.angel.orders.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Pending:</span>
                <span className="font-semibold text-yellow-600">{formatCurrency(analyticsData.brokerBreakdown.angel.pendingAmount)}</span>
              </div>
            </div>
          </div>
          
          <div className="border rounded-lg p-4">
            <h4 className="font-medium text-purple-900 mb-3">Motilal Oswal</h4>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Revenue:</span>
                <span className="font-semibold">{formatCurrency(analyticsData.brokerBreakdown.motilal.revenue)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Orders:</span>
                <span className="font-semibold">{analyticsData.brokerBreakdown.motilal.orders.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Pending:</span>
                <span className="font-semibold text-yellow-600">{formatCurrency(analyticsData.brokerBreakdown.motilal.pendingAmount)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Admin Performance (Super Admin Only) */}
      {userRole === 'super-admin' && analyticsData.adminPerformance && analyticsData.adminPerformance.length > 0 && (
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Admin Performance</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Admin</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Orders</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg Order</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Collection Rate</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pending</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {analyticsData.adminPerformance.map((admin) => (
                  <tr key={admin.adminId}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{admin.adminName}</div>
                        <div className="text-sm text-gray-500">{admin.adminEmail}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatCurrency(admin.totalRevenue)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {admin.totalOrders.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatCurrency(admin.averageOrderValue)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatPercentage(admin.collectionRate)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-yellow-600">
                      {formatCurrency(admin.pendingAmount)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default BillingAnalytics;
