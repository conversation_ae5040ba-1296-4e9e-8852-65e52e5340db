"use client";

import React, { useState, useEffect } from 'react';
import { useToast } from './Toast';

interface BillingRecord {
  _id: string;
  adminId: {
    _id: string;
    name: string;
    email: string;
  };
  orderId: string;
  orderType: 'BUY' | 'SELL';
  symbol: string;
  quantity: number;
  price: number;
  broker: 'angel' | 'motilal';
  orderRate: number;
  amount: number;
  status: 'pending' | 'billed' | 'paid' | 'disputed';
  billingCycle: string;
  createdAt: string;
  updatedAt: string;
}

interface BillingLogsProps {
  userRole: 'super-admin' | 'admin';
  adminId?: string;
}

const BillingLogs: React.FC<BillingLogsProps> = ({ userRole, adminId }) => {
  const { showSuccess, showError, showInfo } = useToast();
  const [billingRecords, setBillingRecords] = useState<BillingRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [filters, setFilters] = useState({
    status: '',
    adminId: adminId || '',
    startDate: '',
    endDate: '',
    broker: ''
  });
  const [statistics, setStatistics] = useState({
    totalAmount: 0,
    pendingAmount: 0,
    paidAmount: 0,
    totalRecords: 0,
    pendingCount: 0,
    paidCount: 0
  });
  const [adminBilling, setAdminBilling] = useState<any[]>([]);

  const fetchBillingLogs = async (page = 1) => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '20',
        ...Object.fromEntries(Object.entries(filters).filter(([_, v]) => v))
      });

      const endpoint = userRole === 'super-admin' 
        ? `/api/super-admin/billing-logs?${params}`
        : `/api/admin/user-billing?${params}`;

      const response = await fetch(endpoint, {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to fetch billing logs');
      }

      const data = await response.json();
      
      if (userRole === 'super-admin') {
        setBillingRecords(data.data.billingRecords || []);
        setStatistics(data.data.statistics || statistics);
        setAdminBilling(data.data.adminBilling || []);
        setTotalPages(data.data.pagination?.totalPages || 1);
        setTotalRecords(data.data.pagination?.totalRecords || 0);
      } else {
        // Admin view
        setBillingRecords(data.userBilling || []);
        setStatistics({
          totalAmount: data.summary?.totalAmount || 0,
          pendingAmount: data.summary?.pendingAmount || 0,
          paidAmount: data.summary?.paidAmount || 0,
          totalRecords: data.userBilling?.length || 0,
          pendingCount: data.userBilling?.filter((r: any) => r.status === 'pending').length || 0,
          paidCount: data.userBilling?.filter((r: any) => r.status === 'paid').length || 0
        });
      }

      setCurrentPage(page);
    } catch (error) {
      console.error('Error fetching billing logs:', error);
      showError('Failed to fetch billing logs');
    } finally {
      setLoading(false);
    }
  };

  const markPaymentCleared = async (recordId: string, adminId: string, billingCycle: string) => {
    try {
      const response = await fetch('/api/super-admin/billing/payment', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          adminId,
          billingCycle,
          paymentMethod: 'Manual',
          paymentReference: `CLEARED-${Date.now()}`,
          notes: 'Payment cleared by super admin'
        })
      });

      if (!response.ok) {
        throw new Error('Failed to clear payment');
      }

      showSuccess('Payment marked as cleared');
      fetchBillingLogs(currentPage);
    } catch (error) {
      console.error('Error clearing payment:', error);
      showError('Failed to clear payment');
    }
  };

  const exportBillingData = async () => {
    try {
      const params = new URLSearchParams({
        ...Object.fromEntries(Object.entries(filters).filter(([_, v]) => v)),
        export: 'true'
      });

      const endpoint = userRole === 'super-admin' 
        ? `/api/super-admin/billing-logs?${params}`
        : `/api/admin/user-billing?${params}`;

      const response = await fetch(endpoint, {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to export data');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `billing-logs-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      showSuccess('Billing data exported successfully');
    } catch (error) {
      console.error('Error exporting data:', error);
      showError('Failed to export data');
    }
  };

  useEffect(() => {
    fetchBillingLogs(1);
  }, [filters]);

  const getStatusBadge = (status: string) => {
    const statusStyles = {
      pending: 'bg-yellow-100 text-yellow-800',
      billed: 'bg-blue-100 text-blue-800',
      paid: 'bg-green-100 text-green-800',
      disputed: 'bg-red-100 text-red-800'
    };
    
    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${statusStyles[status as keyof typeof statusStyles] || 'bg-gray-100 text-gray-800'}`}>
        {status.toUpperCase()}
      </span>
    );
  };

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <div className="bg-blue-50 p-4 rounded-lg">
          <h4 className="font-medium text-blue-900">Total Records</h4>
          <p className="text-2xl font-bold text-blue-600">{statistics.totalRecords}</p>
        </div>
        <div className="bg-green-50 p-4 rounded-lg">
          <h4 className="font-medium text-green-900">Total Amount</h4>
          <p className="text-2xl font-bold text-green-600">₹{statistics.totalAmount.toLocaleString()}</p>
        </div>
        <div className="bg-yellow-50 p-4 rounded-lg">
          <h4 className="font-medium text-yellow-900">Pending</h4>
          <p className="text-2xl font-bold text-yellow-600">₹{statistics.pendingAmount.toLocaleString()}</p>
          <p className="text-sm text-yellow-700">{statistics.pendingCount} orders</p>
        </div>
        <div className="bg-green-50 p-4 rounded-lg">
          <h4 className="font-medium text-green-900">Paid</h4>
          <p className="text-2xl font-bold text-green-600">₹{statistics.paidAmount.toLocaleString()}</p>
          <p className="text-sm text-green-700">{statistics.paidCount} orders</p>
        </div>
        <div className="bg-purple-50 p-4 rounded-lg">
          <h4 className="font-medium text-purple-900">Collection Rate</h4>
          <p className="text-2xl font-bold text-purple-600">
            {statistics.totalAmount > 0 ? ((statistics.paidAmount / statistics.totalAmount) * 100).toFixed(1) : 0}%
          </p>
        </div>
        <div className="bg-indigo-50 p-4 rounded-lg">
          <h4 className="font-medium text-indigo-900">Avg Order Value</h4>
          <p className="text-2xl font-bold text-indigo-600">
            ₹{statistics.totalRecords > 0 ? (statistics.totalAmount / statistics.totalRecords).toFixed(2) : 0}
          </p>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select
              value={filters.status}
              onChange={(e) => setFilters({...filters, status: e.target.value})}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Status</option>
              <option value="pending">Pending</option>
              <option value="billed">Billed</option>
              <option value="paid">Paid</option>
              <option value="disputed">Disputed</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Broker</label>
            <select
              value={filters.broker}
              onChange={(e) => setFilters({...filters, broker: e.target.value})}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Brokers</option>
              <option value="angel">Angel One</option>
              <option value="motilal">Motilal Oswal</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
            <input
              type="date"
              value={filters.startDate}
              onChange={(e) => setFilters({...filters, startDate: e.target.value})}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
            <input
              type="date"
              value={filters.endDate}
              onChange={(e) => setFilters({...filters, endDate: e.target.value})}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div className="flex items-end space-x-2">
            <button
              onClick={() => fetchBillingLogs(1)}
              disabled={loading}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? 'Loading...' : 'Filter'}
            </button>
            <button
              onClick={exportBillingData}
              className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
            >
              Export
            </button>
          </div>
        </div>
      </div>

      {/* Admin Billing Summary (Super Admin Only) */}
      {userRole === 'super-admin' && adminBilling.length > 0 && (
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Admin Billing Summary</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Admin</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Orders</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Amount</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pending</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Paid</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {adminBilling.map((admin) => (
                  <tr key={admin._id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{admin.adminName}</div>
                        <div className="text-sm text-gray-500">{admin.adminEmail}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{admin.totalOrders}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">₹{admin.totalAmount.toLocaleString()}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-yellow-600">₹{admin.pendingAmount.toLocaleString()}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600">₹{admin.paidAmount.toLocaleString()}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      {admin.pendingAmount > 0 && (
                        <button
                          onClick={() => markPaymentCleared('', admin._id, new Date().toISOString().slice(0, 7))}
                          className="text-green-600 hover:text-green-900 mr-2"
                        >
                          Clear Payment
                        </button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Billing Records Table */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900">
              Billing Records ({totalRecords} total)
            </h3>
            <div className="text-sm text-gray-500">
              Page {currentPage} of {totalPages}
            </div>
          </div>

          {loading ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : billingRecords.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No billing records found
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    {userRole === 'super-admin' && (
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Admin</th>
                    )}
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Broker</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    {userRole === 'super-admin' && (
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    )}
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {billingRecords.map((record) => (
                    <tr key={record._id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {new Date(record.createdAt).toLocaleDateString()}
                      </td>
                      {userRole === 'super-admin' && (
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{record.adminId?.name || 'N/A'}</div>
                            <div className="text-sm text-gray-500">{record.adminId?.email || 'N/A'}</div>
                          </div>
                        </td>
                      )}
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-mono">{record.orderId}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-semibold">{record.symbol}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 text-xs font-medium rounded ${
                          record.orderType === 'BUY' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {record.orderType}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{record.quantity.toLocaleString()}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">₹{record.price.toFixed(2)}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 text-xs font-medium rounded ${
                          record.broker === 'angel' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'
                        }`}>
                          {record.broker.toUpperCase()}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">₹{record.orderRate}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-semibold">₹{record.amount}</td>
                      <td className="px-6 py-4 whitespace-nowrap">{getStatusBadge(record.status)}</td>
                      {userRole === 'super-admin' && (
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          {record.status === 'pending' && (
                            <button
                              onClick={() => markPaymentCleared(record._id, record.adminId._id, record.billingCycle)}
                              className="text-green-600 hover:text-green-900"
                            >
                              Clear Payment
                            </button>
                          )}
                        </td>
                      )}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-gray-700">
                Showing {((currentPage - 1) * 20) + 1} to {Math.min(currentPage * 20, totalRecords)} of {totalRecords} results
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => fetchBillingLogs(currentPage - 1)}
                  disabled={currentPage === 1 || loading}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50"
                >
                  Previous
                </button>
                <span className="px-3 py-2 text-sm text-gray-700">
                  Page {currentPage} of {totalPages}
                </span>
                <button
                  onClick={() => fetchBillingLogs(currentPage + 1)}
                  disabled={currentPage === totalPages || loading}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50"
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BillingLogs;
