'use client';

import { useState, useEffect } from 'react';

interface BillingSummary {
  _id: string;
  adminId: {
    _id: string;
    name: string;
    email: string;
    companyName?: string;
  };
  billingCycle: string;
  totalOrders: number;
  totalAmount: number;
  orderRate: number;
  status: 'draft' | 'generated' | 'sent' | 'paid' | 'overdue';
  generatedAt?: Date;
  sentAt?: Date;
  paidAt?: Date;
  dueDate?: Date;
  paymentMethod?: string;
  paymentReference?: string;
  notes?: string;
}

interface BillingData {
  billingSummaries: BillingSummary[];
  summary: {
    totalOrders: number;
    totalAmount: number;
    pendingAmount: number;
    billedAmount: number;
    paidAmount: number;
    totalRevenue: number;
  };
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

interface BillingManagementProps {
  billingData: BillingData | null;
  loading: boolean;
  onRefresh: () => void;
}

export default function BillingManagement({ billingData, loading, onRefresh }: BillingManagementProps) {
  const [selectedSummary, setSelectedSummary] = useState<BillingSummary | null>(null);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showSettlementModal, setShowSettlementModal] = useState(false);
  const [paymentForm, setPaymentForm] = useState({
    paymentMethod: '',
    paymentReference: '',
    notes: '',
    partialAmount: ''
  });

  const handleCollectPayment = async (summary: BillingSummary) => {
    setSelectedSummary(summary);
    setShowPaymentModal(true);
  };

  const handleSettleBilling = async (summary: BillingSummary) => {
    setSelectedSummary(summary);
    setShowSettlementModal(true);
  };

  const submitPayment = async () => {
    if (!selectedSummary) return;

    try {
      const response = await fetch('/api/super-admin/billing/payment', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          summaryId: selectedSummary._id,
          adminId: selectedSummary.adminId._id,
          billingCycle: selectedSummary.billingCycle,
          ...paymentForm,
          partialAmount: paymentForm.partialAmount ? parseFloat(paymentForm.partialAmount) : undefined
        })
      });

      if (response.ok) {
        alert('Payment recorded successfully!');
        setShowPaymentModal(false);
        setPaymentForm({ paymentMethod: '', paymentReference: '', notes: '', partialAmount: '' });
        onRefresh();
      } else {
        const error = await response.json();
        alert(`Error: ${error.message}`);
      }
    } catch (error) {
      console.error('Payment error:', error);
      alert('Failed to record payment');
    }
  };

  const submitSettlement = async () => {
    if (!selectedSummary) return;

    try {
      const response = await fetch('/api/super-admin/billing', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          summaryId: selectedSummary._id,
          status: 'paid',
          paymentMethod: paymentForm.paymentMethod,
          paymentReference: paymentForm.paymentReference,
          notes: paymentForm.notes
        })
      });

      if (response.ok) {
        alert('Billing settled successfully!');
        setShowSettlementModal(false);
        setPaymentForm({ paymentMethod: '', paymentReference: '', notes: '', partialAmount: '' });
        onRefresh();
      } else {
        const error = await response.json();
        alert(`Error: ${error.message}`);
      }
    } catch (error) {
      console.error('Settlement error:', error);
      alert('Failed to settle billing');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'text-green-600 bg-green-100';
      case 'generated': return 'text-blue-600 bg-blue-100';
      case 'overdue': return 'text-red-600 bg-red-100';
      case 'sent': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const formatDate = (date: Date | string | undefined) => {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-gray-500">Loading billing data...</div>
      </div>
    );
  }

  if (!billingData) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500 mb-4">No billing data available</div>
        <button
          onClick={onRefresh}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
        >
          Refresh Data
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Billing Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="text-sm text-gray-600">Total Orders</div>
          <div className="text-2xl font-bold text-gray-900">{billingData.summary.totalOrders}</div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="text-sm text-gray-600">Total Revenue</div>
          <div className="text-2xl font-bold text-green-600">₹{billingData.summary.totalRevenue.toLocaleString()}</div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="text-sm text-gray-600">Pending Amount</div>
          <div className="text-2xl font-bold text-red-600">₹{billingData.summary.pendingAmount.toLocaleString()}</div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="text-sm text-gray-600">Paid Amount</div>
          <div className="text-2xl font-bold text-green-600">₹{billingData.summary.paidAmount.toLocaleString()}</div>
        </div>
      </div>

      {/* Billing Table */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Admin Billing Summary</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Admin
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Billing Cycle
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Orders
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Due Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {billingData.billingSummaries && billingData.billingSummaries.length > 0 ? (
                billingData.billingSummaries.map((summary) => (
                  <tr key={summary._id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {summary.adminId?.name || 'N/A'}
                        </div>
                        <div className="text-sm text-gray-500">
                          {summary.adminId?.email || 'N/A'}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {summary.billingCycle}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {summary.totalOrders}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      ₹{summary.totalAmount.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(summary.status)}`}>
                        {summary.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatDate(summary.dueDate)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                      {summary.status !== 'paid' && (
                        <>
                          <button
                            onClick={() => handleCollectPayment(summary)}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            Collect Payment
                          </button>
                          <button
                            onClick={() => handleSettleBilling(summary)}
                            className="text-green-600 hover:text-green-900"
                          >
                            Settle
                          </button>
                        </>
                      )}
                      {summary.status === 'paid' && (
                        <span className="text-green-600">✓ Settled</span>
                      )}
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={7} className="px-6 py-4 text-center text-gray-500">
                    No billing summaries found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Payment Modal */}
      {showPaymentModal && selectedSummary && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Collect Payment - {selectedSummary.adminId.name}
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Payment Method</label>
                <select
                  value={paymentForm.paymentMethod}
                  onChange={(e) => setPaymentForm(prev => ({ ...prev, paymentMethod: e.target.value }))}
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                >
                  <option value="">Select Method</option>
                  <option value="bank_transfer">Bank Transfer</option>
                  <option value="upi">UPI</option>
                  <option value="cash">Cash</option>
                  <option value="cheque">Cheque</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Payment Reference</label>
                <input
                  type="text"
                  value={paymentForm.paymentReference}
                  onChange={(e) => setPaymentForm(prev => ({ ...prev, paymentReference: e.target.value }))}
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                  placeholder="Transaction ID, Cheque No, etc."
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Partial Amount (Optional)
                </label>
                <input
                  type="number"
                  value={paymentForm.partialAmount}
                  onChange={(e) => setPaymentForm(prev => ({ ...prev, partialAmount: e.target.value }))}
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                  placeholder={`Full amount: ₹${selectedSummary.totalAmount}`}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Notes</label>
                <textarea
                  value={paymentForm.notes}
                  onChange={(e) => setPaymentForm(prev => ({ ...prev, notes: e.target.value }))}
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                  rows={3}
                />
              </div>
            </div>
            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => setShowPaymentModal(false)}
                className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400"
              >
                Cancel
              </button>
              <button
                onClick={submitPayment}
                className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700"
              >
                Record Payment
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Settlement Modal */}
      {showSettlementModal && selectedSummary && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Settle Billing - {selectedSummary.adminId.name}
            </h3>
            <div className="mb-4 p-4 bg-gray-50 rounded-lg">
              <div className="text-sm text-gray-600">Amount to Settle</div>
              <div className="text-2xl font-bold text-green-600">₹{selectedSummary.totalAmount.toLocaleString()}</div>
            </div>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Confirmation Notes</label>
                <textarea
                  value={paymentForm.notes}
                  onChange={(e) => setPaymentForm(prev => ({ ...prev, notes: e.target.value }))}
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                  rows={3}
                  placeholder="Payment confirmed, settlement completed..."
                />
              </div>
            </div>
            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => setShowSettlementModal(false)}
                className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400"
              >
                Cancel
              </button>
              <button
                onClick={submitSettlement}
                className="flex-1 bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700"
              >
                Settle Billing
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
