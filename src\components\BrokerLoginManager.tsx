'use client';

import React, { useState, useEffect } from 'react';
import { useToast } from './Toast';

interface BrokerLoginManagerProps {
  isOpen: boolean;
  onClose: () => void;
}

interface LoginResult {
  userId: string;
  clientName: string;
  status: 'success' | 'failed';
  message: string;
  error?: string;
}

const BrokerLoginManager: React.FC<BrokerLoginManagerProps> = ({ isOpen, onClose }) => {
  const { showSuccess, showError, showInfo, showWarning } = useToast();
  const [loading, setLoading] = useState(false);
  const [angelResults, setAngelResults] = useState<LoginResult[]>([]);
  const [motilalResults, setMotilalResults] = useState<LoginResult[]>([]);
  const [lastLoginTime, setLastLoginTime] = useState<string>('');

  useEffect(() => {
    if (isOpen) {
      // Load last login time from localStorage
      const lastLogin = localStorage.getItem('lastBrokerLogin');
      if (lastLogin) {
        setLastLoginTime(new Date(lastLogin).toLocaleString());
      }
    }
  }, [isOpen]);

  const performBrokerLogin = async (broker: 'angel' | 'motilal' | 'both') => {
    setLoading(true);
    
    try {
      const actions: string[] = [];

      if (broker === 'angel' || broker === 'both') {
        actions.push('login-all-angel');
      }

      if (broker === 'motilal' || broker === 'both') {
        actions.push('login-all-motilal');
      }

      const results = await Promise.allSettled(
        actions.map(action => 
          fetch('/api/auth/broker-login', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('auth-token') || ''}`,
            },
            body: JSON.stringify({ action }),
          }).then(res => res.json())
        )
      );

      let totalSuccessful = 0;
      let totalFailed = 0;

      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          const data = result.value;
          const isAngel = actions[index] === 'login-all-angel';
          
          if (data.success) {
            if (isAngel) {
              setAngelResults(data.results || []);
            } else {
              setMotilalResults(data.results || []);
            }
            
            const successful = data.summary?.successful || 0;
            const failed = data.summary?.failed || 0;
            
            totalSuccessful += successful;
            totalFailed += failed;
            
            showInfo(
              `${isAngel ? 'Angel' : 'Motilal'} Login Complete`,
              `${successful} successful, ${failed} failed`
            );
          } else {
            showError(
              `${isAngel ? 'Angel' : 'Motilal'} Login Failed`,
              data.message || 'Unknown error'
            );
          }
        } else {
          showError(
            'Network Error',
            `Failed to login to ${actions[index].includes('angel') ? 'Angel' : 'Motilal'}`
          );
        }
      });

      // Update last login time
      const now = new Date().toISOString();
      localStorage.setItem('lastBrokerLogin', now);
      setLastLoginTime(new Date(now).toLocaleString());

      // Show overall summary
      if (totalSuccessful > 0 || totalFailed > 0) {
        if (totalFailed === 0) {
          showSuccess(
            'All Logins Successful!',
            `Successfully logged in ${totalSuccessful} accounts`
          );
        } else if (totalSuccessful === 0) {
          showError(
            'All Logins Failed',
            `Failed to login ${totalFailed} accounts`
          );
        } else {
          showWarning(
            'Partial Success',
            `${totalSuccessful} successful, ${totalFailed} failed`
          );
        }
      }

    } catch (error) {
      console.error('Error during broker login:', error);
      showError(
        'Login Error',
        'Failed to perform broker login. Please try again.'
      );
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-800">Broker Login Manager</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 text-2xl"
          >
            ×
          </button>
        </div>

        {/* Last Login Info */}
        {lastLoginTime && (
          <div className="mb-6 bg-blue-50 border border-blue-200 rounded-md p-4">
            <p className="text-sm text-blue-800">
              <strong>Last Login:</strong> {lastLoginTime}
            </p>
          </div>
        )}

        {/* Login Actions */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-4">Login Actions</h3>
          <div className="flex gap-4">
            <button
              onClick={() => performBrokerLogin('angel')}
              disabled={loading}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Logging in...' : 'Login All Angel Accounts'}
            </button>
            
            <button
              onClick={() => performBrokerLogin('motilal')}
              disabled={loading}
              className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Logging in...' : 'Login All Motilal Accounts'}
            </button>
            
            <button
              onClick={() => performBrokerLogin('both')}
              disabled={loading}
              className="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Logging in...' : 'Login All Accounts'}
            </button>
          </div>
        </div>

        {/* Results */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Angel Results */}
          <div>
            <h3 className="text-lg font-semibold mb-3 text-blue-800">Angel One Results</h3>
            {angelResults.length > 0 ? (
              <div className="space-y-2">
                {angelResults.map((result, index) => (
                  <div
                    key={index}
                    className={`p-3 rounded-md border ${
                      result.status === 'success'
                        ? 'bg-green-50 border-green-200'
                        : 'bg-red-50 border-red-200'
                    }`}
                  >
                    <div className="flex justify-between items-center">
                      <span className="font-medium">{result.clientName || result.userId}</span>
                      <span
                        className={`px-2 py-1 rounded-full text-xs ${
                          result.status === 'success'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {result.status}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{result.message}</p>
                    {result.error && (
                      <p className="text-xs text-red-600 mt-1">{result.error}</p>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-sm">No results yet. Click login to see results.</p>
            )}
          </div>

          {/* Motilal Results */}
          <div>
            <h3 className="text-lg font-semibold mb-3 text-green-800">Motilal Oswal Results</h3>
            {motilalResults.length > 0 ? (
              <div className="space-y-2">
                {motilalResults.map((result, index) => (
                  <div
                    key={index}
                    className={`p-3 rounded-md border ${
                      result.status === 'success'
                        ? 'bg-green-50 border-green-200'
                        : 'bg-red-50 border-red-200'
                    }`}
                  >
                    <div className="flex justify-between items-center">
                      <span className="font-medium">{result.clientName || result.userId}</span>
                      <span
                        className={`px-2 py-1 rounded-full text-xs ${
                          result.status === 'success'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {result.status}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{result.message}</p>
                    {result.error && (
                      <p className="text-xs text-red-600 mt-1">{result.error}</p>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-sm">No results yet. Click login to see results.</p>
            )}
          </div>
        </div>

        {/* Instructions */}
        <div className="mt-6 bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <h4 className="font-medium text-yellow-800 mb-2">Instructions:</h4>
          <ul className="text-sm text-yellow-700 space-y-1">
            <li>• Login should be performed daily before market hours (before 9:15 AM)</li>
            <li>• All accounts need valid TOTP keys and credentials in the database</li>
            <li>• Failed logins may require manual intervention or credential updates</li>
            <li>• Successful logins will store JWT tokens for order placement</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default BrokerLoginManager;
