import React, { memo, useMemo, useState, useCallback } from "react";
import TradingOrderForm from './TradingOrderForm';

interface MarketData {
  ticker: string;
  securityId: string;
  exchange: string;
  exchangeCode: number;
  ltp: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  close: number;
  timestamp: number;
}

interface MarketDataTableProps {
  data: MarketData[];
  onRefresh?: () => void;
  onOrderPlacement?: (script: MarketData, orderType: 'BUY' | 'SELL') => void;
}

// Optimized formatters outside component for better performance
const formatNumber = (num: number) => {
  return num.toLocaleString(undefined, {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
};

const formatVolume = (volume: number) => {
  if (volume >= 10000000) return `${(volume / 10000000).toFixed(1)}Cr`;
  if (volume >= 100000) return `${(volume / 100000).toFixed(1)}L`;
  if (volume >= 1000) return `${(volume / 1000).toFixed(1)}K`;
  return volume.toString();
};

const getPriceClass = (change: number) => {
  if (change > 0) return "text-green-600 font-medium";
  if (change < 0) return "text-red-600 font-medium";
  return "text-gray-900";
};

// Ultra-fast memoized row component
const MarketDataRow = memo(({
  item,
  index,
  isSelected,
  onSelect,
  onBuyClick,
  onSellClick
}: {
  item: MarketData;
  index: number;
  isSelected: boolean;
  onSelect: () => void;
  onBuyClick: () => void;
  onSellClick: () => void;
}) => {

  // Pre-calculate values for better performance
  const priceClass = getPriceClass(item.change);
  const changeSign = item.change >= 0 ? "+" : "";
  const changePercentSign = item.changePercent >= 0 ? "+" : "";
  const formattedLtp = formatNumber(item.ltp);
  const formattedChange = formatNumber(item.change);
  const formattedChangePercent = formatNumber(item.changePercent);
  const formattedVolume = formatVolume(item.volume);
  const formattedHigh = formatNumber(item.high);
  const formattedLow = formatNumber(item.low);
  const formattedOpen = formatNumber(item.open);
  const formattedClose = formatNumber(item.close);
  const timeString = new Date(item.timestamp).toLocaleTimeString('en-US', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit'
  });

  const rowClass = `cursor-pointer transition-colors hover:bg-gray-50 ${
    isSelected ? 'bg-blue-50 ring-1 ring-blue-200' : ''
  } ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`;

  return (
    <tr className={rowClass} onClick={onSelect}>
      <td className="px-3 py-2 text-sm font-medium text-blue-600 truncate max-w-[100px]">
        {item.ticker}
      </td>
      <td className={`px-3 py-2 text-sm text-right font-medium ${priceClass}`}>
        ₹{formattedLtp}
      </td>
      <td className={`px-3 py-2 text-sm text-right font-medium ${priceClass}`}>
        {changeSign}{formattedChange}
      </td>
      <td className={`px-3 py-2 text-sm text-right font-medium ${priceClass}`}>
        {changePercentSign}{formattedChangePercent}%
      </td>
      <td className="px-3 py-2 text-sm text-right text-gray-600">
        {formattedVolume}
      </td>
      <td className="px-3 py-2 text-sm text-right text-gray-600">
        ₹{formattedHigh}
      </td>
      <td className="px-3 py-2 text-sm text-right text-gray-600">
        ₹{formattedLow}
      </td>
      <td className="px-3 py-2 text-sm text-right text-gray-600">
        ₹{formattedOpen}
      </td>
      <td className="px-3 py-2 text-sm text-right text-gray-600">
        ₹{formattedClose}
      </td>
      <td className="px-3 py-2 text-sm text-right text-gray-500">
        {timeString}
      </td>
      <td className="px-2 py-2 text-center">
        <div className="flex items-center justify-center space-x-1">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onBuyClick();
            }}
            className="w-7 h-7 bg-green-600 text-white rounded text-xs font-bold hover:bg-green-700 active:bg-green-800 transition-none"
            title="Buy (+)"
          >
            B
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              onSellClick();
            }}
            className="w-7 h-7 bg-red-600 text-white rounded text-xs font-bold hover:bg-red-700 active:bg-red-800 transition-none"
            title="Sell (-)"
          >
            S
          </button>
        </div>
      </td>
    </tr>
  );
});

MarketDataRow.displayName = "MarketDataRow";

function MarketDataTable({ data, onRefresh, onOrderPlacement }: MarketDataTableProps) {
  const [selectedScript, setSelectedScript] = useState<MarketData | null>(null);
  const [orderType, setOrderType] = useState<'BUY' | 'SELL'>('BUY');
  const [isOrderFormOpen, setIsOrderFormOpen] = useState(false);
  const [selectedRowIndex, setSelectedRowIndex] = useState<number>(0);

  // Optimized keyboard shortcuts with useCallback
  const handleKeyPress = useCallback((event: KeyboardEvent) => {
    // Don't trigger if user is typing in an input field
    if (event.target instanceof HTMLInputElement ||
        event.target instanceof HTMLTextAreaElement ||
        event.target instanceof HTMLSelectElement) {
      return;
    }

    if (selectedRowIndex >= 0 && selectedRowIndex < data.length) {
      const script = data[selectedRowIndex];

      if (event.key === '+' || event.key === '=') {
        event.preventDefault();
        openOrderForm(script, 'BUY');
      } else if (event.key === '-' || event.key === '_') {
        event.preventDefault();
        openOrderForm(script, 'SELL');
      }
    }

    // Arrow key navigation
    if (event.key === 'ArrowUp') {
      event.preventDefault();
      setSelectedRowIndex(prev => Math.max(0, prev - 1));
    } else if (event.key === 'ArrowDown') {
      event.preventDefault();
      setSelectedRowIndex(prev => Math.min(data.length - 1, prev + 1));
    }
  }, [selectedRowIndex, data]);

  // Optimized event listener setup
  React.useEffect(() => {
    document.addEventListener('keydown', handleKeyPress, { passive: false });
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);

  const openOrderForm = (script: MarketData, type: 'BUY' | 'SELL') => {
    console.log('Opening order form for:', script.ticker, 'Type:', type);

    // Use the prop if provided, otherwise use internal state
    if (onOrderPlacement) {
      onOrderPlacement(script, type);
    } else {
      setSelectedScript(script);
      setOrderType(type);
      setIsOrderFormOpen(true);
    }
  };

  const closeOrderForm = () => {
    setIsOrderFormOpen(false);
    setSelectedScript(null);
  };

  // Memory-optimized rendered rows with virtualization consideration
  const renderedRows = useMemo(() => {
    // Limit rendering to prevent memory issues with large datasets
    const maxRenderItems = 200; // Only render up to 200 items at once
    const itemsToRender = data.slice(0, maxRenderItems);

    return itemsToRender.map((item, index) => (
      <MarketDataRow
        key={item.securityId}
        item={item}
        index={index}
        isSelected={selectedRowIndex === index}
        onSelect={() => setSelectedRowIndex(index)}
        onBuyClick={() => openOrderForm(item, 'BUY')}
        onSellClick={() => openOrderForm(item, 'SELL')}
      />
    ));
  }, [data, selectedRowIndex]);

  if (data.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500 text-lg">No market data available</div>
        <div className="text-gray-400 text-sm mt-2">
          Waiting for real-time updates...
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg overflow-hidden">
      {/* Header */}
      <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-800">Market Data</h2>
          <div className="flex items-center space-x-4">
            <div className="text-sm text-gray-600">
              <kbd className="px-2 py-1 bg-white rounded border">↑↓</kbd> Navigate
              <kbd className="px-2 py-1 bg-white rounded border ml-2">+</kbd> Buy
              <kbd className="px-2 py-1 bg-white rounded border ml-1">-</kbd> Sell
            </div>
            {onRefresh && (
              <button
                onClick={onRefresh}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:ring-2 focus:ring-blue-500"
              >
                Refresh
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Optimized Table */}
      <div className="overflow-x-auto max-h-[600px] overflow-y-auto">
        <table className="min-w-full divide-y divide-gray-200 table-fixed">
          <thead className="bg-gray-50 sticky top-0 z-10">
            <tr>
              <th className="w-24 px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Ticker
              </th>
              <th className="w-20 px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                LTP
              </th>
              <th className="w-20 px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Change
              </th>
              <th className="w-20 px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Change %
              </th>
              <th className="w-20 px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Volume
              </th>
              <th className="w-20 px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                High
              </th>
              <th className="w-20 px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Low
              </th>
              <th className="w-20 px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Open
              </th>
              <th className="w-20 px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Close
              </th>
              <th className="w-16 px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Time
              </th>
              <th className="w-20 px-2 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {renderedRows}
          </tbody>
        </table>
      </div>

      {/* Trading Order Form Modal */}
      {selectedScript && (
        <TradingOrderForm
          isOpen={isOrderFormOpen}
          onClose={closeOrderForm}
          orderType={orderType}
          scriptData={selectedScript}
        />
      )}

      {/* Footer with shortcuts info */}
      <div className="bg-gray-50 px-6 py-3 border-t border-gray-200">
        <div className="text-xs text-gray-600 flex items-center justify-between">
          <div>
            <strong>Keyboard Shortcuts:</strong>
            <span className="ml-2">Use ↑↓ to navigate, + for Buy, - for Sell</span>
          </div>
          <div>
            Total Scripts: {data.length}
          </div>
        </div>
      </div>
    </div>
  );
}

export default memo(MarketDataTable);
