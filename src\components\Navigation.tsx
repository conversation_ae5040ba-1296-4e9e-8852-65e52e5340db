'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';

export default function Navigation() {
  const { user, logout } = useAuth();
  const router = useRouter();

  const handleLogout = async () => {
    await logout();
    router.push('/login');
  };

  if (!user) {
    return null;
  }

  return (
    <nav className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <h1 className="text-xl font-semibold text-gray-900">
              Trading Platform SaaS
            </h1>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">Welcome,</span>
              <span className="text-sm font-medium text-gray-900">{user.name}</span>
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                user.role === 'super_admin' 
                  ? 'bg-purple-100 text-purple-800'
                  : user.role === 'admin'
                  ? 'bg-blue-100 text-blue-800'
                  : 'bg-green-100 text-green-800'
              }`}>
                {user.role === 'super_admin' ? 'Super Admin' : user.role === 'admin' ? 'Admin' : 'User'}
              </span>
            </div>
            
            <div className="flex items-center space-x-2">
              {user.role === 'super_admin' && (
                <>
                  <button
                    onClick={() => router.push('/super-admin')}
                    className="text-sm text-gray-600 hover:text-gray-900"
                  >
                    Dashboard
                  </button>
                  <button
                    onClick={() => router.push('/super-admin/invite-admin')}
                    className="text-sm text-gray-600 hover:text-gray-900"
                  >
                    Invite Admin
                  </button>
                </>
              )}
              
              {user.role === 'admin' && (
                <button
                  onClick={() => router.push('/admin')}
                  className="text-sm text-gray-600 hover:text-gray-900"
                >
                  Dashboard
                </button>
              )}
              
              <button
                onClick={() => router.push('/trading')}
                className="text-sm text-gray-600 hover:text-gray-900"
              >
                Trading
              </button>
              
              <button
                onClick={handleLogout}
                className="text-sm bg-red-600 text-white px-3 py-1 rounded hover:bg-red-700"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
}
