'use client';

import { useState, useEffect } from 'react';

interface CleanupStatistics {
  total: {
    orderResponses: number;
    simpleOrders: number;
    batchHistory: number;
    billingRecords: number;
    totalRecords: number;
  };
  cleanupable: {
    orderResponses: number;
    simpleOrders: number;
    batchHistory: number;
    billingRecords: number;
    totalRecords: number;
  };
  storage: {
    currentStorageMB: string;
    cleanupableStorageMB: string;
    percentageCleanupable: string;
  };
}

interface CleanupData {
  cutoffDate: string;
  statistics: CleanupStatistics;
}

export default function OrderCleanup() {
  const [cleanupData, setCleanupData] = useState<CleanupData | null>(null);
  const [loading, setLoading] = useState(true);
  const [cleanupLoading, setCleanupLoading] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [cleanupForm, setCleanupForm] = useState({
    cutoffDate: '',
    orderTypes: [] as string[],
    includeSuccessful: true,
    includeFailed: true,
    dryRun: true
  });

  const fetchCleanupStatistics = async (days = 30) => {
    try {
      setLoading(true);
      const response = await fetch(`/api/super-admin/orders/cleanup?days=${days}`, {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to fetch cleanup statistics');
      }

      const data = await response.json();
      setCleanupData(data.data);
      
      // Set default cutoff date
      if (!cleanupForm.cutoffDate) {
        setCleanupForm(prev => ({
          ...prev,
          cutoffDate: new Date(data.data.cutoffDate).toISOString().split('T')[0]
        }));
      }
    } catch (error) {
      console.error('Error fetching cleanup statistics:', error);
      setCleanupData(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCleanupStatistics();
  }, []);

  const handleCleanup = async () => {
    try {
      setCleanupLoading(true);
      const response = await fetch('/api/super-admin/orders/cleanup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          cutoffDate: cleanupForm.cutoffDate,
          orderTypes: cleanupForm.orderTypes.length > 0 ? cleanupForm.orderTypes : undefined,
          includeSuccessful: cleanupForm.includeSuccessful,
          includeFailed: cleanupForm.includeFailed,
          dryRun: cleanupForm.dryRun
        })
      });

      if (!response.ok) {
        throw new Error('Failed to perform cleanup');
      }

      const result = await response.json();
      
      if (cleanupForm.dryRun) {
        alert(`Dry Run Results:\n\nEstimated Deletions:\n- Order Responses: ${result.data.estimatedDeletions.orderResponses}\n- Simple Orders: ${result.data.estimatedDeletions.simpleOrders}\n- Batch History: ${result.data.estimatedDeletions.batchOrderHistory}\n- Billing Records: ${result.data.estimatedDeletions.billingRecords}\n\nTotal: ${result.data.estimatedDeletions.total} records\nEstimated Space Saved: ${result.data.estimatedSpaceSaved}`);
      } else {
        alert(`Cleanup Completed!\n\nDeleted:\n- Order Responses: ${result.data.deletionSummary.orderResponses}\n- Simple Orders: ${result.data.deletionSummary.simpleOrders}\n- Batch History: ${result.data.deletionSummary.batchOrderHistory}\n- Billing Records: ${result.data.deletionSummary.billingRecords}\n\nTotal: ${result.data.deletionSummary.totalDeleted} records\nSpace Saved: ${result.data.spaceSavedMB} MB`);
        
        // Refresh statistics after actual cleanup
        fetchCleanupStatistics();
      }
      
      setShowConfirmModal(false);
    } catch (error) {
      console.error('Cleanup error:', error);
      alert('Failed to perform cleanup');
    } finally {
      setCleanupLoading(false);
    }
  };

  const handleOrderTypeChange = (orderType: string, checked: boolean) => {
    setCleanupForm(prev => ({
      ...prev,
      orderTypes: checked 
        ? [...prev.orderTypes, orderType]
        : prev.orderTypes.filter(type => type !== orderType)
    }));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">Order Database Cleanup</h2>
        <div className="flex space-x-3">
          <button
            onClick={() => fetchCleanupStatistics()}
            className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700"
          >
            🔄 Refresh Stats
          </button>
          <button
            onClick={() => setShowConfirmModal(true)}
            className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700"
          >
            🗑️ Start Cleanup
          </button>
        </div>
      </div>

      {/* Statistics Cards */}
      {cleanupData && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-sm text-gray-600">Total Records</div>
            <div className="text-2xl font-bold text-gray-900">{cleanupData.statistics.total.totalRecords.toLocaleString()}</div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-sm text-gray-600">Cleanupable Records</div>
            <div className="text-2xl font-bold text-orange-600">{cleanupData.statistics.cleanupable.totalRecords.toLocaleString()}</div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-sm text-gray-600">Current Storage</div>
            <div className="text-2xl font-bold text-blue-600">{cleanupData.statistics.storage.currentStorageMB} MB</div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-sm text-gray-600">Cleanupable Storage</div>
            <div className="text-2xl font-bold text-green-600">{cleanupData.statistics.storage.cleanupableStorageMB} MB</div>
          </div>
        </div>
      )}

      {/* Detailed Statistics */}
      {cleanupData && (
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Cleanup Statistics</h3>
            <p className="text-sm text-gray-600">
              Records older than {new Date(cleanupData.cutoffDate).toLocaleDateString()} can be cleaned up
            </p>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Record Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Total Records
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Cleanupable
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Percentage
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    Order Responses
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {cleanupData.statistics.total.orderResponses.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-orange-600">
                    {cleanupData.statistics.cleanupable.orderResponses.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {cleanupData.statistics.total.orderResponses > 0 
                      ? ((cleanupData.statistics.cleanupable.orderResponses / cleanupData.statistics.total.orderResponses) * 100).toFixed(1)
                      : '0'
                    }%
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    Simple Orders
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {cleanupData.statistics.total.simpleOrders.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-orange-600">
                    {cleanupData.statistics.cleanupable.simpleOrders.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {cleanupData.statistics.total.simpleOrders > 0 
                      ? ((cleanupData.statistics.cleanupable.simpleOrders / cleanupData.statistics.total.simpleOrders) * 100).toFixed(1)
                      : '0'
                    }%
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    Batch Order History
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {cleanupData.statistics.total.batchHistory.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-orange-600">
                    {cleanupData.statistics.cleanupable.batchHistory.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {cleanupData.statistics.total.batchHistory > 0 
                      ? ((cleanupData.statistics.cleanupable.batchHistory / cleanupData.statistics.total.batchHistory) * 100).toFixed(1)
                      : '0'
                    }%
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    Billing Records (Paid/Disputed)
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {cleanupData.statistics.total.billingRecords.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-orange-600">
                    {cleanupData.statistics.cleanupable.billingRecords.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {cleanupData.statistics.total.billingRecords > 0 
                      ? ((cleanupData.statistics.cleanupable.billingRecords / cleanupData.statistics.total.billingRecords) * 100).toFixed(1)
                      : '0'
                    }%
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Cleanup Configuration Modal */}
      {showConfirmModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Configure Order Cleanup
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Cutoff Date</label>
                <input
                  type="date"
                  value={cleanupForm.cutoffDate}
                  onChange={(e) => setCleanupForm(prev => ({ ...prev, cutoffDate: e.target.value }))}
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                />
                <p className="text-xs text-gray-500 mt-1">Orders before this date will be deleted</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Order Types (Optional)</label>
                <div className="space-y-2">
                  {['BUY', 'SELL'].map(type => (
                    <label key={type} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={cleanupForm.orderTypes.includes(type)}
                        onChange={(e) => handleOrderTypeChange(type, e.target.checked)}
                        className="mr-2"
                      />
                      <span className="text-sm text-gray-700">{type} Orders</span>
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Include Orders</label>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={cleanupForm.includeSuccessful}
                      onChange={(e) => setCleanupForm(prev => ({ ...prev, includeSuccessful: e.target.checked }))}
                      className="mr-2"
                    />
                    <span className="text-sm text-gray-700">Successful Orders</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={cleanupForm.includeFailed}
                      onChange={(e) => setCleanupForm(prev => ({ ...prev, includeFailed: e.target.checked }))}
                      className="mr-2"
                    />
                    <span className="text-sm text-gray-700">Failed Orders</span>
                  </label>
                </div>
              </div>

              <div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={cleanupForm.dryRun}
                    onChange={(e) => setCleanupForm(prev => ({ ...prev, dryRun: e.target.checked }))}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700">Dry Run (Preview only, don't delete)</span>
                </label>
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => setShowConfirmModal(false)}
                className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400"
              >
                Cancel
              </button>
              <button
                onClick={handleCleanup}
                disabled={cleanupLoading}
                className={`flex-1 py-2 px-4 rounded-md text-white ${
                  cleanupForm.dryRun 
                    ? 'bg-blue-600 hover:bg-blue-700' 
                    : 'bg-red-600 hover:bg-red-700'
                } disabled:opacity-50`}
              >
                {cleanupLoading ? 'Processing...' : cleanupForm.dryRun ? 'Preview Cleanup' : 'Delete Orders'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
