'use client';

import React, { useState, useEffect } from 'react';
import { useToast } from './Toast';

interface Order {
  _id: string;
  clientId: string;
  orderType: string;
  strategyName: string;
  broker: string;
  symboltoken: string;
  createdAt: string;
  details: {
    status: boolean;
    message: string;
    script: string;
    orderid: string;
    uniqueorderid: string;
    response: any;
  };
}

interface OrderManagementProps {
  isOpen: boolean;
  onClose: () => void;
}

const OrderManagement: React.FC<OrderManagementProps> = ({ isOpen, onClose }) => {
  const { showSuccess, showError, showInfo } = useToast();
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(false);
  const [filter, setFilter] = useState<'all' | 'success' | 'failed'>('all');
  const [brokerFilter, setBrokerFilter] = useState<'all' | 'angel' | 'motilal'>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalOrders, setTotalOrders] = useState(0);

  useEffect(() => {
    if (isOpen) {
      setCurrentPage(1);
      fetchOrders(1);
    }
  }, [isOpen, filter, brokerFilter, itemsPerPage]);

  useEffect(() => {
    if (isOpen) {
      fetchOrders(currentPage);
    }
  }, [currentPage]);

  const fetchOrders = async (page = currentPage) => {
    setLoading(true);
    try {
      const offset = (page - 1) * itemsPerPage;
      const params = new URLSearchParams({
        limit: itemsPerPage.toString(),
        offset: offset.toString(),
      });

      if (filter !== 'all') {
        params.append('status', filter);
      }
      if (brokerFilter !== 'all') {
        params.append('broker', brokerFilter);
      }

      const response = await fetch(`/api/orders/history?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth-token') || ''}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setOrders(data.orders || []);
        setTotalOrders(data.pagination?.total || 0);
      } else {
        showError('Error', 'Failed to fetch order history');
      }
    } catch (error) {
      showError('Network Error', 'Failed to fetch orders');
    } finally {
      setLoading(false);
    }
  };

  const cancelOrder = async (orderId: string, broker: string) => {
    try {
      const response = await fetch(`/api/orders/${broker}/cancel`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth-token') || ''}`,
        },
        body: JSON.stringify({ orderId }),
      });

      if (response.ok) {
        showSuccess('Order Cancelled', 'Order cancelled successfully');
        fetchOrders(); // Refresh orders
      } else {
        const error = await response.json();
        showError('Cancellation Failed', error.message || 'Failed to cancel order');
      }
    } catch (error) {
      showError('Network Error', 'Failed to cancel order');
    }
  };

  // Calculate pagination info
  const totalPages = Math.ceil(totalOrders / itemsPerPage);
  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalOrders);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-6xl mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-800">Order Management</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 text-2xl"
          >
            ×
          </button>
        </div>

        {/* Filters and Pagination Controls */}
        <div className="flex flex-wrap gap-4 mb-6 items-end">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value as any)}
              className="border border-gray-300 rounded-md px-3 py-2"
            >
              <option value="all">All Orders</option>
              <option value="success">Successful</option>
              <option value="failed">Failed</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Broker</label>
            <select
              value={brokerFilter}
              onChange={(e) => setBrokerFilter(e.target.value as any)}
              className="border border-gray-300 rounded-md px-3 py-2"
            >
              <option value="all">All Brokers</option>
              <option value="angel">Angel</option>
              <option value="motilal">Motilal</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Show</label>
            <select
              value={itemsPerPage}
              onChange={(e) => setItemsPerPage(Number(e.target.value))}
              className="border border-gray-300 rounded-md px-3 py-2"
            >
              <option value={10}>10</option>
              <option value={25}>25</option>
              <option value={50}>50</option>
              <option value={100}>100</option>
            </select>
            <span className="ml-2 text-sm text-gray-600">per page</span>
          </div>

          <div className="flex items-center gap-2 text-sm text-gray-600">
            <span>Showing {totalOrders > 0 ? startItem : 0} to {endItem} of {totalOrders} results</span>
          </div>

          <div className="flex items-end ml-auto">
            <button
              onClick={() => fetchOrders(currentPage)}
              disabled={loading}
              className="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 disabled:opacity-50"
            >
              {loading ? 'Loading...' : 'Refresh'}
            </button>
          </div>
        </div>

        {/* Orders Table */}
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white border border-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Time</th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Client</th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Script</th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Broker</th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Order ID</th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Message</th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {orders.map((order, index) => (
                <tr key={order._id || `order-${index}-${order.createdAt || Date.now()}`} className="hover:bg-gray-50">
                  <td className="px-4 py-2 text-sm text-gray-900">
                    {order.createdAt ? new Date(order.createdAt).toLocaleString() : 'N/A'}
                  </td>
                  <td className="px-4 py-2 text-sm text-gray-900">{order.clientId || 'N/A'}</td>
                  <td className="px-4 py-2 text-sm text-gray-900">{order.details?.script || 'N/A'}</td>
                  <td className="px-4 py-2 text-sm text-gray-900">{order.orderType || 'N/A'}</td>
                  <td className="px-4 py-2 text-sm text-gray-900">
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      order.broker === 'angel' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'
                    }`}>
                      {order.broker ? order.broker.toUpperCase() : 'N/A'}
                    </span>
                  </td>
                  <td className="px-4 py-2 text-sm">
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      order.details?.status
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {order.details?.status ? 'Success' : 'Failed'}
                    </span>
                  </td>
                  <td className="px-4 py-2 text-sm text-gray-900">
                    {order.details?.orderid || 'N/A'}
                  </td>
                  <td className="px-4 py-2 text-sm text-gray-900 max-w-xs truncate">
                    {order.details?.message || 'No message'}
                  </td>
                  <td className="px-4 py-2 text-sm">
                    {order.details?.status && order.details?.orderid && (
                      <button
                        onClick={() => cancelOrder(order.details.orderid, order.broker)}
                        className="text-red-600 hover:text-red-800 text-xs"
                      >
                        Cancel
                      </button>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {orders.length === 0 && !loading && (
            <div className="text-center py-8 text-gray-500">
              No orders found matching the current filters.
            </div>
          )}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between mt-6">
            <div className="flex items-center gap-2">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Previous
              </button>

              <div className="flex items-center gap-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }

                  return (
                    <button
                      key={pageNum}
                      onClick={() => setCurrentPage(pageNum)}
                      className={`px-3 py-2 text-sm rounded-md ${
                        currentPage === pageNum
                          ? 'bg-blue-500 text-white'
                          : 'border border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}

                {totalPages > 5 && currentPage < totalPages - 2 && (
                  <>
                    <span className="px-2">...</span>
                    <button
                      onClick={() => setCurrentPage(totalPages)}
                      className="px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50"
                    >
                      {totalPages}
                    </button>
                  </>
                )}
              </div>

              <button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Next
              </button>
            </div>
          </div>
        )}

        {/* Summary */}
        <div className="mt-6 bg-gray-50 p-4 rounded-lg">
          <h3 className="font-semibold mb-2">Current Page Summary</h3>
          <div className="grid grid-cols-3 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Page Orders:</span>
              <span className="ml-2 font-semibold">{orders.length}</span>
            </div>
            <div>
              <span className="text-gray-600">Successful:</span>
              <span className="ml-2 font-semibold text-green-600">
                {orders.filter(o => o.details?.status).length}
              </span>
            </div>
            <div>
              <span className="text-gray-600">Failed:</span>
              <span className="ml-2 font-semibold text-red-600">
                {orders.filter(o => o.details && !o.details.status).length}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderManagement;
