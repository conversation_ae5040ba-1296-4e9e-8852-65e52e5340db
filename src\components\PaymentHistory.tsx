'use client';

import { useState, useEffect } from 'react';

interface PaymentRecord {
  _id: string;
  adminId: {
    _id: string;
    name: string;
    email: string;
    companyName?: string;
  };
  billingCycle: string;
  totalOrders: number;
  totalAmount: number;
  orderRate: number;
  status: string;
  paidAt: Date;
  paymentMethod?: string;
  paymentReference?: string;
  notes?: string;
}

interface PaymentData {
  payments: PaymentRecord[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
  summary: {
    totalAmount: number;
    totalOrders: number;
    count: number;
  };
}

interface PaymentHistoryProps {
  userRole: 'super-admin' | 'admin';
}

export default function PaymentHistory({ userRole }: PaymentHistoryProps) {
  const [paymentData, setPaymentData] = useState<PaymentData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filters, setFilters] = useState({
    adminId: '',
    billingCycle: '',
    page: 1
  });

  const fetchPaymentHistory = async () => {
    try {
      setLoading(true);
      setError('');

      const params = new URLSearchParams({
        page: filters.page.toString(),
        limit: '20',
        ...Object.fromEntries(Object.entries(filters).filter(([key, value]) => key !== 'page' && value))
      });

      const endpoint = userRole === 'super-admin'
        ? `/api/super-admin/billing/payment?${params}`
        : `/api/admin/payment-history?${params}`;

      console.log(`🔄 Fetching payment history from: ${endpoint}`);

      const response = await fetch(endpoint, {
        credentials: 'include'
      });

      console.log(`📡 Payment history API response status: ${response.status}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`❌ Payment history API error: ${response.status} - ${errorText}`);
        throw new Error(`Failed to fetch payment history: ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ Payment history data received:', data);

      if (data.success) {
        setPaymentData(data.data);
      } else {
        console.error('❌ Payment history API returned error:', data.error);
        setError(data.error || 'Failed to load payment history');
        setPaymentData(null);
      }
    } catch (error) {
      console.error('💥 Error fetching payment history:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch payment history');
      setPaymentData(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPaymentHistory();
  }, [filters]);

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1 // Reset to first page when filters change
    }));
  };

  const handlePageChange = (newPage: number) => {
    setFilters(prev => ({ ...prev, page: newPage }));
  };

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const exportPaymentHistory = async () => {
    try {
      const params = new URLSearchParams({
        export: 'true',
        ...Object.fromEntries(Object.entries(filters).filter(([key, value]) => key !== 'page' && value))
      });

      const endpoint = userRole === 'super-admin' 
        ? `/api/super-admin/billing/payment/export?${params}`
        : `/api/admin/payment-history/export?${params}`;

      const response = await fetch(endpoint, {
        credentials: 'include'
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `payment-history-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        alert('Failed to export payment history');
      }
    } catch (error) {
      console.error('Export error:', error);
      alert('Failed to export payment history');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">Payment History</h2>
        <button
          onClick={exportPaymentHistory}
          className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 flex items-center space-x-2"
        >
          <span>📊</span>
          <span>Export CSV</span>
        </button>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-red-700">{error}</div>
        </div>
      )}

      {/* Summary Cards */}
      {paymentData?.summary && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-sm text-gray-600">Total Payments</div>
            <div className="text-2xl font-bold text-gray-900">{paymentData.summary.count}</div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-sm text-gray-600">Total Amount Collected</div>
            <div className="text-2xl font-bold text-green-600">₹{paymentData.summary.totalAmount.toLocaleString()}</div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-sm text-gray-600">Total Orders Paid</div>
            <div className="text-2xl font-bold text-blue-600">{paymentData.summary.totalOrders}</div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {userRole === 'super-admin' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Admin</label>
              <input
                type="text"
                value={filters.adminId}
                onChange={(e) => handleFilterChange('adminId', e.target.value)}
                placeholder="Admin ID or name"
                className="w-full border border-gray-300 rounded-md px-3 py-2"
              />
            </div>
          )}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Billing Cycle</label>
            <input
              type="month"
              value={filters.billingCycle}
              onChange={(e) => handleFilterChange('billingCycle', e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2"
            />
          </div>
          <div className="flex items-end">
            <button
              onClick={() => setFilters({ adminId: '', billingCycle: '', page: 1 })}
              className="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600"
            >
              Clear Filters
            </button>
          </div>
        </div>
      </div>

      {/* Payment History Table */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {userRole === 'super-admin' && (
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Admin
                  </th>
                )}
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Billing Cycle
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Orders
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Payment Method
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Reference
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Paid Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Notes
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {paymentData?.payments && paymentData.payments.length > 0 ? (
                paymentData.payments.map((payment) => (
                  <tr key={payment._id}>
                    {userRole === 'super-admin' && (
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {payment.adminId?.name || 'N/A'}
                          </div>
                          <div className="text-sm text-gray-500">
                            {payment.adminId?.email || 'N/A'}
                          </div>
                        </div>
                      </td>
                    )}
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {payment.billingCycle}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">
                      ₹{payment.totalAmount.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {payment.totalOrders}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {payment.paymentMethod || 'N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {payment.paymentReference || 'N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatDate(payment.paidAt)}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                      {payment.notes || 'N/A'}
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={userRole === 'super-admin' ? 8 : 7} className="px-6 py-4 text-center text-gray-500">
                    No payment history found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {paymentData?.pagination && paymentData.pagination.pages > 1 && (
          <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => handlePageChange(Math.max(1, filters.page - 1))}
                disabled={filters.page <= 1}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Previous
              </button>
              <button
                onClick={() => handlePageChange(Math.min(paymentData.pagination.pages, filters.page + 1))}
                disabled={filters.page >= paymentData.pagination.pages}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Next
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Showing{' '}
                  <span className="font-medium">
                    {(filters.page - 1) * paymentData.pagination.limit + 1}
                  </span>{' '}
                  to{' '}
                  <span className="font-medium">
                    {Math.min(filters.page * paymentData.pagination.limit, paymentData.pagination.total)}
                  </span>{' '}
                  of{' '}
                  <span className="font-medium">{paymentData.pagination.total}</span> results
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  <button
                    onClick={() => handlePageChange(Math.max(1, filters.page - 1))}
                    disabled={filters.page <= 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    Previous
                  </button>
                  {Array.from({ length: Math.min(5, paymentData.pagination.pages) }, (_, i) => {
                    const page = i + 1;
                    return (
                      <button
                        key={page}
                        onClick={() => handlePageChange(page)}
                        className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                          page === filters.page
                            ? 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600'
                            : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                        }`}
                      >
                        {page}
                      </button>
                    );
                  })}
                  <button
                    onClick={() => handlePageChange(Math.min(paymentData.pagination.pages, filters.page + 1))}
                    disabled={filters.page >= paymentData.pagination.pages}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    Next
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
