import React from "react";

interface StatsProps {
  stats: {
    instrumentCount: number;
    updateRate: number;
    latency: number;
    lastUpdate: string;
  };
}

export default function Stats({ stats }: StatsProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <div className="bg-white rounded-lg shadow p-4">
        <div className="text-sm text-gray-500">Instruments</div>
        <div className="text-2xl font-semibold text-blue-600">
          {stats.instrumentCount}
        </div>
      </div>
      <div className="bg-white rounded-lg shadow p-4">
        <div className="text-sm text-gray-500">Updates/sec</div>
        <div className="text-2xl font-semibold text-green-600">
          {stats.updateRate}
        </div>
      </div>
      <div className="bg-white rounded-lg shadow p-4">
        <div className="text-sm text-gray-500">Latency</div>
        <div className="text-2xl font-semibold text-purple-600">
          {stats.latency}ms
        </div>
      </div>
      <div className="bg-white rounded-lg shadow p-4">
        <div className="text-sm text-gray-500">Last Update</div>
        <div className="text-2xl font-semibold text-gray-600">
          {stats.lastUpdate}
        </div>
      </div>
    </div>
  );
}
