'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import io, { Socket } from 'socket.io-client';
import BatchUserSelection from './BatchUserSelection';

interface MarketData {
  securityId: string;
  ticker: string;
  ltp: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  close: number;
}

interface ClientAccount {
  clientCode: string;
  userId: string;
  type: 'ANGEL' | 'MOTILAL' | 'DHAN' | 'OTHER';
  isActive: boolean;
  name?: string;
}

interface UserBatch {
  id: string;
  name: string;
  description: string;
  accounts: Array<{
    id: string;
    clientCode: string;
    broker: 'angel' | 'motilal';
    isActive: boolean;
    clientName?: string;
    capital?: number;
  }>;
  createdAt: Date;
  isActive: boolean;
}

export default function TradingDashboard() {
  const router = useRouter();
  const [socket, setSocket] = useState<Socket | null>(null);
  const [marketData, setMarketData] = useState<Map<string, MarketData>>(new Map());
  const [clientAccounts, setClientAccounts] = useState<ClientAccount[]>([]);
  const [selectedClient, setSelectedClient] = useState<string>('');
  const [isConnected, setIsConnected] = useState(false);
  const [showBuyModal, setShowBuyModal] = useState(false);
  const [selectedInstrument, setSelectedInstrument] = useState<MarketData | null>(null);
  const [isBatchModalOpen, setIsBatchModalOpen] = useState(false);
  const [userBatches, setUserBatches] = useState<UserBatch[]>([]);
  const [orderForm, setOrderForm] = useState({
    quantity: '',
    price: '',
    productType: 'INTRADAY',
    validity: 'DAY'
  });

  // Fetch client accounts
  const fetchClientAccounts = useCallback(async () => {
    try {
      const response = await fetch('/api/client-accounts');
      const data = await response.json();

      if (data.success) {
        const allAccounts: ClientAccount[] = [
          ...data.angelAccounts.map((acc: any) => ({ ...acc, type: 'ANGEL' as const })),
          ...data.motilalAccounts.map((acc: any) => ({ ...acc, type: 'MOTILAL' as const })),
          ...data.dhanAccounts.map((acc: any) => ({ ...acc, type: 'DHAN' as const })),
          ...data.otherAccounts.map((acc: any) => ({ ...acc, type: 'OTHER' as const }))
        ];
        setClientAccounts(allAccounts);
      }
    } catch (error) {
      console.error('Error fetching client accounts:', error);
    }
  }, []);

  // Fetch user batches
  const fetchUserBatches = useCallback(async () => {
    try {
      const response = await fetch('/api/batches', { credentials: 'include' });
      const data = await response.json();

      if (data.success) {
        setUserBatches(data.batches);
      }
    } catch (error) {
      console.error('Error fetching user batches:', error);
    }
  }, []);

  // Initialize socket connection
  useEffect(() => {
    const newSocket = io(process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:3002', {
      transports: ['websocket', 'polling'],
      timeout: 20000,
      forceNew: true
    });

    newSocket.on('connect', () => {
      console.log('Connected to market data server');
      setIsConnected(true);
    });

    newSocket.on('disconnect', () => {
      console.log('Disconnected from market data server');
      setIsConnected(false);
    });

    newSocket.on('marketData', (data: MarketData[]) => {
      setMarketData(prev => {
        const newMap = new Map(prev);
        data.forEach(item => {
          newMap.set(item.securityId, item);
        });
        return newMap;
      });
    });

    newSocket.on('initialData', (data: MarketData[]) => {
      const newMap = new Map<string, MarketData>();
      data.forEach(item => {
        newMap.set(item.securityId, item);
      });
      setMarketData(newMap);
    });

    setSocket(newSocket);
    fetchClientAccounts();
    fetchUserBatches();

    return () => {
      newSocket.close();
    };
  }, [fetchClientAccounts, fetchUserBatches]);

  // Handle buy order
  const handleBuyOrder = useCallback(async () => {
    if (!selectedInstrument || !selectedClient) return;

    try {
      const response = await fetch('/api/orders/place', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          clientCode: selectedClient,
          securityId: selectedInstrument.securityId,
          ticker: selectedInstrument.ticker,
          quantity: parseInt(orderForm.quantity),
          price: parseFloat(orderForm.price),
          productType: orderForm.productType,
          validity: orderForm.validity,
          orderType: 'BUY'
        }),
      });

      const result = await response.json();
      
      if (result.success) {
        alert('Order placed successfully!');
        setShowBuyModal(false);
        setOrderForm({ quantity: '', price: '', productType: 'INTRADAY', validity: 'DAY' });
      } else {
        alert(`Order failed: ${result.error}`);
      }
    } catch (error) {
      console.error('Error placing order:', error);
      alert('Failed to place order');
    }
  }, [selectedInstrument, selectedClient, orderForm]);

  // Handle batch creation
  const handleBatchCreated = useCallback((batch: UserBatch) => {
    setUserBatches(prev => [...prev, batch]);
    console.log('New batch created:', batch);
  }, []);

  // Memoized market data array
  const marketDataArray = useMemo(() => {
    return Array.from(marketData.values()).sort((a, b) => a.ticker.localeCompare(b.ticker));
  }, [marketData]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <h1 className="text-2xl font-bold text-gray-900">Trading Dashboard</h1>
            <div className="flex items-center space-x-4">
              <div className={`flex items-center space-x-2 ${isConnected ? 'text-green-600' : 'text-red-600'}`}>
                <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
                <span className="text-sm font-medium">
                  {isConnected ? 'Connected' : 'Disconnected'}
                </span>
              </div>
              <button
                onClick={() => router.push('/admin/dashboard')}
                className="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600"
              >
                Admin Panel
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Client Selection */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <h2 className="text-lg font-semibold mb-4">Client Selection</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Client Code
              </label>
              <select
                value={selectedClient}
                onChange={(e) => setSelectedClient(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select Client Code</option>
                <option value="all-motilal">🏢 All Motilal Accounts</option>
                <option value="all-angel">👼 All Angel Accounts</option>
                <option value="all-accounts">🌐 All Accounts</option>
                {clientAccounts.map((account) => (
                  <option key={account.clientCode} value={account.clientCode}>
                    {account.clientCode} ({account.type})
                  </option>
                ))}
              </select>
            </div>
            <div className="flex items-end space-x-3">
              <button
                onClick={() => setIsBatchModalOpen(true)}
                className="bg-purple-500 text-white px-4 py-2 rounded-md hover:bg-purple-600 flex items-center space-x-2"
              >
                <span>📦</span>
                <span>Manage Batches</span>
              </button>
              <button
                onClick={() => window.location.reload()}
                className="bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600"
              >
                Refresh Data
              </button>
            </div>
          </div>
        </div>

        {/* Market Data Table */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold">Market Data</h2>
            <p className="text-sm text-gray-600">
              Showing {marketDataArray.length} instruments
            </p>
          </div>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Symbol
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    LTP
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Change
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Change %
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Volume
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {marketDataArray.map((data) => (
                  <tr key={data.securityId} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {data.ticker}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      ₹{data.ltp?.toFixed(2) || '0.00'}
                    </td>
                    <td className={`px-6 py-4 whitespace-nowrap text-sm ${
                      data.change >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {data.change >= 0 ? '+' : ''}{data.change?.toFixed(2) || '0.00'}
                    </td>
                    <td className={`px-6 py-4 whitespace-nowrap text-sm ${
                      data.changePercent >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {data.changePercent >= 0 ? '+' : ''}{data.changePercent?.toFixed(2) || '0.00'}%
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {data.volume?.toLocaleString() || '0'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm space-x-2">
                      <button
                        onClick={() => {
                          setSelectedInstrument(data);
                          setOrderForm(prev => ({ ...prev, price: data.ltp?.toString() || '' }));
                          setShowBuyModal(true);
                        }}
                        disabled={!selectedClient}
                        className="bg-green-500 text-white px-3 py-1 rounded text-xs hover:bg-green-600 disabled:bg-gray-300"
                      >
                        Buy
                      </button>
                      <button
                        disabled={!selectedClient}
                        className="bg-red-500 text-white px-3 py-1 rounded text-xs hover:bg-red-600 disabled:bg-gray-300"
                      >
                        Sell
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </main>

      {/* Buy Order Modal */}
      {showBuyModal && selectedInstrument && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">BUY Order</h3>
              <button
                onClick={() => setShowBuyModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Script: {selectedInstrument.ticker}
                </label>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  LTP: ₹{selectedInstrument.ltp?.toFixed(2)}
                </label>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Client Code
                </label>
                <select
                  value={selectedClient}
                  onChange={(e) => setSelectedClient(e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                >
                  <option value="">Select Client Code</option>
                  {clientAccounts.map((account) => (
                    <option key={account.clientCode} value={account.clientCode}>
                      {account.clientCode} ({account.type})
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Quantity
                </label>
                <input
                  type="number"
                  value={orderForm.quantity}
                  onChange={(e) => setOrderForm(prev => ({ ...prev, quantity: e.target.value }))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  placeholder="Enter quantity"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Price
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={orderForm.price}
                  onChange={(e) => setOrderForm(prev => ({ ...prev, price: e.target.value }))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  placeholder="Enter price"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Product Type
                </label>
                <select
                  value={orderForm.productType}
                  onChange={(e) => setOrderForm(prev => ({ ...prev, productType: e.target.value }))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                >
                  <option value="INTRADAY">Intraday</option>
                  <option value="DELIVERY">Delivery</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Validity
                </label>
                <select
                  value={orderForm.validity}
                  onChange={(e) => setOrderForm(prev => ({ ...prev, validity: e.target.value }))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                >
                  <option value="DAY">Day</option>
                  <option value="IOC">IOC</option>
                </select>
              </div>
            </div>
            
            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => setShowBuyModal(false)}
                className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400"
              >
                Cancel
              </button>
              <button
                onClick={handleBuyOrder}
                disabled={!orderForm.quantity || !orderForm.price || !selectedClient}
                className="flex-1 bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-gray-300"
              >
                Place BUY Order
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Batch Management Modal */}
      <BatchUserSelection
        isOpen={isBatchModalOpen}
        onClose={() => setIsBatchModalOpen(false)}
        onBatchCreated={handleBatchCreated}
        existingBatches={userBatches}
      />
    </div>
  );
}