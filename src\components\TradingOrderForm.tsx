"use client";

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useToast } from './Toast';
import { MarketData, OrderRequest, ApiResponse, BrokerAccount } from '@/types/api';
import { isValidOrderType, isValidProductType, safeParseFloat, safeParseInt } from '@/lib/typeGuards';

// Enhanced interfaces with better typing
interface TradingOrderFormProps {
  isOpen: boolean;
  onClose: () => void;
  orderType: 'BUY' | 'SELL';
  scriptData: MarketData;
  bulkOrderContext?: 'all-accounts' | 'angel-accounts' | 'motilal-accounts' | null;
  onOrderSubmit?: (order: OrderRequest) => void;
  onBulkOrderSubmit?: (orders: OrderRequest[]) => void;
}

interface ClientAccount {
  id: string;
  clientCode: string;
  broker: 'angel' | 'motilal' | 'all';
  isActive: boolean;
}

interface OrderFormData {
  quantity: string;
  price: string;
  productType: 'INTRADAY' | 'DELIVERY' | 'CNC' | 'MIS';
  validity: 'DAY' | 'IOC' | 'GTD';
  orderType: 'LIMIT' | 'MARKET';
}

interface FormErrors {
  quantity?: string;
  price?: string;
  client?: string;
  general?: string;
}

interface OrderSubmissionResult {
  success: boolean;
  orderId?: string;
  error?: string;
}

const TradingOrderForm: React.FC<TradingOrderFormProps> = ({
  isOpen,
  onClose,
  orderType,
  scriptData,
  bulkOrderContext = null,
  onOrderSubmit,
  onBulkOrderSubmit
}) => {
  const { showSuccess, showError, showWarning, showInfo } = useToast();

  // State with proper typing
  const [clientAccounts, setClientAccounts] = useState<ClientAccount[]>([]);
  const [selectedClient, setSelectedClient] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [errors, setErrors] = useState<FormErrors>({});

  // Memoized initial order data
  const initialOrderData = useMemo((): OrderFormData => ({
    quantity: '',
    price: safeParseFloat(scriptData.ltp, 0).toFixed(2),
    productType: 'INTRADAY',
    validity: 'DAY',
    orderType: 'LIMIT'
  }), [scriptData.ltp]);

  const [orderData, setOrderData] = useState<OrderFormData>(initialOrderData);

  // Fetch user's client accounts
  useEffect(() => {
    const fetchClientAccounts = async () => {
      try {
        console.log('Fetching client accounts...', { bulkOrderContext });

        // If we have a bulk order context, create special bulk accounts
        if (bulkOrderContext) {
          const bulkAccounts: ClientAccount[] = [];

          switch (bulkOrderContext) {
            case 'all-accounts':
              bulkAccounts.push({
                id: 'all-accounts',
                clientCode: 'all-accounts',
                broker: 'all',
                isActive: true
              });
              break;
            case 'angel-accounts':
              bulkAccounts.push({
                id: 'all-angel',
                clientCode: 'all-angel',
                broker: 'angel',
                isActive: true
              });
              break;
            case 'motilal-accounts':
              bulkAccounts.push({
                id: 'all-motilal',
                clientCode: 'all-motilal',
                broker: 'motilal',
                isActive: true
              });
              break;
          }

          console.log('Setting bulk accounts:', bulkAccounts);
          console.log('Bulk order context:', bulkOrderContext);
          setClientAccounts(bulkAccounts);
          if (bulkAccounts.length > 0) {
            setSelectedClient(bulkAccounts[0].clientCode);
            console.log('Selected client set to:', bulkAccounts[0].clientCode);
          }
          return;
        }

        // Regular account fetching for individual trading
        const [angelResponse, motilalResponse] = await Promise.all([
          fetch('/api/users/angel'),
          fetch('/api/users/motilal')
        ]);

        console.log('Angel response status:', angelResponse.status);
        console.log('Motilal response status:', motilalResponse.status);

        const angelData = await angelResponse.json();
        const motilalData = await motilalResponse.json();

        console.log('Angel data:', angelData);
        console.log('Motilal data:', motilalData);

        // Handle API response format - check if it's an array or object with users property
        const angelAccounts = Array.isArray(angelData) ? angelData : (angelData.users || []);
        const motilalAccounts = Array.isArray(motilalData) ? motilalData : (motilalData.users || []);

        console.log('Processed angel accounts:', angelAccounts);
        console.log('Processed motilal accounts:', motilalAccounts);

        const allAccounts: ClientAccount[] = [
          ...angelAccounts.map((acc: any) => ({
            id: acc._id || acc.id,
            clientCode: acc.clientCode || acc.userId || acc.clientName || `ANG_${acc._id}`,
            broker: 'angel' as const,
            isActive: acc.isActive !== false
          })),
          ...motilalAccounts.map((acc: any) => ({
            id: acc._id || acc.id,
            clientCode: acc.clientCode || acc.userId || acc.clientName || `MOT_${acc._id}`,
            broker: 'motilal' as const,
            isActive: acc.isActive !== false
          }))
        ];

        // Add bulk order options to the accounts array
        const bulkOptions: ClientAccount[] = [
          {
            id: 'bulk-all',
            clientCode: 'all-accounts',
            broker: 'all' as any,
            isActive: true
          },
          {
            id: 'bulk-angel',
            clientCode: 'all-angel',
            broker: 'angel' as const,
            isActive: true
          },
          {
            id: 'bulk-motilal',
            clientCode: 'all-motilal',
            broker: 'motilal' as const,
            isActive: true
          }
        ];

        const finalAccounts = [...bulkOptions, ...allAccounts];

        console.log('Final accounts with bulk options:', finalAccounts);
        console.log('Bulk options added:', bulkOptions);
        console.log('Angel accounts raw:', angelAccounts);
        console.log('Motilal accounts raw:', motilalAccounts);
        setClientAccounts(finalAccounts);

        // Set default client selection if none selected
        if (!selectedClient && finalAccounts.length > 0) {
          setSelectedClient(finalAccounts[0].clientCode);
        }
      } catch (error) {
        console.error('Error fetching client accounts:', error);
        // Add some dummy accounts for testing
        const dummyAccounts: ClientAccount[] = [
          { id: '1', clientCode: 'ANG001', broker: 'angel', isActive: true },
          { id: '2', clientCode: 'ANG002', broker: 'angel', isActive: true },
          { id: '3', clientCode: 'MOT001', broker: 'motilal', isActive: true },
          { id: '4', clientCode: 'MOT002', broker: 'motilal', isActive: true },
        ];
        setClientAccounts(dummyAccounts);
      }
    };

    if (isOpen) {
      fetchClientAccounts();
    }
  }, [isOpen, bulkOrderContext]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (!isOpen) return;
      
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [isOpen, onClose]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Immediate UI feedback
    setIsSubmitting(true);
    showInfo('Order Status', 'Placing order...');

    try {
      // Pre-calculate values for speed
      const quantity = parseInt(orderData.quantity);
      const price = orderData.orderType !== 'MARKET' ? parseFloat(orderData.price) : null;

      // Check if this is a bulk order
      const isBulkOrder = selectedClient === 'all-accounts' || selectedClient === 'all-angel' || selectedClient === 'all-motilal';

      if (isBulkOrder) {
        console.log(`🏦 Processing bulk order for: ${selectedClient}`);

        // Use bulk API for bulk orders
        const bulkPayload = {
          clientCode: selectedClient,
          orderType: orderType,
          quantity: quantity,
          price: price,
          productType: orderData.productType,
          validity: orderData.validity,
          symbolToken: scriptData.securityId,
          tradingSymbol: scriptData.ticker
        };

        const response = await fetch('/api/orders/bulk', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify(bulkPayload),
        });

        const result = await response.json();

        if (response.ok && result.success) {
          showSuccess('Order Status', `Bulk order placed successfully! ${result.message}`);
          onClose();
        } else {
          showError('Order Failed', result.message || 'Failed to place bulk order');
        }
        return;
      }

      const orderPayload = {
        clientCode: selectedClient,
        tradingSymbol: scriptData.ticker,
        symbolToken: scriptData.securityId || scriptData.ticker,
        orderType: orderType,
        quantity,
        price,
        productType: orderData.productType,
        validity: orderData.validity,
        exchange: scriptData.exchange
      };

      // Handle different client selections with optimized requests
      if (selectedClient === 'all-accounts') {
        // Fast parallel order placement
        const angelPayload = { ...orderPayload, clientCode: 'all-angel' };
        const motilalPayload = { ...orderPayload, clientCode: 'all-motilal' };

        // Pre-build headers for reuse
        const headers = {
          'Content-Type': 'application/json',
        };

        const [angelResponse, motilalResponse] = await Promise.allSettled([
          fetch('/api/orders/angel', {
            method: 'POST',
            headers,
            credentials: 'include',
            body: JSON.stringify(angelPayload),
          }),
          fetch('/api/orders/motilal', {
            method: 'POST',
            headers,
            credentials: 'include',
            body: JSON.stringify(motilalPayload),
          })
        ]);

        // Process combined results with proper error handling
        const angelResult = angelResponse.status === 'fulfilled' && angelResponse.value.ok
          ? await angelResponse.value.json()
          : { success: false, message: 'Angel API failed', results: [] };

        const motilalResult = motilalResponse.status === 'fulfilled' && motilalResponse.value.ok
          ? await motilalResponse.value.json()
          : { success: false, message: 'Motilal API failed', results: [] };

        console.log('📊 Angel API Result:', angelResult);
        console.log('📊 Motilal API Result:', motilalResult);

        const combinedResults = [
          ...(angelResult.results || []),
          ...(motilalResult.results || [])
        ];

        const successCount = combinedResults.filter(r => r.status === true).length;
        const totalCount = combinedResults.length;
        const failedCount = totalCount - successCount;

        console.log(`📈 Order Results: ${successCount}/${totalCount} successful`);
        console.log('📋 Combined Results:', combinedResults);

        if (totalCount === 0) {
          showError(
            'Order Failed',
            'No accounts available for order placement'
          );
        } else if (failedCount === 0) {
          showSuccess(
            'Order Placed Successfully!',
            `${orderType} order placed on all ${totalCount} account(s)`
          );
        } else if (successCount > 0) {
          showWarning(
            'Partial Success',
            `${successCount}/${totalCount} orders placed successfully`
          );
        } else {
          showError(
            'Order Failed',
            'Failed to place orders on any accounts'
          );
        }

        onClose();
        return;
      }

      // Determine which broker API to call based on selected client
      const selectedAccount = clientAccounts.find(acc => acc.clientCode === selectedClient);
      let brokerEndpoint = '/api/orders/angel'; // Default to Angel

      // Determine broker based on client code
      if (selectedClient === 'all-motilal' || selectedAccount?.broker === 'motilal') {
        brokerEndpoint = '/api/orders/motilal';
      } else if (selectedClient === 'all-angel' || selectedAccount?.broker === 'angel') {
        brokerEndpoint = '/api/orders/angel';
      }

      console.log('Placing order:', orderPayload, 'to endpoint:', brokerEndpoint);

      const response = await fetch(brokerEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(orderPayload),
      });

      if (response.ok) {
        const result = await response.json();
        console.log('Order response:', result);

        console.log('📊 Single Broker API Result:', result);

        if (result.success && result.results) {
          const clientCount = result.results.length;
          const successCount = result.results.filter((r: any) => r.status === true).length;
          const failedCount = clientCount - successCount;

          console.log(`📈 Single Broker Results: ${successCount}/${clientCount} successful`);

          if (failedCount === 0) {
            showSuccess(
              'Order Placed Successfully!',
              `${orderType} order placed for ${clientCount} client(s)`
            );
          } else if (successCount > 0) {
            showWarning(
              'Partial Success',
              `${successCount}/${clientCount} orders placed successfully`
            );
          } else {
            showError(
              'Order Failed',
              'Failed to place orders on any accounts'
            );
          }

          // Show detailed results for each client
          if (result.results && result.results.length > 1) {
            result.results.forEach((clientResult: any) => {
              if (clientResult.status) {
                showInfo(
                  `${clientResult.clientName || clientResult.clientId}`,
                  `Order ID: ${clientResult.orderid || 'N/A'}`
                );
              } else {
                showError(
                  `${clientResult.clientName || clientResult.clientId}`,
                  clientResult.message || 'Order failed'
                );
              }
            });
          }

          onClose();
        } else {
          showError(
            'Order Failed',
            result.message || 'Unknown error occurred'
          );
        }
      } else {
        const error = await response.json();
        showError(
          'Server Error',
          error.message || 'Failed to communicate with server'
        );
      }
    } catch (error) {
      console.error('Error placing order:', error);
      showError(
        'Network Error',
        'Failed to place order. Please check your connection and try again.'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const getClientOptions = () => {
    const options: Array<{value: string, label: string, broker: string, uniqueKey: string}> = [];
    const seenValues = new Set<string>();

    console.log('Getting client options from accounts:', clientAccounts);

    // Process all accounts and separate bulk options from individual accounts
    if (clientAccounts && Array.isArray(clientAccounts)) {
      clientAccounts.forEach((account, index) => {
        if (account && account.isActive && account.clientCode && account.broker) {
          // Handle bulk order options with better labels
          if (account.clientCode === 'all-accounts') {
            options.push({
              value: account.clientCode,
              label: '🏦 ALL BROKER ACCOUNTS (Bulk Order)',
              broker: account.broker,
              uniqueKey: `bulk-all-${index}`
            });
            return;
          }

          if (account.clientCode === 'all-angel') {
            options.push({
              value: account.clientCode,
              label: '🔥 ALL ANGEL ACCOUNTS (Bulk Order)',
              broker: account.broker,
              uniqueKey: `bulk-angel-${index}`
            });
            return;
          }

          if (account.clientCode === 'all-motilal') {
            options.push({
              value: account.clientCode,
              label: '💼 ALL MOTILAL ACCOUNTS (Bulk Order)',
              broker: account.broker,
              uniqueKey: `bulk-motilal-${index}`
            });
            return;
          }

          // Handle individual accounts
          const label = `${account.clientCode} (${account.broker.toUpperCase()})`;
          const uniqueKey = `${account.broker}-${account.clientCode}-${index}`;
          const optionValue = account.clientCode;

          // Only add if we haven't seen this exact combination before
          if (!seenValues.has(uniqueKey)) {
            options.push({
              value: optionValue,
              label: label,
              broker: account.broker,
              uniqueKey: uniqueKey
            });
            seenValues.add(uniqueKey);
          }
        }
      });
    }

    console.log('Generated client options:', options);
    return options;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
        <div className={`flex items-center justify-between mb-4 p-3 rounded-lg ${
          orderType === 'BUY' ? 'bg-blue-100 text-blue-800' : 'bg-red-100 text-red-800'
        }`}>
          <h2 className="text-xl font-bold">{orderType} Order</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 text-xl font-bold"
          >
            ×
          </button>
        </div>

        {/* Main Content - Horizontal Layout */}
        <div className="flex gap-6">
          {/* Left Side - Script Details */}
          <div className="flex-1">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-semibold mb-3">Script Details</h3>
              <div className="grid grid-cols-2 gap-3 text-sm">
                <div><strong>Script:</strong> {scriptData.ticker}</div>
                <div><strong>Exchange:</strong> {scriptData.exchange}</div>
                <div><strong>LTP:</strong> ₹{scriptData.ltp.toFixed(2)}</div>
                <div className={`${scriptData.change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  <strong>Change:</strong> {scriptData.change >= 0 ? '+' : ''}{scriptData.change.toFixed(2)} ({scriptData.changePercent.toFixed(2)}%)
                </div>
                <div><strong>Volume:</strong> {scriptData.volume.toLocaleString()}</div>
                <div><strong>High:</strong> ₹{scriptData.high.toFixed(2)}</div>
                <div><strong>Low:</strong> ₹{scriptData.low.toFixed(2)}</div>
                <div><strong>Open:</strong> ₹{scriptData.open.toFixed(2)}</div>
              </div>
            </div>
          </div>

          {/* Right Side - Order Form */}
          <div className="flex-1">
            <form onSubmit={handleSubmit} className="space-y-4">
          {/* Client Code Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Client Code
            </label>
            <select
              value={selectedClient}
              onChange={(e) => setSelectedClient(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            >
              <option value="">Select Client Code</option>
              {getClientOptions().map((option) => (
                <option key={option.uniqueKey} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Order Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Order Type
            </label>
            <select
              value={orderData.orderType}
              onChange={(e) => setOrderData({...orderData, orderType: e.target.value as 'LIMIT' | 'MARKET'})}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="LIMIT">Limit</option>
              <option value="MARKET">Market</option>
              <option value="SL">Stop Loss</option>
              <option value="SL-M">Stop Loss Market</option>
            </select>
          </div>

          {/* Quantity */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Quantity
            </label>
            <input
              type="number"
              value={orderData.quantity}
              onChange={(e) => setOrderData({...orderData, quantity: e.target.value})}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter quantity"
              required
              min="1"
            />
          </div>

          {/* Price */}
          {orderData.orderType !== 'MARKET' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Price
              </label>
              <input
                type="number"
                step="0.05"
                value={orderData.price}
                onChange={(e) => setOrderData({...orderData, price: e.target.value})}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter price"
                required
              />
            </div>
          )}

          {/* Product Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Product Type
            </label>
            <select
              value={orderData.productType}
              onChange={(e) => setOrderData({...orderData, productType: e.target.value as 'INTRADAY' | 'DELIVERY' | 'CNC' | 'MIS'})}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="INTRADAY">Intraday</option>
              <option value="DELIVERY">Delivery</option>
              <option value="CARRYFORWARD">Carry Forward</option>
            </select>
          </div>

          {/* Validity */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Validity
            </label>
            <select
              value={orderData.validity}
              onChange={(e) => setOrderData({...orderData, validity: e.target.value as 'DAY' | 'IOC' | 'GTD'})}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="DAY">Day</option>
              <option value="IOC">IOC</option>
              <option value="GTD">GTD</option>
            </select>
          </div>

          {/* Submit Buttons */}
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:ring-2 focus:ring-gray-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              className={`flex-1 px-4 py-2 text-white rounded-md focus:ring-2 ${
                orderType === 'BUY'
                  ? 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
                  : 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
              }`}
            >
              Place {orderType} Order
            </button>
          </div>
            </form>
          </div>
        </div>

        {/* Keyboard Shortcuts Info */}
        <div className="mt-4 p-2 bg-gray-100 rounded text-xs text-gray-600">
          <strong>Shortcuts:</strong> Press <kbd className="px-1 bg-white rounded">Esc</kbd> to close
        </div>
      </div>
    </div>
  );
};

export default TradingOrderForm;
