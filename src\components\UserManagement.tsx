'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import AddUserForm from './AddUserForm';

interface AngelUser {
  _id: string;
  userId: string;
  clientName: string;
  email: string;
  phoneNumber: string;
  capital: number;
  state: 'live' | 'inactive';
  createdAt: string;
}

interface MotilalUser {
  _id: string;
  userId: string;
  clientName: string;
  email: string;
  phoneNumber: string;
  capital: number;
  status: 'active' | 'inactive';
  createdAt: string;
}

export default function UserManagement() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<'angel' | 'motilal'>('angel');
  const [angelUsers, setAngelUsers] = useState<AngelUser[]>([]);
  const [motilalUsers, setMotilalUsers] = useState<MotilalUser[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showAddForm, setShowAddForm] = useState(false);

  useEffect(() => {
    if (user) {
      fetchUsers();
    }
  }, [user, activeTab]);

  const fetchUsers = async () => {
    setLoading(true);
    setError('');
    try {
      console.log(`🔄 Fetching ${activeTab} users...`);
      const response = await fetch(`/api/users/${activeTab}`, {
        credentials: 'include',
      });

      console.log(`📡 API response status: ${response.status}`);

      if (response.ok) {
        const data = await response.json();
        console.log(`✅ Received data:`, data);

        if (data.users) {
          if (activeTab === 'angel') {
            setAngelUsers(data.users);
          } else {
            setMotilalUsers(data.users);
          }
        } else {
          console.warn('⚠️ No users array in response');
          setError('No user data received from server');
        }
      } else {
        const errorText = await response.text();
        console.error(`❌ API error: ${response.status} - ${errorText}`);
        setError(`Failed to fetch users: ${response.status}`);
      }
    } catch (error) {
      console.error('💥 Error fetching users:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch users');
    } finally {
      setLoading(false);
    }
  };

  const deleteUser = async (userId: string) => {
    if (!confirm('Are you sure you want to delete this user?')) return;

    try {
      const response = await fetch(`/api/users/${activeTab}?id=${userId}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      if (response.ok) {
        fetchUsers(); // Refresh the list
      }
    } catch (error) {
      console.error('Error deleting user:', error);
    }
  };

  const toggleUserStatus = async (userId: string, currentStatus: string) => {
    const newStatus = currentStatus === 'live' || currentStatus === 'active' ? 'inactive' : 
                     activeTab === 'angel' ? 'live' : 'active';

    try {
      const response = await fetch(`/api/users/${activeTab}?id=${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          [activeTab === 'angel' ? 'state' : 'status']: newStatus
        }),
      });

      if (response.ok) {
        fetchUsers(); // Refresh the list
      }
    } catch (error) {
      console.error('Error updating user status:', error);
    }
  };

  if (!user) return null;

  const currentUsers = activeTab === 'angel' ? angelUsers : motilalUsers;

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">User Management</h2>
        <button
          onClick={() => setShowAddForm(true)}
          className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700"
        >
          Add {activeTab === 'angel' ? 'Angel' : 'Motilal'} User
        </button>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-red-700">{error}</div>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('angel')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'angel'
                ? 'border-indigo-500 text-indigo-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Angel Users ({angelUsers.length})
          </button>
          <button
            onClick={() => setActiveTab('motilal')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'motilal'
                ? 'border-indigo-500 text-indigo-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Motilal Users ({motilalUsers.length})
          </button>
        </nav>
      </div>

      {/* Users Table */}
      {loading ? (
        <div className="text-center py-8">Loading...</div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  User ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Client Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Email
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Capital
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {currentUsers.map((user) => (
                <tr key={user._id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {user.userId}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {user.clientName}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {user.email}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ₹{user.capital.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        (user as any).state === 'live' || (user as any).status === 'active'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}
                    >
                      {(user as any).state || (user as any).status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                      onClick={() => toggleUserStatus(user._id, (user as any).state || (user as any).status)}
                      className="text-indigo-600 hover:text-indigo-900 mr-4"
                    >
                      {(user as any).state === 'live' || (user as any).status === 'active' ? 'Deactivate' : 'Activate'}
                    </button>
                    <button
                      onClick={() => deleteUser(user._id)}
                      className="text-red-600 hover:text-red-900"
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {currentUsers.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              No {activeTab} users found. Add your first user to get started.
            </div>
          )}
        </div>
      )}

      {/* Add User Modal */}
      {showAddForm && (
        <AddUserForm
          type={activeTab}
          onClose={() => setShowAddForm(false)}
          onSuccess={() => {
            setShowAddForm(false);
            fetchUsers();
          }}
        />
      )}
    </div>
  );
}
