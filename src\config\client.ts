// Client-side configuration
// NOTE: All URLs are dynamically constructed from environment variables
// This ensures consistency across different environments (dev, staging, prod)

// Get backend port from environment or use default
const BACKEND_PORT = process.env.NEXT_PUBLIC_BACKEND_PORT || process.env.BACKEND_PORT || "3001";
const FRONTEND_PORT = process.env.NEXT_PUBLIC_FRONTEND_PORT || process.env.FRONTEND_PORT || "3000";

// Construct base URLs
const BASE_URL = `http://localhost:${BACKEND_PORT}`;

export const CLIENT_CONFIG = {
  // WebSocket server URL - connects to backend server
  // Used for: Real-time market data streaming
  WS_SERVER_URL: process.env.NEXT_PUBLIC_WS_SERVER_URL || BASE_URL,

  // API base URL - connects to backend REST API
  // Used for: Authentication, user management, initial data fetch
  API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || BASE_URL,

  // Frontend URL - where the React app runs
  // Used for: CORS configuration, redirects
  FRONTEND_URL: process.env.NEXT_PUBLIC_FRONTEND_URL || `http://localhost:${FRONTEND_PORT}`,

  // Socket.IO configuration
  // Optimized for reliable real-time connections
  SOCKET_CONFIG: {
    transports: ["websocket", "polling"],
    upgrade: true,
    rememberUpgrade: false,
    timeout: 20000,
    forceNew: true,
    reconnectionAttempts: 5,
    reconnectionDelay: 5000,
  },

  // Port information for reference
  PORTS: {
    BACKEND: BACKEND_PORT,
    FRONTEND: FRONTEND_PORT,
  },
} as const;
