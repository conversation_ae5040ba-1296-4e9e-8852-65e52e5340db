import dotenv from "dotenv";

dotenv.config();

export const config = {
  port: process.env.BACKEND_PORT || process.env.PORT || 3003,
  dhan: {
    wsUrl: process.env.DHAN_WS_URL || "wss://stream.dhan.co",
    clientId: process.env.DHAN_CLIENT_ID,
    accessToken: process.env.DHAN_ACCESS_TOKEN,
  },
  symbols: [
    { exchange: "NSE", symbol: "RELIANCE" },
    { exchange: "NSE", symbol: "TCS" },
    { exchange: "NSE", symbol: "INFY" },
    { exchange: "NSE", symbol: "HDFCBANK" },
    { exchange: "NSE", symbol: "ICICIBANK" },
  ],
  reconnect: {
    maxAttempts: 5,
    delay: 5000, // 5 seconds
  },
} as const;
