import { useEffect, useCallback, useState } from 'react';

interface MarketData {
  ticker: string;
  securityId: string;
  exchange: string;
  ltp: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  close: number;
}

interface UseGlobalKeyboardShortcutsProps {
  selectedScript?: MarketData | null;
  onBuyOrder?: (script: MarketData) => void;
  onSellOrder?: (script: MarketData) => void;
  onNavigateUp?: () => void;
  onNavigateDown?: () => void;
  onEscape?: () => void;
  isModalOpen?: boolean;
}

export const useGlobalKeyboardShortcuts = ({
  selectedScript,
  onBuyOrder,
  onSellOrder,
  onNavigateUp,
  onNavigateDown,
  onEscape,
  isModalOpen = false
}: UseGlobalKeyboardShortcutsProps) => {
  
  const handleKeyPress = useCallback((event: KeyboardEvent) => {
    // Don't trigger shortcuts if user is typing in an input field
    if (event.target instanceof HTMLInputElement || 
        event.target instanceof HTMLTextAreaElement || 
        event.target instanceof HTMLSelectElement) {
      return;
    }

    // Handle Escape key
    if (event.key === 'Escape' && onEscape) {
      event.preventDefault();
      onEscape();
      return;
    }

    // Don't handle other shortcuts if modal is open
    if (isModalOpen) {
      return;
    }

    // Handle buy/sell shortcuts only if a script is selected
    if (selectedScript) {
      if ((event.key === '+' || event.key === '=') && onBuyOrder) {
        event.preventDefault();
        onBuyOrder(selectedScript);
      } else if ((event.key === '-' || event.key === '_') && onSellOrder) {
        event.preventDefault();
        onSellOrder(selectedScript);
      }
    }

    // Handle navigation shortcuts
    if (event.key === 'ArrowUp' && onNavigateUp) {
      event.preventDefault();
      onNavigateUp();
    } else if (event.key === 'ArrowDown' && onNavigateDown) {
      event.preventDefault();
      onNavigateDown();
    }

    // Additional shortcuts can be added here
    // For example:
    // - 'r' for refresh
    // - 'w' for watchlist
    // - 'p' for portfolio
    // - 'o' for orders
    
  }, [selectedScript, onBuyOrder, onSellOrder, onNavigateUp, onNavigateDown, onEscape, isModalOpen]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);

  return {
    // Return any utility functions if needed
    isShortcutActive: !isModalOpen
  };
};

// Hook for managing keyboard shortcuts in trading tables
export const useTradingTableShortcuts = (
  data: MarketData[],
  onOrderPlace: (script: MarketData, orderType: 'BUY' | 'SELL') => void
) => {
  const [selectedRowIndex, setSelectedRowIndex] = useState<number>(-1);

  const handleNavigateUp = useCallback(() => {
    setSelectedRowIndex(prev => Math.max(0, prev - 1));
  }, []);

  const handleNavigateDown = useCallback(() => {
    setSelectedRowIndex(prev => Math.min(data.length - 1, prev + 1));
  }, [data.length]);

  const handleBuyOrder = useCallback((script: MarketData) => {
    onOrderPlace(script, 'BUY');
  }, [onOrderPlace]);

  const handleSellOrder = useCallback((script: MarketData) => {
    onOrderPlace(script, 'SELL');
  }, [onOrderPlace]);

  const selectedScript = selectedRowIndex >= 0 && selectedRowIndex < data.length 
    ? data[selectedRowIndex] 
    : null;

  useGlobalKeyboardShortcuts({
    selectedScript,
    onBuyOrder: handleBuyOrder,
    onSellOrder: handleSellOrder,
    onNavigateUp: handleNavigateUp,
    onNavigateDown: handleNavigateDown
  });

  return {
    selectedRowIndex,
    setSelectedRowIndex,
    selectedScript
  };
};


