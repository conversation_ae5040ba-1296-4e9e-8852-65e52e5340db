import ActivityLog from '@/models/ActivityLog';
import connectDB from '@/lib/mongodb';

interface LogActivityParams {
  superAdminId: string;
  adminId?: string;
  userId?: string;
  action: string;
  category: 'admin_management' | 'user_management' | 'billing' | 'orders' | 'system' | 'authentication';
  description: string;
  details?: any;
  ipAddress?: string;
  userAgent?: string;
  severity?: 'low' | 'medium' | 'high' | 'critical';
  status?: 'success' | 'failed' | 'pending';
}

export async function logActivity(params: LogActivityParams): Promise<void> {
  try {
    await connectDB();

    const activityLog = new ActivityLog({
      superAdminId: params.superAdminId,
      adminId: params.adminId,
      userId: params.userId,
      action: params.action,
      category: params.category,
      description: params.description,
      details: params.details,
      ipAddress: params.ipAddress,
      userAgent: params.userAgent,
      severity: params.severity || 'low',
      status: params.status || 'success',
    });

    await activityLog.save();
    console.log(`📝 Activity logged: ${params.action} - ${params.description}`);
  } catch (error) {
    console.error('❌ Error logging activity:', error);
    // Don't throw error to avoid breaking the main operation
  }
}

// Helper function to extract IP and User Agent from request
export function getRequestInfo(request: Request): { ipAddress?: string; userAgent?: string } {
  const ipAddress = request.headers.get('x-forwarded-for') || 
                   request.headers.get('x-real-ip') || 
                   'unknown';
  
  const userAgent = request.headers.get('user-agent') || 'unknown';

  return { ipAddress, userAgent };
}

// Predefined activity templates for common actions
export const ActivityTemplates = {
  // Admin Management
  ADMIN_CREATED: {
    action: 'admin_created',
    category: 'admin_management' as const,
    severity: 'medium' as const,
  },
  ADMIN_UPDATED: {
    action: 'admin_updated',
    category: 'admin_management' as const,
    severity: 'low' as const,
  },
  ADMIN_SUSPENDED: {
    action: 'admin_suspended',
    category: 'admin_management' as const,
    severity: 'high' as const,
  },
  ADMIN_BLOCKED: {
    action: 'admin_blocked',
    category: 'admin_management' as const,
    severity: 'high' as const,
  },
  ADMIN_TERMINATED: {
    action: 'admin_terminated',
    category: 'admin_management' as const,
    severity: 'critical' as const,
  },
  ADMIN_ACTIVATED: {
    action: 'admin_activated',
    category: 'admin_management' as const,
    severity: 'medium' as const,
  },
  ADMIN_RATE_UPDATED: {
    action: 'admin_rate_updated',
    category: 'admin_management' as const,
    severity: 'medium' as const,
  },

  // User Management
  USER_CREATED: {
    action: 'user_created',
    category: 'user_management' as const,
    severity: 'low' as const,
  },
  USER_UPDATED: {
    action: 'user_updated',
    category: 'user_management' as const,
    severity: 'low' as const,
  },
  USER_DELETED: {
    action: 'user_deleted',
    category: 'user_management' as const,
    severity: 'medium' as const,
  },

  // Billing
  BILLING_GENERATED: {
    action: 'billing_generated',
    category: 'billing' as const,
    severity: 'low' as const,
  },
  PAYMENT_COLLECTED: {
    action: 'payment_collected',
    category: 'billing' as const,
    severity: 'medium' as const,
  },
  BILLING_SETTLED: {
    action: 'billing_settled',
    category: 'billing' as const,
    severity: 'medium' as const,
  },
  BILLING_CLEARED: {
    action: 'billing_cleared',
    category: 'billing' as const,
    severity: 'high' as const,
  },

  // Orders
  ORDER_CLEANUP: {
    action: 'order_cleanup',
    category: 'orders' as const,
    severity: 'medium' as const,
  },
  BATCH_ORDER_EXECUTED: {
    action: 'batch_order_executed',
    category: 'orders' as const,
    severity: 'low' as const,
  },

  // System
  SYSTEM_BACKUP: {
    action: 'system_backup',
    category: 'system' as const,
    severity: 'low' as const,
  },
  SYSTEM_MAINTENANCE: {
    action: 'system_maintenance',
    category: 'system' as const,
    severity: 'medium' as const,
  },

  // Authentication
  LOGIN_SUCCESS: {
    action: 'login_success',
    category: 'authentication' as const,
    severity: 'low' as const,
  },
  LOGIN_FAILED: {
    action: 'login_failed',
    category: 'authentication' as const,
    severity: 'medium' as const,
    status: 'failed' as const,
  },
  LOGOUT: {
    action: 'logout',
    category: 'authentication' as const,
    severity: 'low' as const,
  },
};

// Helper function to log with template
export async function logActivityWithTemplate(
  template: typeof ActivityTemplates[keyof typeof ActivityTemplates],
  params: Omit<LogActivityParams, 'action' | 'category' | 'severity' | 'status'> & {
    severity?: 'low' | 'medium' | 'high' | 'critical';
    status?: 'success' | 'failed' | 'pending';
  }
): Promise<void> {
  await logActivity({
    ...template,
    ...params,
    severity: params.severity || template.severity,
    status: params.status || ('status' in template ? template.status : 'success'),
  });
}
