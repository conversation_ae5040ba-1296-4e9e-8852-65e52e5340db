import { TOTP } from 'totp-generator';
import Angel<PERSON><PERSON> from '@/models/AngelUser';
import Motil<PERSON>U<PERSON> from '@/models/MotilalUser';
import connectDB from './mongodb';

// Angel Broking Login Function (based on epicrisenew/server.js)
export async function loginAngelUser(userId: string, password: string, totpKey: string, apiKey: string) {
  try {
    console.log(`🔐 Attempting Angel login for user: ${userId}`);

    // Generate TOTP
    const otp = TOTP.generate(totpKey).otp;
    console.log(`🔑 Generated TOTP: ${otp}`);

    const loginData = {
      clientcode: userId,
      password: password,
      totp: otp,
    };

    console.log(`📤 Angel login request:`, { clientcode: userId, totp: otp });

    const response = await fetch('https://apiconnect.angelone.in/rest/auth/angelbroking/user/v1/loginByPassword', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-UserType': 'USER',
        'X-SourceID': 'WEB',
        'X-ClientLocalIP': '***********',
        'X-ClientPublicIP': '***********',
        'X-MACAddress': '00:00:00:00:00:00',
        'X-PrivateKey': apiKey,
      },
      body: JSON.stringify(loginData),
    });

    const result = await response.json();

    console.log(`📨 Angel login response for ${userId}:`, {
      status: response.status,
      statusText: response.statusText,
      data: result
    });

    if (result.status && result.data) {
      const { jwtToken, refreshToken, feedToken } = result.data;

      console.log(`✅ Angel login successful for ${userId}`);

      // Update user with tokens
      await connectDB();
      await AngelUser.updateOne(
        { userId: userId },
        {
          $set: {
            jwtToken: jwtToken,
            refreshToken: refreshToken,
            feedToken: feedToken,
          },
        }
      );

      console.log(`💾 Tokens saved for Angel user: ${userId}`);

      return {
        success: true,
        message: 'Login successful',
        data: {
          jwtToken,
          refreshToken,
          feedToken,
        },
      };
    } else {
      console.error(`❌ Angel login failed for ${userId}:`, result);
      return {
        success: false,
        message: result.message || 'Login failed',
        error: result.errorcode || 'UNKNOWN_ERROR',
      };
    }
  } catch (error) {
    console.error('Angel login error:', error);
    return {
      success: false,
      message: 'Network error during login',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// Motilal Oswal Login Function (based on epicrisenew/server.js)
export async function loginMotilalUser(
  userId: string,
  password: string,
  apiKey: string,
  twoFA: string,
  totpKey: string
) {
  try {
    console.log(`🔐 Attempting Motilal login for user: ${userId}`);

    // Generate TOTP
    const otp = TOTP.generate(totpKey).otp;
    console.log(`🔑 Generated TOTP: ${otp}`);

    // Create hash (based on epicrisenew logic)
    const crypto = require('crypto');
    const hash = crypto.createHash('sha256');
    hash.update(password + apiKey);
    const hashedPassword = hash.digest('hex');

    const loginData = {
      userid: userId,
      password: hashedPassword,
      "2FA": twoFA, // Note: quotes around 2FA are important
      totp: otp, // Include the generated TOTP
    };

    console.log(`📤 Motilal login request:`, loginData);

    const response = await fetch('https://openapi.motilaloswal.com/rest/login/v3/authdirectapi', {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'MOSL/V.1.1.0',
        'ApiKey': apiKey,
        'ClientLocalIp': '***********',
        'ClientPublicIp': '***********',
        'MacAddress': '00:00:00:00:00:00',
        'SourceId': 'WEB',
        'vendorinfo': userId, // This was missing - required for MO2012 error
        'osname': 'Windows 10',
        'osversion': '10.0.19041',
        'devicemodel': 'AHV',
        'manufacturer': 'Google',
        'productname': 'Server',
        'productversion': '10.2.3.652',
        'browsername': 'Chrome',
        'browserversion': '105.0',
      },
      body: JSON.stringify(loginData),
    });

    const result = await response.json();

    console.log(`📨 Motilal login response for ${userId}:`, {
      status: response.status,
      statusText: response.statusText,
      data: result
    });

    if (result.AuthToken) {
      console.log(`✅ Motilal login successful for ${userId}`);

      // Update user with auth token
      await connectDB();
      await MotilalUser.updateOne(
        { userId: userId },
        {
          $set: {
            authToken: result.AuthToken,
          },
        }
      );

      console.log(`💾 Auth token saved for Motilal user: ${userId}`);

      return {
        success: true,
        message: 'Login successful',
        data: {
          authToken: result.AuthToken,
        },
      };
    } else {
      console.error(`❌ Motilal login failed for ${userId}:`, result);
      return {
        success: false,
        message: result.message || 'Login failed',
        error: result.errorcode || 'UNKNOWN_ERROR',
      };
    }
  } catch (error) {
    console.error('Motilal login error:', error);
    return {
      success: false,
      message: 'Network error during login',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// Login all Angel users (based on epicrisenew performLoginForAllUsers)
export async function loginAllAngelUsers() {
  try {
    await connectDB();
    const users = await AngelUser.find({});
    
    if (!users || users.length === 0) {
      return {
        success: false,
        message: 'No Angel users found',
        results: [],
      };
    }

    const results = [];

    for (const user of users) {
      try {
        const loginResult = await loginAngelUser(
          user.userId,
          user.password,
          user.totpKey,
          user.apiKey
        );

        results.push({
          userId: user.userId,
          clientName: user.clientName,
          status: loginResult.success ? 'success' : 'failed',
          message: loginResult.message,
          error: loginResult.error,
        });
      } catch (error) {
        results.push({
          userId: user.userId,
          clientName: user.clientName,
          status: 'failed',
          message: 'Login failed',
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    const successful = results.filter(r => r.status === 'success').length;
    const failed = results.filter(r => r.status === 'failed').length;

    return {
      success: true,
      message: `Login completed: ${successful} successful, ${failed} failed`,
      results,
      summary: {
        total: users.length,
        successful,
        failed,
      },
    };
  } catch (error) {
    console.error('Error logging in all Angel users:', error);
    return {
      success: false,
      message: 'Error during bulk login',
      error: error instanceof Error ? error.message : 'Unknown error',
      results: [],
    };
  }
}

// Login all Motilal users
export async function loginAllMotilalUsers() {
  try {
    await connectDB();
    const users = await MotilalUser.find({});
    
    if (!users || users.length === 0) {
      return {
        success: false,
        message: 'No Motilal users found',
        results: [],
      };
    }

    const results = [];

    for (const user of users) {
      try {
        const loginResult = await loginMotilalUser(
          user.userId,
          user.password,
          user.apiKey,
          user.twoFA,
          user.totpKey
        );

        results.push({
          userId: user.userId,
          clientName: user.clientName,
          status: loginResult.success ? 'success' : 'failed',
          message: loginResult.message,
          error: loginResult.error,
        });
      } catch (error) {
        results.push({
          userId: user.userId,
          clientName: user.clientName,
          status: 'failed',
          message: 'Login failed',
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    const successful = results.filter(r => r.status === 'success').length;
    const failed = results.filter(r => r.status === 'failed').length;

    return {
      success: true,
      message: `Login completed: ${successful} successful, ${failed} failed`,
      results,
      summary: {
        total: users.length,
        successful,
        failed,
      },
    };
  } catch (error) {
    console.error('Error logging in all Motilal users:', error);
    return {
      success: false,
      message: 'Error during bulk login',
      error: error instanceof Error ? error.message : 'Unknown error',
      results: [],
    };
  }
}
