// Simple in-memory cache for API responses
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

class MemoryCache {
  private cache = new Map<string, CacheEntry<any>>();
  private maxSize = 1000; // Maximum number of cache entries

  set<T>(key: string, data: T, ttlSeconds: number = 300): void {
    // Clean up old entries if cache is getting too large
    if (this.cache.size >= this.maxSize) {
      this.cleanup();
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttlSeconds * 1000
    });
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    // Check if entry has expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data as T;
  }

  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  // Clean up expired entries
  private cleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key));

    // If still too large, remove oldest entries
    if (this.cache.size >= this.maxSize) {
      const entries = Array.from(this.cache.entries());
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
      
      const toRemove = entries.slice(0, Math.floor(this.maxSize * 0.2));
      toRemove.forEach(([key]) => this.cache.delete(key));
    }
  }

  // Get cache statistics
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      entries: Array.from(this.cache.keys())
    };
  }
}

// Global cache instance
const cache = new MemoryCache();

// Cache wrapper function for API responses
export async function withCache<T>(
  key: string,
  fetcher: () => Promise<T>,
  ttlSeconds: number = 300
): Promise<T> {
  // Try to get from cache first
  const cached = cache.get<T>(key);
  if (cached !== null) {
    console.log(`🚀 Cache hit for key: ${key}`);
    return cached;
  }

  // Fetch fresh data
  console.log(`🔄 Cache miss for key: ${key}, fetching...`);
  const data = await fetcher();
  
  // Store in cache
  cache.set(key, data, ttlSeconds);
  
  return data;
}

// Specific cache functions for common operations
export const cacheKeys = {
  userAccounts: (userId: string, broker: string) => `user_accounts_${userId}_${broker}`,
  adminDashboard: (adminId: string) => `admin_dashboard_${adminId}`,
  superAdminDashboard: () => `super_admin_dashboard`,
  billingLogs: (page: number, limit: number) => `billing_logs_${page}_${limit}`,
  billingAnalytics: (startDate: string, endDate: string) => `billing_analytics_${startDate}_${endDate}`,
  clientAccounts: (broker?: string) => `client_accounts_${broker || 'all'}`,
};

// Cache invalidation functions
export const invalidateCache = {
  userAccounts: (userId: string, broker?: string) => {
    if (broker) {
      cache.delete(cacheKeys.userAccounts(userId, broker));
    } else {
      // Invalidate all broker accounts for user
      ['angel', 'motilal'].forEach(b => {
        cache.delete(cacheKeys.userAccounts(userId, b));
      });
    }
  },
  
  adminDashboard: (adminId: string) => {
    cache.delete(cacheKeys.adminDashboard(adminId));
  },
  
  superAdminDashboard: () => {
    cache.delete(cacheKeys.superAdminDashboard());
  },
  
  billingData: () => {
    // Clear all billing-related cache entries
    const stats = cache.getStats();
    stats.entries.forEach(key => {
      if (key.includes('billing_')) {
        cache.delete(key);
      }
    });
  },
  
  clientAccounts: () => {
    cache.delete(cacheKeys.clientAccounts());
    cache.delete(cacheKeys.clientAccounts('angel'));
    cache.delete(cacheKeys.clientAccounts('motilal'));
  }
};

export { cache };
export default cache;
