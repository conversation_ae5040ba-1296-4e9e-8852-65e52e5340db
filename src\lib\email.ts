import nodemailer from 'nodemailer';

// Email configuration
const emailConfig = {
  host: process.env.SMTP_HOST || 'smtp.gmail.com',
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: false, // true for 465, false for other ports
  auth: {
    user: process.env.SMTP_USER || '',
    pass: process.env.SMTP_PASS || '',
  },
};

// Create transporter
const transporter = nodemailer.createTransport(emailConfig);

// Verify connection configuration
export async function verifyEmailConnection() {
  try {
    await transporter.verify();
    console.log('✅ Email server is ready to take our messages');
    return true;
  } catch (error) {
    console.error('❌ Email server connection failed:', error);
    return false;
  }
}

// Send admin invitation email
export async function sendAdminInvitationEmail({
  email,
  name,
  companyName,
  temporaryPassword,
  invitationLink,
  invitedBy,
  orderRate
}: {
  email: string;
  name: string;
  companyName: string;
  temporaryPassword: string;
  invitationLink: string;
  invitedBy: string;
  orderRate: number;
}) {
  try {
    // If email is not configured, just log the details
    if (!process.env.SMTP_USER || !process.env.SMTP_PASS) {
      console.log('📧 Email service not configured. Invitation details:');
      console.log({
        to: email,
        subject: 'Trading Platform Admin Invitation',
        temporaryPassword,
        invitationLink,
        orderRate
      });
      return { success: true, message: 'Email service not configured - invitation created' };
    }

    const mailOptions = {
      from: `"Trading Platform" <${process.env.SMTP_USER}>`,
      to: email,
      subject: 'Trading Platform - Admin Invitation',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #2563eb;">Welcome to Trading Platform!</h2>
          
          <p>Hello <strong>${name}</strong>,</p>
          
          <p>You have been invited by <strong>${invitedBy}</strong> to join Trading Platform as an Admin for <strong>${companyName}</strong>.</p>
          
          <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin-top: 0;">Your Account Details:</h3>
            <ul style="list-style: none; padding: 0;">
              <li><strong>Email:</strong> ${email}</li>
              <li><strong>Temporary Password:</strong> <code style="background-color: #e5e7eb; padding: 2px 4px; border-radius: 4px;">${temporaryPassword}</code></li>
              <li><strong>Company:</strong> ${companyName}</li>
              <li><strong>Order Rate:</strong> ₹${orderRate} per order</li>
            </ul>
          </div>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${invitationLink}" 
               style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              Accept Invitation & Set Password
            </a>
          </div>
          
          <div style="background-color: #fef3c7; padding: 15px; border-radius: 6px; margin: 20px 0;">
            <p style="margin: 0; color: #92400e;"><strong>Important:</strong></p>
            <ul style="color: #92400e; margin: 10px 0;">
              <li>This invitation expires in 7 days</li>
              <li>Please change your password after first login</li>
              <li>Keep your login credentials secure</li>
            </ul>
          </div>
          
          <p>If you have any questions, please contact the super admin who invited you.</p>
          
          <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;">
          <p style="color: #6b7280; font-size: 14px;">
            This is an automated email from Trading Platform. Please do not reply to this email.
          </p>
        </div>
      `
    };

    const result = await transporter.sendMail(mailOptions);
    console.log('✅ Admin invitation email sent:', result.messageId);
    return { success: true, messageId: result.messageId };

  } catch (error) {
    console.error('❌ Failed to send admin invitation email:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

// Send password reset email
export async function sendPasswordResetEmail({
  email,
  name,
  resetLink
}: {
  email: string;
  name: string;
  resetLink: string;
}) {
  try {
    if (!process.env.SMTP_USER || !process.env.SMTP_PASS) {
      console.log('📧 Password reset email (not configured):', { email, resetLink });
      return { success: true, message: 'Email service not configured' };
    }

    const mailOptions = {
      from: `"Trading Platform" <${process.env.SMTP_USER}>`,
      to: email,
      subject: 'Trading Platform - Password Reset',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #dc2626;">Password Reset Request</h2>
          
          <p>Hello <strong>${name}</strong>,</p>
          
          <p>You have requested to reset your password for Trading Platform.</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetLink}" 
               style="background-color: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              Reset Password
            </a>
          </div>
          
          <p>If you did not request this password reset, please ignore this email.</p>
          
          <p style="color: #6b7280; font-size: 14px;">
            This link will expire in 1 hour for security reasons.
          </p>
        </div>
      `
    };

    const result = await transporter.sendMail(mailOptions);
    console.log('✅ Password reset email sent:', result.messageId);
    return { success: true, messageId: result.messageId };

  } catch (error) {
    console.error('❌ Failed to send password reset email:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}
