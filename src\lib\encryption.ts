import crypto from 'crypto';

// Encryption configuration
const ALGORITHM = 'aes-256-cbc';
const SECRET_KEY = process.env.ENCRYPTION_SECRET || 'your-32-character-secret-key-here!';
const IV_LENGTH = 16; // For CBC, this is always 16

// Ensure the secret key is 32 bytes
const getKey = (): Buffer => {
  const key = crypto.createHash('sha256').update(SECRET_KEY).digest();
  return key;
};

/**
 * Encrypt sensitive data like API keys, JWT tokens, etc.
 * @param text - The text to encrypt
 * @returns Encrypted string in format: iv:encrypted
 */
export function encrypt(text: string): string {
  if (!text) return '';

  try {
    const key = getKey();
    const iv = crypto.randomBytes(IV_LENGTH);
    const cipher = crypto.createCipher(ALGORITHM, key.toString('hex'));

    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    return `${iv.toString('hex')}:${encrypted}`;
  } catch (error) {
    console.error('Encryption error:', error);
    throw new Error('Failed to encrypt data');
  }
}

/**
 * Decrypt sensitive data
 * @param encryptedData - The encrypted string in format: iv:encrypted
 * @returns Decrypted string
 */
export function decrypt(encryptedData: string): string {
  if (!encryptedData) return '';

  try {
    const key = getKey();

    // Handle both old format (no iv) and new format (iv:encrypted)
    if (encryptedData.includes(':')) {
      const [ivHex, encrypted] = encryptedData.split(':');
      // For now, just use the encrypted part with createDecipher
      const decipher = crypto.createDecipher(ALGORITHM, key.toString('hex'));
      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      return decrypted;
    } else {
      // Old format - direct decryption
      const decipher = crypto.createDecipher(ALGORITHM, key.toString('hex'));
      let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      return decrypted;
    }
  } catch (error) {
    console.error('Decryption error:', error);
    throw new Error('Failed to decrypt data');
  }
}

/**
 * Hash sensitive data for comparison (one-way)
 * @param text - The text to hash
 * @returns Hashed string
 */
export function hash(text: string): string {
  if (!text) return '';
  
  try {
    return crypto.createHash('sha256').update(text + SECRET_KEY).digest('hex');
  } catch (error) {
    console.error('Hashing error:', error);
    throw new Error('Failed to hash data');
  }
}

/**
 * Generate a secure random token
 * @param length - Length of the token (default: 32)
 * @returns Random token string
 */
export function generateToken(length: number = 32): string {
  return crypto.randomBytes(length).toString('hex');
}

/**
 * Encrypt API key for storage
 * @param apiKey - The API key to encrypt
 * @returns Encrypted API key
 */
export function encryptApiKey(apiKey: string): string {
  return encrypt(apiKey);
}

/**
 * Decrypt API key for use
 * @param encryptedApiKey - The encrypted API key
 * @returns Decrypted API key
 */
export function decryptApiKey(encryptedApiKey: string): string {
  return decrypt(encryptedApiKey);
}

/**
 * Encrypt JWT token for storage
 * @param token - The JWT token to encrypt
 * @returns Encrypted JWT token
 */
export function encryptJwtToken(token: string): string {
  return encrypt(token);
}

/**
 * Decrypt JWT token for use
 * @param encryptedToken - The encrypted JWT token
 * @returns Decrypted JWT token
 */
export function decryptJwtToken(encryptedToken: string): string {
  return decrypt(encryptedToken);
}

/**
 * Mask sensitive data for display (show only first and last few characters)
 * @param data - The sensitive data to mask
 * @param visibleChars - Number of characters to show at start and end (default: 4)
 * @returns Masked string
 */
export function maskSensitiveData(data: string, visibleChars: number = 4): string {
  if (!data || data.length <= visibleChars * 2) {
    return '*'.repeat(data?.length || 8);
  }
  
  const start = data.substring(0, visibleChars);
  const end = data.substring(data.length - visibleChars);
  const middle = '*'.repeat(Math.max(4, data.length - visibleChars * 2));
  
  return `${start}${middle}${end}`;
}

/**
 * Validate if a string is properly encrypted
 * @param encryptedData - The data to validate
 * @returns True if valid encrypted format
 */
export function isValidEncryptedData(encryptedData: string): boolean {
  if (!encryptedData) return false;
  
  const parts = encryptedData.split(':');
  return (parts.length === 2 &&
         parts[0].length === IV_LENGTH * 2 && // IV in hex
         parts[1].length > 0) || // Encrypted data
         (parts.length === 1 && parts[0].length > 0); // Old format without IV
}

/**
 * Secure compare two strings (prevents timing attacks)
 * @param a - First string
 * @param b - Second string
 * @returns True if strings match
 */
export function secureCompare(a: string, b: string): boolean {
  if (a.length !== b.length) {
    return false;
  }
  
  let result = 0;
  for (let i = 0; i < a.length; i++) {
    result |= a.charCodeAt(i) ^ b.charCodeAt(i);
  }
  
  return result === 0;
}

// Export utility functions for broker-specific encryption
export const brokerEncryption = {
  angel: {
    encryptApiKey: (apiKey: string) => encryptApiKey(apiKey),
    decryptApiKey: (encryptedApiKey: string) => decryptApiKey(encryptedApiKey),
    encryptJwtToken: (token: string) => encryptJwtToken(token),
    decryptJwtToken: (encryptedToken: string) => decryptJwtToken(encryptedToken),
    encryptTotpKey: (totpKey: string) => encrypt(totpKey),
    decryptTotpKey: (encryptedTotpKey: string) => decrypt(encryptedTotpKey)
  },
  motilal: {
    encryptApiKey: (apiKey: string) => encryptApiKey(apiKey),
    decryptApiKey: (encryptedApiKey: string) => decryptApiKey(encryptedApiKey),
    encryptAuthToken: (token: string) => encrypt(token),
    decryptAuthToken: (encryptedToken: string) => decrypt(encryptedToken),
    encryptTotpKey: (totpKey: string) => encrypt(totpKey),
    decryptTotpKey: (encryptedTotpKey: string) => decrypt(encryptedTotpKey)
  }
};

export default {
  encrypt,
  decrypt,
  hash,
  generateToken,
  encryptApiKey,
  decryptApiKey,
  encryptJwtToken,
  decryptJwtToken,
  maskSensitiveData,
  isValidEncryptedData,
  secureCompare,
  brokerEncryption
};
