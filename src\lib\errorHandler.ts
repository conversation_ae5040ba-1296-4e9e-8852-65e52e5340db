import { NextResponse } from 'next/server';

export interface ApiError extends Error {
  statusCode?: number;
  code?: string;
}

export class AppError extends Error implements ApiError {
  statusCode: number;
  code?: string;

  constructor(message: string, statusCode: number = 500, code?: string) {
    super(message);
    this.name = 'AppError';
    this.statusCode = statusCode;
    this.code = code;
  }
}

export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 400, 'VALIDATION_ERROR');
    this.name = 'ValidationError';
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication required') {
    super(message, 401, 'AUTHENTICATION_ERROR');
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = 'Access denied') {
    super(message, 403, 'AUTHORIZATION_ERROR');
    this.name = 'AuthorizationError';
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = 'Resource not found') {
    super(message, 404, 'NOT_FOUND_ERROR');
    this.name = 'NotFoundError';
  }
}

export class ConflictError extends AppError {
  constructor(message: string = 'Resource conflict') {
    super(message, 409, 'CONFLICT_ERROR');
    this.name = 'ConflictError';
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = 'Rate limit exceeded') {
    super(message, 429, 'RATE_LIMIT_ERROR');
    this.name = 'RateLimitError';
  }
}

/**
 * Global error handler for API routes
 */
export function handleApiError(error: any): NextResponse {
  console.error('API Error:', error);

  // Handle known error types
  if (error instanceof AppError) {
    return NextResponse.json(
      {
        error: error.message,
        code: error.code,
        statusCode: error.statusCode
      },
      { status: error.statusCode }
    );
  }

  // Handle JWT errors
  if (error.name === 'JsonWebTokenError') {
    return NextResponse.json(
      {
        error: 'Invalid token',
        code: 'INVALID_TOKEN',
        statusCode: 401
      },
      { status: 401 }
    );
  }

  if (error.name === 'TokenExpiredError') {
    return NextResponse.json(
      {
        error: 'Token expired',
        code: 'TOKEN_EXPIRED',
        statusCode: 401
      },
      { status: 401 }
    );
  }

  // Handle MongoDB/Mongoose errors
  if (error.name === 'ValidationError') {
    const errors = Object.values(error.errors || {}).map((err: any) => err.message);
    return NextResponse.json(
      {
        error: 'Validation failed',
        code: 'VALIDATION_ERROR',
        details: errors,
        statusCode: 400
      },
      { status: 400 }
    );
  }

  if (error.name === 'CastError') {
    return NextResponse.json(
      {
        error: 'Invalid ID format',
        code: 'INVALID_ID',
        statusCode: 400
      },
      { status: 400 }
    );
  }

  if (error.code === 11000) {
    return NextResponse.json(
      {
        error: 'Duplicate entry',
        code: 'DUPLICATE_ENTRY',
        statusCode: 409
      },
      { status: 409 }
    );
  }

  // Handle network/connection errors
  if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
    return NextResponse.json(
      {
        error: 'Service unavailable',
        code: 'SERVICE_UNAVAILABLE',
        statusCode: 503
      },
      { status: 503 }
    );
  }

  // Handle timeout errors
  if (error.code === 'ETIMEDOUT') {
    return NextResponse.json(
      {
        error: 'Request timeout',
        code: 'TIMEOUT',
        statusCode: 408
      },
      { status: 408 }
    );
  }

  // Default to internal server error
  return NextResponse.json(
    {
      error: process.env.NODE_ENV === 'development' 
        ? error.message || 'Internal server error'
        : 'Internal server error',
      code: 'INTERNAL_ERROR',
      statusCode: 500,
      ...(process.env.NODE_ENV === 'development' && {
        stack: error.stack,
        details: error
      })
    },
    { status: 500 }
  );
}

/**
 * Async wrapper for API route handlers
 */
export function withErrorHandler(handler: Function) {
  return async (request: Request, context?: any) => {
    try {
      return await handler(request, context);
    } catch (error) {
      return handleApiError(error);
    }
  };
}

/**
 * Utility function to create standardized success responses
 */
export function createSuccessResponse(data: any, message?: string, status: number = 200) {
  return NextResponse.json(
    {
      success: true,
      message,
      data,
      timestamp: new Date().toISOString()
    },
    { status }
  );
}

/**
 * Utility function to create standardized error responses
 */
export function createErrorResponse(
  message: string, 
  statusCode: number = 500, 
  code?: string, 
  details?: any
) {
  return NextResponse.json(
    {
      success: false,
      error: message,
      code,
      statusCode,
      details,
      timestamp: new Date().toISOString()
    },
    { status: statusCode }
  );
}

/**
 * Validation helper
 */
export function validateRequired(data: any, fields: string[]): void {
  const missing = fields.filter(field => !data[field]);
  if (missing.length > 0) {
    throw new ValidationError(`Missing required fields: ${missing.join(', ')}`);
  }
}

/**
 * Rate limiting helper (basic implementation)
 */
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

export function checkRateLimit(
  identifier: string, 
  maxRequests: number = 100, 
  windowMs: number = 60000
): void {
  const now = Date.now();
  const windowStart = now - windowMs;
  
  const current = rateLimitMap.get(identifier);
  
  if (!current || current.resetTime < windowStart) {
    rateLimitMap.set(identifier, { count: 1, resetTime: now + windowMs });
    return;
  }
  
  if (current.count >= maxRequests) {
    throw new RateLimitError(`Rate limit exceeded. Try again in ${Math.ceil((current.resetTime - now) / 1000)} seconds.`);
  }
  
  current.count++;
}

/**
 * Log error to external service (placeholder)
 */
export function logError(error: Error, context?: any): void {
  // In production, you might want to send this to an error tracking service
  // like Sentry, LogRocket, or similar
  console.error('Error logged:', {
    message: error.message,
    stack: error.stack,
    context,
    timestamp: new Date().toISOString()
  });
}
