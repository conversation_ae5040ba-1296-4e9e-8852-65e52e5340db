// Memory management utilities to prevent memory leaks

interface ResourceTracker {
  id: string;
  type: 'interval' | 'timeout' | 'websocket' | 'eventListener' | 'subscription';
  resource: any;
  createdAt: Date;
  cleanup: () => void;
}

class MemoryManager {
  private resources = new Map<string, ResourceTracker>();
  private cleanupInterval: NodeJS.Timeout | null = null;

  constructor() {
    // Auto-cleanup every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.performCleanup();
    }, 5 * 60 * 1000);

    // Cleanup on process exit
    if (typeof process !== 'undefined') {
      process.on('beforeExit', () => this.cleanup());
      process.on('SIGINT', () => this.cleanup());
      process.on('SIGTERM', () => this.cleanup());
    }

    // Cleanup on page unload (browser)
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', () => this.cleanup());
      window.addEventListener('unload', () => this.cleanup());
    }
  }

  // Track a resource for automatic cleanup
  track(
    id: string,
    type: ResourceTracker['type'],
    resource: any,
    cleanup: () => void
  ): string {
    const tracker: ResourceTracker = {
      id,
      type,
      resource,
      createdAt: new Date(),
      cleanup
    };

    this.resources.set(id, tracker);
    console.log(`🔍 Tracking ${type} resource: ${id}`);
    return id;
  }

  // Untrack and cleanup a specific resource
  untrack(id: string): boolean {
    const tracker = this.resources.get(id);
    if (tracker) {
      try {
        tracker.cleanup();
        this.resources.delete(id);
        console.log(`🧹 Cleaned up ${tracker.type} resource: ${id}`);
        return true;
      } catch (error) {
        console.error(`❌ Error cleaning up resource ${id}:`, error);
        this.resources.delete(id); // Remove even if cleanup failed
        return false;
      }
    }
    return false;
  }

  // Track a setTimeout
  trackTimeout(callback: () => void, delay: number): string {
    const id = `timeout_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const timeoutId = setTimeout(() => {
      callback();
      this.untrack(id);
    }, delay);

    this.track(id, 'timeout', timeoutId, () => {
      clearTimeout(timeoutId);
    });

    return id;
  }

  // Track a setInterval
  trackInterval(callback: () => void, interval: number): string {
    const id = `interval_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const intervalId = setInterval(callback, interval);

    this.track(id, 'interval', intervalId, () => {
      clearInterval(intervalId);
    });

    return id;
  }

  // Track a WebSocket connection
  trackWebSocket(ws: WebSocket | any, id?: string): string {
    const resourceId = id || `websocket_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    this.track(resourceId, 'websocket', ws, () => {
      if (ws && typeof ws.close === 'function') {
        ws.close();
      }
      if (ws && typeof ws.disconnect === 'function') {
        ws.disconnect();
      }
    });

    return resourceId;
  }

  // Track an event listener
  trackEventListener(
    element: EventTarget,
    event: string,
    listener: EventListener,
    options?: boolean | AddEventListenerOptions
  ): string {
    const id = `listener_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    element.addEventListener(event, listener, options);
    
    this.track(id, 'eventListener', { element, event, listener, options }, () => {
      element.removeEventListener(event, listener, options);
    });

    return id;
  }

  // Track a subscription (like RxJS or similar)
  trackSubscription(subscription: any, id?: string): string {
    const resourceId = id || `subscription_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    this.track(resourceId, 'subscription', subscription, () => {
      if (subscription && typeof subscription.unsubscribe === 'function') {
        subscription.unsubscribe();
      }
      if (subscription && typeof subscription.close === 'function') {
        subscription.close();
      }
      if (subscription && typeof subscription.destroy === 'function') {
        subscription.destroy();
      }
    });

    return resourceId;
  }

  // Get resource statistics
  getStats() {
    const stats = {
      total: this.resources.size,
      byType: {} as Record<string, number>,
      oldResources: [] as Array<{ id: string; type: string; age: number }>
    };

    const now = Date.now();
    
    for (const [id, tracker] of this.resources) {
      stats.byType[tracker.type] = (stats.byType[tracker.type] || 0) + 1;
      
      const age = now - tracker.createdAt.getTime();
      if (age > 10 * 60 * 1000) { // Older than 10 minutes
        stats.oldResources.push({
          id,
          type: tracker.type,
          age: Math.round(age / 1000) // in seconds
        });
      }
    }

    return stats;
  }

  // Perform automatic cleanup of old resources
  private performCleanup() {
    const stats = this.getStats();
    
    if (stats.oldResources.length > 0) {
      console.log(`🧹 Auto-cleanup: Found ${stats.oldResources.length} old resources`);
      
      stats.oldResources.forEach(({ id, type }) => {
        console.log(`🧹 Auto-cleaning old ${type} resource: ${id}`);
        this.untrack(id);
      });
    }

    // Log memory stats
    if (stats.total > 0) {
      console.log(`📊 Memory Manager Stats:`, stats);
    }
  }

  // Manual cleanup of all resources
  cleanup() {
    console.log(`🧹 Cleaning up ${this.resources.size} tracked resources...`);
    
    for (const [id, tracker] of this.resources) {
      try {
        tracker.cleanup();
        console.log(`✅ Cleaned up ${tracker.type}: ${id}`);
      } catch (error) {
        console.error(`❌ Error cleaning up ${tracker.type} ${id}:`, error);
      }
    }
    
    this.resources.clear();
    
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    
    console.log('🧹 Memory manager cleanup complete');
  }

  // Force garbage collection (if available)
  forceGC() {
    if (typeof global !== 'undefined' && global.gc) {
      console.log('🗑️ Forcing garbage collection...');
      global.gc();
    } else {
      console.log('⚠️ Garbage collection not available');
    }
  }
}

// Global memory manager instance
const memoryManager = new MemoryManager();

// Convenience functions
export const trackTimeout = (callback: () => void, delay: number) => 
  memoryManager.trackTimeout(callback, delay);

export const trackInterval = (callback: () => void, interval: number) => 
  memoryManager.trackInterval(callback, interval);

export const trackWebSocket = (ws: WebSocket | any, id?: string) => 
  memoryManager.trackWebSocket(ws, id);

export const trackEventListener = (
  element: EventTarget,
  event: string,
  listener: EventListener,
  options?: boolean | AddEventListenerOptions
) => memoryManager.trackEventListener(element, event, listener, options);

export const trackSubscription = (subscription: any, id?: string) => 
  memoryManager.trackSubscription(subscription, id);

export const untrack = (id: string) => memoryManager.untrack(id);

export const getMemoryStats = () => memoryManager.getStats();

export const cleanupMemory = () => memoryManager.cleanup();

export const forceGC = () => memoryManager.forceGC();

// React hook for automatic cleanup
export function useMemoryManager() {
  const trackedResources = new Set<string>();

  const track = (
    type: ResourceTracker['type'],
    resource: any,
    cleanup: () => void,
    id?: string
  ) => {
    const resourceId = id || `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    memoryManager.track(resourceId, type, resource, cleanup);
    trackedResources.add(resourceId);
    return resourceId;
  };

  const cleanup = () => {
    trackedResources.forEach(id => {
      memoryManager.untrack(id);
    });
    trackedResources.clear();
  };

  // Return cleanup function for useEffect
  return { track, cleanup };
}

export { memoryManager };
export default memoryManager;
