import mongoose from 'mongoose';

const MONGODB_URI = process.env.TESTLIST || process.env.MONGODB_URI || 'mongodb://localhost:27017/trading-platform';

if (!MONGODB_URI) {
  throw new Error('Please define the TESTLIST or MONGODB_URI environment variable inside .env');
}

/**
 * Enhanced database connection manager with better error handling and monitoring
 */
interface ConnectionCache {
  conn: typeof mongoose | null;
  promise: Promise<typeof mongoose> | null;
  lastConnected: Date | null;
  connectionCount: number;
  errors: Array<{ timestamp: Date; error: string }>;
}

let cached: ConnectionCache = (global as any).mongoose;

if (!cached) {
  cached = (global as any).mongoose = {
    conn: null,
    promise: null,
    lastConnected: null,
    connectionCount: 0,
    errors: []
  };
}

// Connection monitoring
function logConnectionEvent(event: string, details?: any) {
  const timestamp = new Date().toISOString();
  console.log(`🗄️ [MongoDB] ${timestamp} - ${event}`, details || '');
}

function logConnectionError(error: Error) {
  const errorEntry = {
    timestamp: new Date(),
    error: error.message
  };

  cached.errors.push(errorEntry);

  // Keep only last 10 errors
  if (cached.errors.length > 10) {
    cached.errors.shift();
  }

  console.error(`❌ [MongoDB] Connection error:`, error);
}

async function connectDB() {
  // Return existing connection if available
  if (cached.conn) {
    // Check if connection is still alive
    if (cached.conn.connection.readyState === 1) {
      return cached.conn;
    } else {
      logConnectionEvent('Connection lost, reconnecting...');
      cached.conn = null;
      cached.promise = null;
    }
  }

  if (!cached.promise) {
    const opts = {
      bufferCommands: false,
      maxPoolSize: 15, // Increased pool size for better performance
      serverSelectionTimeoutMS: 8000, // Increased timeout
      socketTimeoutMS: 60000, // Increased socket timeout
      family: 4, // Use IPv4, skip trying IPv6
      maxIdleTimeMS: 30000, // Close connections after 30 seconds of inactivity
      connectTimeoutMS: 15000, // Increased connection timeout
      heartbeatFrequencyMS: 10000, // Check connection health every 10 seconds
      retryWrites: true,
      retryReads: true,
      compressors: ['zlib' as const], // Enable compression
      zlibCompressionLevel: 6 as const,
    };

    logConnectionEvent('Initiating database connection...');

    cached.promise = mongoose.connect(MONGODB_URI!, opts);

    // Add connection event listeners
    mongoose.connection.on('connected', () => {
      cached.lastConnected = new Date();
      cached.connectionCount++;
      logConnectionEvent('Connected successfully', {
        connectionCount: cached.connectionCount,
        readyState: mongoose.connection.readyState
      });
    });

    mongoose.connection.on('error', (error) => {
      logConnectionError(error);
    });

    mongoose.connection.on('disconnected', () => {
      logConnectionEvent('Disconnected');
      cached.conn = null;
      cached.promise = null;
    });

    mongoose.connection.on('reconnected', () => {
      logConnectionEvent('Reconnected');
    });
  }

  try {
    cached.conn = await cached.promise;
    return cached.conn;
  } catch (error) {
    cached.promise = null;
    logConnectionError(error as Error);
    throw error;
  }
}

// Health check function
export function getConnectionHealth() {
  return {
    isConnected: cached.conn?.connection.readyState === 1,
    readyState: cached.conn?.connection.readyState || 0,
    lastConnected: cached.lastConnected,
    connectionCount: cached.connectionCount,
    recentErrors: cached.errors.slice(-5), // Last 5 errors
    host: cached.conn?.connection.host,
    port: cached.conn?.connection.port,
    name: cached.conn?.connection.name
  };
}

// Force disconnect (for testing or cleanup)
export async function disconnectDB() {
  if (cached.conn) {
    await cached.conn.disconnect();
    cached.conn = null;
    cached.promise = null;
    logConnectionEvent('Manually disconnected');
  }
}

// Connection statistics
export function getConnectionStats() {
  const health = getConnectionHealth();
  return {
    ...health,
    uptime: health.lastConnected ? Date.now() - health.lastConnected.getTime() : 0,
    errorRate: cached.errors.length > 0 ?
      cached.errors.filter(e => Date.now() - e.timestamp.getTime() < 60000).length : 0 // Errors in last minute
  };
}

export default connectDB;
