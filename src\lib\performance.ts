// Performance monitoring and optimization utilities

interface PerformanceMetric {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  metadata?: Record<string, any>;
}

interface PerformanceStats {
  totalRequests: number;
  averageResponseTime: number;
  minResponseTime: number;
  maxResponseTime: number;
  errorRate: number;
  throughput: number; // requests per second
  lastUpdated: Date;
}

class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetric[]> = new Map();
  private activeMetrics: Map<string, PerformanceMetric> = new Map();
  private maxMetricsPerEndpoint = 1000;
  private cleanupInterval: NodeJS.Timeout;

  constructor() {
    // Clean up old metrics every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  // Start timing a performance metric
  startTiming(name: string, metadata?: Record<string, any>): string {
    const id = `${name}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const metric: PerformanceMetric = {
      name,
      startTime: performance.now(),
      metadata
    };

    this.activeMetrics.set(id, metric);
    return id;
  }

  // End timing and record the metric
  endTiming(id: string): number | null {
    const metric = this.activeMetrics.get(id);
    if (!metric) {
      console.warn(`Performance metric not found: ${id}`);
      return null;
    }

    metric.endTime = performance.now();
    metric.duration = metric.endTime - metric.startTime;

    // Store the completed metric
    const endpointMetrics = this.metrics.get(metric.name) || [];
    endpointMetrics.push(metric);

    // Keep only the most recent metrics
    if (endpointMetrics.length > this.maxMetricsPerEndpoint) {
      endpointMetrics.splice(0, endpointMetrics.length - this.maxMetricsPerEndpoint);
    }

    this.metrics.set(metric.name, endpointMetrics);
    this.activeMetrics.delete(id);

    return metric.duration;
  }

  // Get performance statistics for an endpoint
  getStats(name: string): PerformanceStats | null {
    const endpointMetrics = this.metrics.get(name);
    if (!endpointMetrics || endpointMetrics.length === 0) {
      return null;
    }

    const durations = endpointMetrics
      .filter(m => m.duration !== undefined)
      .map(m => m.duration!);

    if (durations.length === 0) return null;

    const totalRequests = durations.length;
    const averageResponseTime = durations.reduce((sum, d) => sum + d, 0) / totalRequests;
    const minResponseTime = Math.min(...durations);
    const maxResponseTime = Math.max(...durations);

    // Calculate error rate (if metadata includes error info)
    const errors = endpointMetrics.filter(m => m.metadata?.error === true).length;
    const errorRate = (errors / totalRequests) * 100;

    // Calculate throughput (requests per second over last minute)
    const oneMinuteAgo = Date.now() - 60000;
    const recentRequests = endpointMetrics.filter(m => 
      m.startTime > oneMinuteAgo - performance.timeOrigin
    ).length;
    const throughput = recentRequests / 60;

    return {
      totalRequests,
      averageResponseTime,
      minResponseTime,
      maxResponseTime,
      errorRate,
      throughput,
      lastUpdated: new Date()
    };
  }

  // Get all performance statistics
  getAllStats(): Record<string, PerformanceStats> {
    const allStats: Record<string, PerformanceStats> = {};
    
    for (const [name] of this.metrics) {
      const stats = this.getStats(name);
      if (stats) {
        allStats[name] = stats;
      }
    }

    return allStats;
  }

  // Clean up old metrics
  private cleanup() {
    const fiveMinutesAgo = performance.now() - 5 * 60 * 1000;
    
    for (const [name, endpointMetrics] of this.metrics) {
      const recentMetrics = endpointMetrics.filter(m => 
        m.startTime > fiveMinutesAgo
      );
      
      if (recentMetrics.length === 0) {
        this.metrics.delete(name);
      } else {
        this.metrics.set(name, recentMetrics);
      }
    }

    // Clean up stale active metrics (older than 1 minute)
    const oneMinuteAgo = performance.now() - 60 * 1000;
    for (const [id, metric] of this.activeMetrics) {
      if (metric.startTime < oneMinuteAgo) {
        console.warn(`Cleaning up stale active metric: ${id}`);
        this.activeMetrics.delete(id);
      }
    }
  }

  // Record an error for performance tracking
  recordError(name: string, error: Error, metadata?: Record<string, any>) {
    const metric: PerformanceMetric = {
      name,
      startTime: performance.now(),
      endTime: performance.now(),
      duration: 0,
      metadata: {
        ...metadata,
        error: true,
        errorMessage: error.message,
        errorStack: error.stack
      }
    };

    const endpointMetrics = this.metrics.get(name) || [];
    endpointMetrics.push(metric);
    this.metrics.set(name, endpointMetrics);
  }

  // Get slow queries/operations
  getSlowOperations(threshold: number = 1000): Array<{
    name: string;
    duration: number;
    metadata?: Record<string, any>;
  }> {
    const slowOps: Array<{
      name: string;
      duration: number;
      metadata?: Record<string, any>;
    }> = [];

    for (const [name, endpointMetrics] of this.metrics) {
      for (const metric of endpointMetrics) {
        if (metric.duration && metric.duration > threshold) {
          slowOps.push({
            name: metric.name,
            duration: metric.duration,
            metadata: metric.metadata
          });
        }
      }
    }

    return slowOps.sort((a, b) => b.duration - a.duration);
  }

  // Destroy the monitor
  destroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.metrics.clear();
    this.activeMetrics.clear();
  }
}

// Global performance monitor instance
const performanceMonitor = new PerformanceMonitor();

// Decorator for timing functions
export function timed(name?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const metricName = name || `${target.constructor.name}.${propertyKey}`;

    descriptor.value = async function (...args: any[]) {
      const timingId = performanceMonitor.startTiming(metricName, {
        args: args.length,
        className: target.constructor.name,
        methodName: propertyKey
      });

      try {
        const result = await originalMethod.apply(this, args);
        performanceMonitor.endTiming(timingId);
        return result;
      } catch (error) {
        performanceMonitor.endTiming(timingId);
        performanceMonitor.recordError(metricName, error as Error);
        throw error;
      }
    };

    return descriptor;
  };
}

// Higher-order function for timing async operations
export async function withTiming<T>(
  name: string,
  operation: () => Promise<T>,
  metadata?: Record<string, any>
): Promise<T> {
  const timingId = performanceMonitor.startTiming(name, metadata);
  
  try {
    const result = await operation();
    const duration = performanceMonitor.endTiming(timingId);
    
    if (duration && duration > 1000) {
      console.warn(`Slow operation detected: ${name} took ${duration.toFixed(2)}ms`);
    }
    
    return result;
  } catch (error) {
    performanceMonitor.endTiming(timingId);
    performanceMonitor.recordError(name, error as Error, metadata);
    throw error;
  }
}

// Memory usage monitoring
export function getMemoryUsage() {
  if (typeof process !== 'undefined' && process.memoryUsage) {
    const usage = process.memoryUsage();
    return {
      rss: Math.round(usage.rss / 1024 / 1024), // MB
      heapTotal: Math.round(usage.heapTotal / 1024 / 1024), // MB
      heapUsed: Math.round(usage.heapUsed / 1024 / 1024), // MB
      external: Math.round(usage.external / 1024 / 1024), // MB
      arrayBuffers: Math.round(usage.arrayBuffers / 1024 / 1024) // MB
    };
  }
  return null;
}

// Performance report generation
export function generatePerformanceReport(): {
  stats: Record<string, PerformanceStats>;
  slowOperations: Array<{ name: string; duration: number; metadata?: Record<string, any> }>;
  memoryUsage: ReturnType<typeof getMemoryUsage>;
  timestamp: Date;
} {
  return {
    stats: performanceMonitor.getAllStats(),
    slowOperations: performanceMonitor.getSlowOperations(),
    memoryUsage: getMemoryUsage(),
    timestamp: new Date()
  };
}

export { performanceMonitor };
export default performanceMonitor;
