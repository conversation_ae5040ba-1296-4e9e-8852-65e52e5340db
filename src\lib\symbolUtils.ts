import { MongoClient } from 'mongodb';

// Use the correct MongoDB URI from environment variables
// This should match the connection string used in epicrisenew
const MONGODB_URI = process.env.TESTLIST || process.env.MONGODB_URI || 'mongodb://localhost:27017';
let client: MongoClient;

async function connectToAngelDB() {
  try {
    if (!client) {
      console.log(`🔌 Connecting to MongoDB: ${MONGODB_URI}`);
      client = new MongoClient(MONGODB_URI);
      await client.connect();
      console.log(`✅ Connected to MongoDB successfully`);
    }
    return client;
  } catch (error) {
    console.error(`❌ MongoDB connection error:`, error);
    throw error;
  }
}

// Function to find symbol data from Angel_api database by symbol name (your exact implementation)
export async function findSymbolInAngelDB(symbolName: string) {
  try {
    const client = await connectToAngelDB();
    const database = client.db("Angel_api");
    const collection = database.collection("totalscript");

    const query = {
      exch_seg: "NSE",
      name: symbolName,
    };

    console.log(`🔍 Searching for symbol: ${symbolName} in Angel_api database`);
    const results = await collection.find(query).toArray();
    console.log(`Found ${results.length} results for symbol: ${symbolName}`);

    for (let result of results) {
      if (result.symbol && result.symbol.endsWith("-EQ")) {
        console.log(`✅ Found matching symbol: ${result.symbol} with token: ${result.token}`);
        return {
          token: result.token,
          symbol: result.symbol,
          name: result.name,
          exch_seg: result.exch_seg,
          lotsize: result.lotsize,
          tick_size: result.tick_size,
          instrumenttype: result.instrumenttype || '',
          expiry: result.expiry || '',
          strike: result.strike || ''
        };
      }
    }

    console.log(`❌ No -EQ symbol found for: ${symbolName}`);
    return null;
  } catch (error) {
    console.error("Database query error:", error);
    return null;
  }
}

// Function to find symbol by token (security ID) - Now only NSE symbols remain
export async function findSymbolByToken(securityId: string) {
  try {
    const client = await connectToAngelDB();
    const database = client.db("Angel_api");
    const collection = database.collection("totalscript");

    console.log(`🔍 Searching for security ID (token): ${securityId} in Angel_api database`);

    // Search by token field - no need for exchange filter since CDS symbols are removed
    const result = await collection.findOne({
      token: securityId.toString()
    });

    if (result) {
      console.log(`✅ Found security ID: ${result.token} for symbol: ${result.symbol} on ${result.exch_seg}`);
      return {
        token: result.token, // This is the security ID
        symbol: result.symbol, // This is the trading symbol
        name: result.name, // This is the instrument name
        exch_seg: result.exch_seg,
        lotsize: result.lotsize,
        tick_size: result.tick_size,
        instrumenttype: result.instrumenttype || '',
        expiry: result.expiry || '',
        strike: result.strike || ''
      };
    } else {
      console.log(`❌ Security ID ${securityId} not found in Angel_api database`);
      return null;
    }
  } catch (error) {
    console.error("Error searching security ID in Angel_api database:", error);
    return null;
  }
}

// Function to find symbol by security ID (the correct approach for order placement)
export async function findSymbolBySecurityId(securityId: string) {
  try {
    const client = await connectToAngelDB();
    const database = client.db("Angel_api");
    const collection = database.collection("totalscript");

    console.log(`🔍 Searching for security ID: ${securityId} in Angel_api database`);

    // Convert to string if it's a number
    const searchId = securityId.toString();

    // Search by token field - no need for exchange filtering since CDS symbols are removed
    const result = await collection.findOne({
      token: searchId
    });

    if (result) {
      console.log(`✅ Found security ID: ${result.token} for symbol: ${result.symbol} (${result.name}) on ${result.exch_seg}`);
      return {
        token: result.token, // Security ID for Angel API
        symbol: result.symbol, // Trading symbol for Angel API
        name: result.name, // Company/instrument name
        exch_seg: result.exch_seg,
        lotsize: result.lotsize,
        tick_size: result.tick_size,
        instrumenttype: result.instrumenttype,
        expiry: result.expiry,
        strike: result.strike
      };
    } else {
      console.log(`❌ Security ID ${securityId} not found in Angel_api database`);
      return null;
    }
  } catch (error) {
    console.error("Error searching security ID in Angel_api database:", error);
    return null;
  }
}

// Function specifically for Angel orders - only NSE symbols
export async function findNSESymbolBySecurityId(securityId: string) {
  try {
    const client = await connectToAngelDB();
    const database = client.db("Angel_api");
    const collection = database.collection("totalscript");

    console.log(`🔍 Searching for NSE security ID: ${securityId} in Angel_api database`);

    // Convert to string if it's a number
    const searchId = securityId.toString();

    // Search ONLY in NSE exchange for Angel orders
    const result = await collection.findOne({
      token: searchId,
      exch_seg: "NSE"  // Only NSE for Angel orders
    });

    if (result) {
      console.log(`✅ Found NSE security ID: ${result.token} for symbol: ${result.symbol} (${result.name}) on ${result.exch_seg}`);
      return {
        token: result.token, // Security ID for Angel API
        symbol: result.symbol, // Trading symbol for Angel API
        name: result.name, // Company/instrument name
        exch_seg: result.exch_seg,
        lotsize: result.lotsize,
        tick_size: result.tick_size,
        instrumenttype: result.instrumenttype,
        expiry: result.expiry,
        strike: result.strike
      };
    } else {
      console.log(`❌ NSE Security ID ${securityId} not found in Angel_api database`);
      return null;
    }
  } catch (error) {
    console.error("Error searching NSE security ID in Angel_api database:", error);
    return null;
  }
}

// Function to search symbols by partial name match
export async function searchSymbols(searchTerm: string, limit: number = 10) {
  try {
    const client = await connectToAngelDB();
    const database = client.db("Angel_api");
    const collection = database.collection("totalscript");

    console.log(`🔍 Searching for symbols matching: ${searchTerm}`);

    const query = {
      exch_seg: "NSE",
      $or: [
        { name: { $regex: searchTerm, $options: 'i' } },
        { symbol: { $regex: searchTerm, $options: 'i' } }
      ],
      symbol: { $regex: "-EQ$" }
    };

    const results = await collection.find(query).limit(limit).toArray();

    console.log(`✅ Found ${results.length} symbols matching: ${searchTerm}`);

    return results.map(result => ({
      token: result.token,
      symbol: result.symbol,
      name: result.name,
      exch_seg: result.exch_seg,
      lotsize: result.lotsize,
      tick_size: result.tick_size
    }));
  } catch (error) {
    console.error("Error searching symbols:", error);
    return [];
  }
}
