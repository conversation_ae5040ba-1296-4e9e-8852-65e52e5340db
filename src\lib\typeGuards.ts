// Type guards and utility functions for better TypeScript safety

import { User, BrokerAccount, OrderResponse, MarketData, BillingRecord } from '@/types/api';

// Basic type guards
export function isString(value: unknown): value is string {
  return typeof value === 'string';
}

export function isNumber(value: unknown): value is number {
  return typeof value === 'number' && !isNaN(value);
}

export function isBoolean(value: unknown): value is boolean {
  return typeof value === 'boolean';
}

export function isObject(value: unknown): value is Record<string, unknown> {
  return typeof value === 'object' && value !== null && !Array.isArray(value);
}

export function isArray<T>(value: unknown, itemGuard?: (item: unknown) => item is T): value is T[] {
  if (!Array.isArray(value)) return false;
  if (!itemGuard) return true;
  return value.every(itemGuard);
}

// MongoDB ObjectId type guard
export function isValidObjectId(value: unknown): value is string {
  return isString(value) && /^[0-9a-fA-F]{24}$/.test(value);
}

// Email validation type guard
export function isValidEmail(value: unknown): value is string {
  return isString(value) && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
}

// User role type guard
export function isValidUserRole(value: unknown): value is 'user' | 'admin' | 'super_admin' {
  return isString(value) && ['user', 'admin', 'super_admin'].includes(value);
}

// Broker type guard
export function isValidBroker(value: unknown): value is 'angel' | 'motilal' {
  return isString(value) && ['angel', 'motilal'].includes(value);
}

// Order type guard
export function isValidOrderType(value: unknown): value is 'BUY' | 'SELL' {
  return isString(value) && ['BUY', 'SELL'].includes(value);
}

// Product type guard
export function isValidProductType(value: unknown): value is 'INTRADAY' | 'DELIVERY' | 'CNC' | 'MIS' {
  return isString(value) && ['INTRADAY', 'DELIVERY', 'CNC', 'MIS'].includes(value);
}

// Complex object type guards
export function isUser(value: unknown): value is User {
  if (!isObject(value)) return false;
  
  const user = value as Record<string, unknown>;
  
  return (
    isValidObjectId(user._id) &&
    isString(user.name) &&
    isValidEmail(user.email) &&
    isValidUserRole(user.role) &&
    isString(user.userCode) &&
    isBoolean(user.isActive) &&
    (user.adminId === undefined || isValidObjectId(user.adminId)) &&
    (user.superAdminId === undefined || isValidObjectId(user.superAdminId)) &&
    (user.companyName === undefined || isString(user.companyName)) &&
    (user.orderRate === undefined || isNumber(user.orderRate))
  );
}

export function isBrokerAccount(value: unknown): value is BrokerAccount {
  if (!isObject(value)) return false;
  
  const account = value as Record<string, unknown>;
  
  return (
    isValidObjectId(account._id) &&
    (isValidObjectId(account.owner) || isUser(account.owner)) &&
    isString(account.clientCode) &&
    isValidBroker(account.broker) &&
    isBoolean(account.isActive)
  );
}

export function isOrderResponse(value: unknown): value is OrderResponse {
  if (!isObject(value)) return false;
  
  const order = value as Record<string, unknown>;
  
  return (
    isValidObjectId(order._id) &&
    isString(order.orderId) &&
    isValidObjectId(order.userId) &&
    isString(order.clientCode) &&
    isString(order.securityId) &&
    isString(order.ticker) &&
    isNumber(order.quantity) &&
    isNumber(order.price) &&
    isString(order.productType) &&
    isString(order.validity) &&
    isValidOrderType(order.orderType) &&
    isValidBroker(order.broker) &&
    isString(order.status) &&
    (order.adminId === undefined || isValidObjectId(order.adminId))
  );
}

export function isMarketData(value: unknown): value is MarketData {
  if (!isObject(value)) return false;
  
  const data = value as Record<string, unknown>;
  
  return (
    isString(data.ticker) &&
    isString(data.securityId) &&
    isString(data.exchange) &&
    isNumber(data.exchangeCode) &&
    isNumber(data.ltp) &&
    isNumber(data.change) &&
    isNumber(data.changePercent) &&
    isNumber(data.volume) &&
    isNumber(data.high) &&
    isNumber(data.low) &&
    isNumber(data.open) &&
    isNumber(data.close) &&
    isNumber(data.timestamp)
  );
}

export function isBillingRecord(value: unknown): value is BillingRecord {
  if (!isObject(value)) return false;
  
  const record = value as Record<string, unknown>;
  
  return (
    isValidObjectId(record._id) &&
    isValidObjectId(record.userId) &&
    isValidObjectId(record.adminId) &&
    isString(record.orderId) &&
    isNumber(record.amount) &&
    isNumber(record.rate) &&
    isString(record.billingCycle) &&
    isString(record.status) &&
    (record.superAdminId === undefined || isValidObjectId(record.superAdminId))
  );
}

// Utility functions for safe type conversion
export function safeParseInt(value: unknown, defaultValue: number = 0): number {
  if (isNumber(value)) return Math.floor(value);
  if (isString(value)) {
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? defaultValue : parsed;
  }
  return defaultValue;
}

export function safeParseFloat(value: unknown, defaultValue: number = 0): number {
  if (isNumber(value)) return value;
  if (isString(value)) {
    const parsed = parseFloat(value);
    return isNaN(parsed) ? defaultValue : parsed;
  }
  return defaultValue;
}

export function safeParseBoolean(value: unknown, defaultValue: boolean = false): boolean {
  if (isBoolean(value)) return value;
  if (isString(value)) {
    const lower = value.toLowerCase();
    if (lower === 'true' || lower === '1') return true;
    if (lower === 'false' || lower === '0') return false;
  }
  if (isNumber(value)) return value !== 0;
  return defaultValue;
}

export function safeParseDate(value: unknown, defaultValue?: Date): Date {
  if (value instanceof Date) return value;
  if (isString(value) || isNumber(value)) {
    const date = new Date(value);
    return isNaN(date.getTime()) ? (defaultValue || new Date()) : date;
  }
  return defaultValue || new Date();
}

// Array validation helpers
export function validateArray<T>(
  value: unknown,
  itemValidator: (item: unknown) => item is T,
  minLength: number = 0,
  maxLength: number = Infinity
): T[] | null {
  if (!Array.isArray(value)) return null;
  if (value.length < minLength || value.length > maxLength) return null;
  
  const validatedItems: T[] = [];
  for (const item of value) {
    if (!itemValidator(item)) return null;
    validatedItems.push(item);
  }
  
  return validatedItems;
}

// Safe property access
export function safeGet<T>(
  obj: unknown,
  path: string,
  validator: (value: unknown) => value is T,
  defaultValue?: T
): T | undefined {
  if (!isObject(obj)) return defaultValue;
  
  const keys = path.split('.');
  let current: any = obj;
  
  for (const key of keys) {
    if (!isObject(current) || !(key in current)) {
      return defaultValue;
    }
    current = current[key];
  }
  
  return validator(current) ? current : defaultValue;
}

// Environment variable helpers
export function getEnvString(key: string, defaultValue?: string): string {
  const value = process.env[key];
  return isString(value) ? value : (defaultValue || '');
}

export function getEnvNumber(key: string, defaultValue: number = 0): number {
  const value = process.env[key];
  return safeParseFloat(value, defaultValue);
}

export function getEnvBoolean(key: string, defaultValue: boolean = false): boolean {
  const value = process.env[key];
  return safeParseBoolean(value, defaultValue);
}

// Export all type guards and utilities
export default {
  isString,
  isNumber,
  isBoolean,
  isObject,
  isArray,
  isValidObjectId,
  isValidEmail,
  isValidUserRole,
  isValidBroker,
  isValidOrderType,
  isValidProductType,
  isUser,
  isBrokerAccount,
  isOrderResponse,
  isMarketData,
  isBillingRecord,
  safeParseInt,
  safeParseFloat,
  safeParseBoolean,
  safeParseDate,
  validateArray,
  safeGet,
  getEnvString,
  getEnvNumber,
  getEnvBoolean
};
