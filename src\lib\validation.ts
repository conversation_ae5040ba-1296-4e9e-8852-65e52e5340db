// Input validation utilities for enhanced security
import { ValidationError } from './errorHandler';

// Common validation patterns
export const ValidationPatterns = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^\+?[\d\s\-\(\)]{10,}$/,
  alphanumeric: /^[a-zA-Z0-9]+$/,
  alphanumericWithSpaces: /^[a-zA-Z0-9\s]+$/,
  numeric: /^\d+$/,
  decimal: /^\d+(\.\d{1,2})?$/,
  password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
  userCode: /^[A-Z0-9]{6,12}$/,
  clientCode: /^[A-Z0-9]{6,15}$/,
  symbol: /^[A-Z0-9\-]{1,20}$/,
  mongoId: /^[0-9a-fA-F]{24}$/
};

// Sanitization functions
export const sanitize = {
  string: (input: any): string => {
    if (typeof input !== 'string') return '';
    return input.trim().replace(/[<>]/g, ''); // Basic XSS prevention
  },

  email: (input: any): string => {
    const email = sanitize.string(input).toLowerCase();
    if (!ValidationPatterns.email.test(email)) {
      throw new ValidationError('Invalid email format');
    }
    return email;
  },

  number: (input: any, min?: number, max?: number): number => {
    const num = parseFloat(input);
    if (isNaN(num)) {
      throw new ValidationError('Invalid number format');
    }
    if (min !== undefined && num < min) {
      throw new ValidationError(`Number must be at least ${min}`);
    }
    if (max !== undefined && num > max) {
      throw new ValidationError(`Number must be at most ${max}`);
    }
    return num;
  },

  integer: (input: any, min?: number, max?: number): number => {
    const num = parseInt(input);
    if (isNaN(num) || !Number.isInteger(num)) {
      throw new ValidationError('Invalid integer format');
    }
    if (min !== undefined && num < min) {
      throw new ValidationError(`Integer must be at least ${min}`);
    }
    if (max !== undefined && num > max) {
      throw new ValidationError(`Integer must be at most ${max}`);
    }
    return num;
  },

  boolean: (input: any): boolean => {
    if (typeof input === 'boolean') return input;
    if (typeof input === 'string') {
      const lower = input.toLowerCase();
      if (lower === 'true' || lower === '1') return true;
      if (lower === 'false' || lower === '0') return false;
    }
    if (typeof input === 'number') {
      return input !== 0;
    }
    throw new ValidationError('Invalid boolean format');
  },

  mongoId: (input: any): string => {
    const id = sanitize.string(input);
    if (!ValidationPatterns.mongoId.test(id)) {
      throw new ValidationError('Invalid MongoDB ObjectId format');
    }
    return id;
  }
};

// Validation schemas for common objects
export const ValidationSchemas = {
  user: {
    name: (input: any) => {
      const name = sanitize.string(input);
      if (name.length < 2 || name.length > 50) {
        throw new ValidationError('Name must be between 2 and 50 characters');
      }
      if (!ValidationPatterns.alphanumericWithSpaces.test(name)) {
        throw new ValidationError('Name can only contain letters, numbers, and spaces');
      }
      return name;
    },

    email: sanitize.email,

    password: (input: any) => {
      const password = sanitize.string(input);
      if (password.length < 8) {
        throw new ValidationError('Password must be at least 8 characters long');
      }
      if (!ValidationPatterns.password.test(password)) {
        throw new ValidationError('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character');
      }
      return password;
    },

    role: (input: any) => {
      const role = sanitize.string(input).toLowerCase();
      if (!['user', 'admin', 'super_admin'].includes(role)) {
        throw new ValidationError('Invalid role. Must be user, admin, or super_admin');
      }
      return role;
    },

    userCode: (input: any) => {
      const code = sanitize.string(input).toUpperCase();
      if (!ValidationPatterns.userCode.test(code)) {
        throw new ValidationError('User code must be 6-12 alphanumeric characters');
      }
      return code;
    }
  },

  order: {
    symbol: (input: any) => {
      const symbol = sanitize.string(input).toUpperCase();
      if (!ValidationPatterns.symbol.test(symbol)) {
        throw new ValidationError('Invalid symbol format');
      }
      return symbol;
    },

    quantity: (input: any) => {
      return sanitize.integer(input, 1, 10000);
    },

    price: (input: any) => {
      return sanitize.number(input, 0.01, 100000);
    },

    orderType: (input: any) => {
      const type = sanitize.string(input).toUpperCase();
      if (!['BUY', 'SELL'].includes(type)) {
        throw new ValidationError('Order type must be BUY or SELL');
      }
      return type;
    },

    productType: (input: any) => {
      const type = sanitize.string(input).toUpperCase();
      if (!['INTRADAY', 'DELIVERY', 'CNC', 'MIS'].includes(type)) {
        throw new ValidationError('Invalid product type');
      }
      return type;
    },

    broker: (input: any) => {
      const broker = sanitize.string(input).toLowerCase();
      if (!['angel', 'motilal'].includes(broker)) {
        throw new ValidationError('Broker must be angel or motilal');
      }
      return broker;
    },

    clientCode: (input: any) => {
      const code = sanitize.string(input).toUpperCase();
      if (!ValidationPatterns.clientCode.test(code)) {
        throw new ValidationError('Invalid client code format');
      }
      return code;
    }
  },

  pagination: {
    page: (input: any) => {
      return sanitize.integer(input, 1, 1000);
    },

    limit: (input: any) => {
      return sanitize.integer(input, 1, 100);
    }
  }
};

// Validation middleware for API routes
export function validateRequestBody<T>(
  body: any,
  schema: { [K in keyof T]: (input: any) => T[K] }
): T {
  const validated = {} as T;
  const errors: string[] = [];

  for (const [key, validator] of Object.entries(schema)) {
    try {
      validated[key as keyof T] = validator(body[key]);
    } catch (error) {
      if (error instanceof ValidationError) {
        errors.push(`${key}: ${error.message}`);
      } else {
        errors.push(`${key}: Validation failed`);
      }
    }
  }

  if (errors.length > 0) {
    throw new ValidationError(`Validation failed: ${errors.join(', ')}`);
  }

  return validated;
}

// Query parameter validation
export function validateQueryParams(
  url: URL,
  schema: { [key: string]: (input: any) => any }
): any {
  const validated: any = {};
  const errors: string[] = [];

  for (const [key, validator] of Object.entries(schema)) {
    const value = url.searchParams.get(key);
    if (value !== null) {
      try {
        validated[key] = validator(value);
      } catch (error) {
        if (error instanceof ValidationError) {
          errors.push(`${key}: ${error.message}`);
        } else {
          errors.push(`${key}: Validation failed`);
        }
      }
    }
  }

  if (errors.length > 0) {
    throw new ValidationError(`Query validation failed: ${errors.join(', ')}`);
  }

  return validated;
}

// Security headers for API responses
export function getSecurityHeaders(): Record<string, string> {
  return {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' ws: wss:;",
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'
  };
}

export default {
  ValidationPatterns,
  sanitize,
  ValidationSchemas,
  validateRequestBody,
  validateQueryParams,
  getSecurityHeaders
};
