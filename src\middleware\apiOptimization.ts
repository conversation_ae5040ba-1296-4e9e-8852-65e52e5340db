// API optimization middleware for better performance

import { withTiming } from '@/lib/performance';
import { getSecurityHeaders } from '@/lib/validation';

interface OptimizationOptions {
  enableCompression?: boolean;
  enableCaching?: boolean;
  enableTiming?: boolean;
  enableSecurity?: boolean;
  maxAge?: number;
  staleWhileRevalidate?: number;
}

const defaultOptions: OptimizationOptions = {
  enableCompression: true,
  enableCaching: true,
  enableTiming: true,
  enableSecurity: true,
  maxAge: 300, // 5 minutes
  staleWhileRevalidate: 600 // 10 minutes
};

/**
 * Optimized API middleware wrapper
 */
export function withApiOptimization(
  handler: (request: Request) => Promise<Response>,
  options: OptimizationOptions = {}
) {
  const opts = { ...defaultOptions, ...options };

  return async (request: Request): Promise<Response> => {
    const startTime = performance.now();
    const url = new URL(request.url);
    const method = request.method;
    const endpoint = `${method} ${url.pathname}`;

    try {
      let response: Response;

      if (opts.enableTiming) {
        response = await withTiming(endpoint, () => handler(request), {
          method,
          url: url.pathname,
          userAgent: request.headers.get('user-agent') || 'unknown'
        });
      } else {
        response = await handler(request);
      }

      // Clone response to modify headers
      const optimizedResponse = new Response(response.body, {
        status: response.status,
        statusText: response.statusText,
        headers: new Headers(response.headers)
      });

      // Add performance headers
      const duration = performance.now() - startTime;
      optimizedResponse.headers.set('X-Response-Time', `${duration.toFixed(2)}ms`);
      optimizedResponse.headers.set('X-Timestamp', new Date().toISOString());

      // Add security headers
      if (opts.enableSecurity) {
        const securityHeaders = getSecurityHeaders();
        Object.entries(securityHeaders).forEach(([key, value]) => {
          optimizedResponse.headers.set(key, value);
        });
      }

      // Add caching headers for GET requests
      if (opts.enableCaching && method === 'GET' && response.ok) {
        const cacheControl = [
          `max-age=${opts.maxAge}`,
          `stale-while-revalidate=${opts.staleWhileRevalidate}`,
          'public'
        ].join(', ');
        
        optimizedResponse.headers.set('Cache-Control', cacheControl);
        optimizedResponse.headers.set('ETag', generateETag(url.pathname, duration));
      }

      // Add compression hint
      if (opts.enableCompression) {
        optimizedResponse.headers.set('Vary', 'Accept-Encoding');
      }

      // Add CORS headers for API routes
      if (url.pathname.startsWith('/api/')) {
        optimizedResponse.headers.set('Access-Control-Allow-Origin', '*');
        optimizedResponse.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        optimizedResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
      }

      // Log performance metrics
      if (duration > 1000) {
        console.warn(`🐌 Slow API response: ${endpoint} took ${duration.toFixed(2)}ms`);
      } else if (duration < 100) {
        console.log(`⚡ Fast API response: ${endpoint} took ${duration.toFixed(2)}ms`);
      }

      return optimizedResponse;

    } catch (error) {
      const duration = performance.now() - startTime;
      console.error(`❌ API error in ${endpoint} after ${duration.toFixed(2)}ms:`, error);
      
      // Return error response with optimization headers
      const errorResponse = Response.json(
        {
          error: 'Internal server error',
          timestamp: new Date().toISOString(),
          endpoint,
          duration: `${duration.toFixed(2)}ms`
        },
        { status: 500 }
      );

      // Add security headers even for errors
      if (opts.enableSecurity) {
        const securityHeaders = getSecurityHeaders();
        Object.entries(securityHeaders).forEach(([key, value]) => {
          errorResponse.headers.set(key, value);
        });
      }

      return errorResponse;
    }
  };
}

/**
 * Generate ETag for caching
 */
function generateETag(pathname: string, duration: number): string {
  const timestamp = Math.floor(Date.now() / 1000 / 60); // Change every minute
  const hash = btoa(`${pathname}-${timestamp}-${duration.toFixed(0)}`);
  return `"${hash.substring(0, 16)}"`;
}

/**
 * Middleware for handling preflight OPTIONS requests
 */
export function withCORS(handler: (request: Request) => Promise<Response>) {
  return async (request: Request): Promise<Response> => {
    // Handle preflight requests
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          'Access-Control-Max-Age': '86400', // 24 hours
        },
      });
    }

    const response = await handler(request);
    
    // Add CORS headers to actual responses
    const corsResponse = new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: new Headers(response.headers)
    });

    corsResponse.headers.set('Access-Control-Allow-Origin', '*');
    corsResponse.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    corsResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    return corsResponse;
  };
}

/**
 * Middleware for request/response compression
 */
export function withCompression(handler: (request: Request) => Promise<Response>) {
  return async (request: Request): Promise<Response> => {
    const response = await handler(request);
    
    // Check if client accepts compression
    const acceptEncoding = request.headers.get('accept-encoding') || '';
    const supportsGzip = acceptEncoding.includes('gzip');
    const supportsBrotli = acceptEncoding.includes('br');

    if (!supportsGzip && !supportsBrotli) {
      return response;
    }

    // Only compress text-based responses
    const contentType = response.headers.get('content-type') || '';
    const isCompressible = 
      contentType.includes('application/json') ||
      contentType.includes('text/') ||
      contentType.includes('application/javascript') ||
      contentType.includes('application/xml');

    if (!isCompressible) {
      return response;
    }

    // Add compression headers (actual compression would be handled by the server/CDN)
    const compressedResponse = new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: new Headers(response.headers)
    });

    compressedResponse.headers.set('Vary', 'Accept-Encoding');
    
    if (supportsBrotli) {
      compressedResponse.headers.set('Content-Encoding', 'br');
    } else if (supportsGzip) {
      compressedResponse.headers.set('Content-Encoding', 'gzip');
    }

    return compressedResponse;
  };
}

/**
 * Middleware for request body size limiting
 */
export function withBodySizeLimit(
  handler: (request: Request) => Promise<Response>,
  maxSize: number = 10 * 1024 * 1024 // 10MB default
) {
  return async (request: Request): Promise<Response> => {
    const contentLength = request.headers.get('content-length');
    
    if (contentLength && parseInt(contentLength) > maxSize) {
      return Response.json(
        {
          error: 'Request body too large',
          maxSize: `${Math.round(maxSize / 1024 / 1024)}MB`,
          received: `${Math.round(parseInt(contentLength) / 1024 / 1024)}MB`
        },
        { status: 413 }
      );
    }

    return handler(request);
  };
}

/**
 * Middleware for request timeout
 */
export function withTimeout(
  handler: (request: Request) => Promise<Response>,
  timeoutMs: number = 30000 // 30 seconds default
) {
  return async (request: Request): Promise<Response> => {
    const timeoutPromise = new Promise<Response>((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Request timeout after ${timeoutMs}ms`));
      }, timeoutMs);
    });

    try {
      return await Promise.race([
        handler(request),
        timeoutPromise
      ]);
    } catch (error) {
      if (error instanceof Error && error.message.includes('timeout')) {
        return Response.json(
          {
            error: 'Request timeout',
            timeout: `${timeoutMs}ms`
          },
          { status: 408 }
        );
      }
      throw error;
    }
  };
}

/**
 * Combined optimization middleware
 */
export function withFullOptimization(
  handler: (request: Request) => Promise<Response>,
  options: OptimizationOptions & {
    maxBodySize?: number;
    timeoutMs?: number;
  } = {}
) {
  const { maxBodySize = 10 * 1024 * 1024, timeoutMs = 30000, ...optimizationOptions } = options;

  return withApiOptimization(
    withCORS(
      withCompression(
        withBodySizeLimit(
          withTimeout(handler, timeoutMs),
          maxBodySize
        )
      )
    ),
    optimizationOptions
  );
}

export default {
  withApiOptimization,
  withCORS,
  withCompression,
  withBodySizeLimit,
  withTimeout,
  withFullOptimization
};
