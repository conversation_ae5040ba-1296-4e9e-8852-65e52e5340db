import jwt from 'jsonwebtoken';
import User from '@/models/User';
import connectDB from '@/lib/mongodb';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export interface AuthUser {
  userId: string;
  email: string;
  role: 'super_admin' | 'admin' | 'user';
  adminId?: string;
  superAdminId?: string;
  name: string;
  companyName?: string;
}

// Enhanced authentication middleware
export async function verifyAuth(request: Request): Promise<AuthUser | null> {
  try {
    const authHeader = request.headers.get('authorization');
    const cookieToken = request.headers.get('cookie')?.split('auth-token=')[1]?.split(';')[0];
    const token = authHeader?.replace('Bearer ', '') || cookieToken;

    if (!token) {
      console.log('🔐 No token provided');
      return null;
    }

    console.log('🔑 Token received:', token.substring(0, 20) + '...');

    let decoded;
    try {
      decoded = jwt.verify(token, JWT_SECRET) as any;
      console.log('✅ JWT decoded successfully:', { userId: decoded.userId, email: decoded.email });
    } catch (jwtError: any) {
      console.log('❌ JWT verification failed:', jwtError.message);
      if (jwtError.message.includes('malformed')) {
        console.log('🔄 Token appears to be from old JWT secret, user needs to re-login');
      }
      console.log('🔍 Token details:', {
        length: token.length,
        starts: token.substring(0, 10),
        type: typeof token
      });
      return null;
    }
    
    // Get fresh user data from database
    await connectDB();
    const user = await User.findById(decoded.userId).select('-password');
    
    if (!user || !user.isActive) {
      return null;
    }

    return {
      userId: user._id.toString(),
      email: user.email,
      role: user.role,
      adminId: user.adminId?.toString(),
      superAdminId: user.superAdminId?.toString(),
      name: user.name,
      companyName: user.companyName,
    };
  } catch (error) {
    console.error('JWT verification failed:', error);
    return null;
  }
}

// Role-based access control middleware
export function requireRole(allowedRoles: string[]) {
  return async (request: Request): Promise<{ user: AuthUser | null; hasAccess: boolean }> => {
    const user = await verifyAuth(request);
    
    if (!user) {
      return { user: null, hasAccess: false };
    }

    const hasAccess = allowedRoles.includes(user.role);
    return { user, hasAccess };
  };
}

// Super Admin only access
export const requireSuperAdmin = requireRole(['super_admin']);

// Admin and Super Admin access
export const requireAdmin = requireRole(['super_admin', 'admin']);

// All authenticated users
export const requireAuth = requireRole(['super_admin', 'admin', 'user']);

// Check if user can access specific admin's data
export async function canAccessAdminData(currentUser: AuthUser, targetAdminId: string): Promise<boolean> {
  // Super admin can access all data
  if (currentUser.role === 'super_admin') {
    return true;
  }

  // Admin can only access their own data
  if (currentUser.role === 'admin') {
    return currentUser.userId === targetAdminId;
  }

  // Users cannot access admin data
  return false;
}

// Check if user can access specific user's data
export async function canAccessUserData(currentUser: AuthUser, targetUserId: string): Promise<boolean> {
  // Super admin can access all data
  if (currentUser.role === 'super_admin') {
    return true;
  }

  // Admin can access their users' data
  if (currentUser.role === 'admin') {
    await connectDB();
    const targetUser = await User.findById(targetUserId);
    return targetUser?.adminId?.toString() === currentUser.userId;
  }

  // Users can only access their own data
  if (currentUser.role === 'user') {
    return currentUser.userId === targetUserId;
  }

  return false;
}

// Get users under admin's management
export async function getUsersUnderAdmin(adminId: string): Promise<any[]> {
  await connectDB();
  return await User.find({ 
    adminId: adminId, 
    role: 'user',
    isActive: true 
  }).select('-password');
}

// Get admins under super admin's management
export async function getAdminsUnderSuperAdmin(superAdminId: string): Promise<any[]> {
  await connectDB();
  return await User.find({ 
    superAdminId: superAdminId, 
    role: 'admin',
    isActive: true 
  }).select('-password');
}

// Multi-tenant data filtering
export async function filterDataByTenant(currentUser: AuthUser, query: any = {}): Promise<any> {
  if (currentUser.role === 'super_admin') {
    // Super admin sees all data
    return query;
  }

  if (currentUser.role === 'admin') {
    // Admin sees only their users' data
    const userIds = await getUsersUnderAdmin(currentUser.userId);
    const userIdStrings = userIds.map(user => user._id.toString());
    
    return {
      ...query,
      $or: [
        { userId: { $in: userIdStrings } },
        { adminId: currentUser.userId },
      ]
    };
  }

  if (currentUser.role === 'user') {
    // User sees only their own data
    return {
      ...query,
      userId: currentUser.userId
    };
  }

  return { ...query, _id: null }; // Return empty result for unknown roles
}

// Billing access control
export async function canAccessBillingData(currentUser: AuthUser, targetAdminId?: string): Promise<boolean> {
  // Super admin can access all billing data
  if (currentUser.role === 'super_admin') {
    return true;
  }

  // Admin can only access their own billing data
  if (currentUser.role === 'admin') {
    return !targetAdminId || currentUser.userId === targetAdminId;
  }

  // Users cannot access billing data
  return false;
}

// Generate invitation token
export function generateInvitationToken(): string {
  return jwt.sign(
    { 
      type: 'invitation',
      timestamp: Date.now(),
      random: Math.random().toString(36).substring(7)
    },
    JWT_SECRET,
    { expiresIn: '7d' }
  );
}

// Verify invitation token
export function verifyInvitationToken(token: string): any {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as any;
    if (decoded.type !== 'invitation') {
      throw new Error('Invalid token type');
    }
    return decoded;
  } catch (error) {
    throw new Error('Invalid or expired invitation token');
  }
}

// Helper function to get current billing cycle
export function getCurrentBillingCycle(): string {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  return `${year}-${month}`;
}

// Helper function to get previous billing cycle
export function getPreviousBillingCycle(): string {
  const now = new Date();
  const prevMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
  const year = prevMonth.getFullYear();
  const month = String(prevMonth.getMonth() + 1).padStart(2, '0');
  return `${year}-${month}`;
}

// Helper function to check if user is in testing mode
export function isTestingMode(): boolean {
  return process.env.NODE_ENV === 'development' || process.env.TESTING_MODE === 'true';
}

// Testing fallback for development
export async function getTestUser(): Promise<AuthUser> {
  return {
    userId: 'test-user',
    email: '<EMAIL>',
    role: 'super_admin',
    name: 'Test User',
    companyName: 'Test Company'
  };
}
