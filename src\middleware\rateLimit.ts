// Rate limiting middleware for API routes
interface RateLimitEntry {
  count: number;
  resetTime: number;
}

class RateLimiter {
  private requests = new Map<string, RateLimitEntry>();
  private cleanupInterval: NodeJS.Timeout;

  constructor() {
    // Clean up expired entries every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  private cleanup() {
    const now = Date.now();
    for (const [key, entry] of this.requests.entries()) {
      if (now > entry.resetTime) {
        this.requests.delete(key);
      }
    }
  }

  private getClientKey(request: Request): string {
    // Try to get IP from various headers
    const forwarded = request.headers.get('x-forwarded-for');
    const realIp = request.headers.get('x-real-ip');
    const cfConnectingIp = request.headers.get('cf-connecting-ip');
    
    const ip = forwarded?.split(',')[0] || realIp || cfConnectingIp || 'unknown';
    
    // Also include user agent for better identification
    const userAgent = request.headers.get('user-agent') || 'unknown';
    
    return `${ip}:${userAgent.substring(0, 50)}`;
  }

  checkLimit(
    request: Request,
    maxRequests: number = 100,
    windowMs: number = 15 * 60 * 1000 // 15 minutes
  ): { allowed: boolean; remaining: number; resetTime: number } {
    const key = this.getClientKey(request);
    const now = Date.now();
    const resetTime = now + windowMs;

    const entry = this.requests.get(key);

    if (!entry || now > entry.resetTime) {
      // First request or window expired
      this.requests.set(key, { count: 1, resetTime });
      return { allowed: true, remaining: maxRequests - 1, resetTime };
    }

    if (entry.count >= maxRequests) {
      // Rate limit exceeded
      return { allowed: false, remaining: 0, resetTime: entry.resetTime };
    }

    // Increment count
    entry.count++;
    this.requests.set(key, entry);

    return { 
      allowed: true, 
      remaining: maxRequests - entry.count, 
      resetTime: entry.resetTime 
    };
  }

  // Get current stats
  getStats() {
    return {
      totalClients: this.requests.size,
      entries: Array.from(this.requests.entries()).map(([key, entry]) => ({
        client: key.substring(0, 20) + '...',
        count: entry.count,
        resetTime: new Date(entry.resetTime).toISOString()
      }))
    };
  }

  // Clear all entries
  clear() {
    this.requests.clear();
  }

  // Destroy the rate limiter
  destroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.clear();
  }
}

// Global rate limiter instance
const rateLimiter = new RateLimiter();

// Rate limiting configurations for different endpoints
export const rateLimitConfigs = {
  // Authentication endpoints - stricter limits
  auth: { maxRequests: 10, windowMs: 15 * 60 * 1000 }, // 10 requests per 15 minutes
  
  // Order placement - moderate limits
  orders: { maxRequests: 50, windowMs: 15 * 60 * 1000 }, // 50 requests per 15 minutes
  
  // Data fetching - more lenient
  data: { maxRequests: 200, windowMs: 15 * 60 * 1000 }, // 200 requests per 15 minutes
  
  // Admin operations - moderate limits
  admin: { maxRequests: 100, windowMs: 15 * 60 * 1000 }, // 100 requests per 15 minutes
  
  // Default for other endpoints
  default: { maxRequests: 100, windowMs: 15 * 60 * 1000 } // 100 requests per 15 minutes
};

// Rate limiting middleware function
export function withRateLimit(
  handler: (request: Request) => Promise<Response>,
  config: { maxRequests: number; windowMs: number } = rateLimitConfigs.default
) {
  return async (request: Request): Promise<Response> => {
    const { allowed, remaining, resetTime } = rateLimiter.checkLimit(
      request,
      config.maxRequests,
      config.windowMs
    );

    if (!allowed) {
      return Response.json(
        {
          error: 'Rate limit exceeded',
          message: `Too many requests. Try again after ${new Date(resetTime).toISOString()}`,
          retryAfter: Math.ceil((resetTime - Date.now()) / 1000)
        },
        {
          status: 429,
          headers: {
            'X-RateLimit-Limit': config.maxRequests.toString(),
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': resetTime.toString(),
            'Retry-After': Math.ceil((resetTime - Date.now()) / 1000).toString()
          }
        }
      );
    }

    // Add rate limit headers to successful responses
    const response = await handler(request);
    
    // Clone response to add headers
    const newResponse = new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        ...Object.fromEntries(response.headers.entries()),
        'X-RateLimit-Limit': config.maxRequests.toString(),
        'X-RateLimit-Remaining': remaining.toString(),
        'X-RateLimit-Reset': resetTime.toString()
      }
    });

    return newResponse;
  };
}

// Specific rate limiting functions for different endpoint types
export const withAuthRateLimit = (handler: (request: Request) => Promise<Response>) =>
  withRateLimit(handler, rateLimitConfigs.auth);

export const withOrderRateLimit = (handler: (request: Request) => Promise<Response>) =>
  withRateLimit(handler, rateLimitConfigs.orders);

export const withDataRateLimit = (handler: (request: Request) => Promise<Response>) =>
  withRateLimit(handler, rateLimitConfigs.data);

export const withAdminRateLimit = (handler: (request: Request) => Promise<Response>) =>
  withRateLimit(handler, rateLimitConfigs.admin);

// Export the rate limiter instance for stats and management
export { rateLimiter };
export default rateLimiter;
