import mongoose from 'mongoose';

export interface IActivityLog extends mongoose.Document {
  superAdminId: mongoose.Types.ObjectId;
  adminId?: mongoose.Types.ObjectId;
  userId?: mongoose.Types.ObjectId;
  action: string;
  category: 'admin_management' | 'user_management' | 'billing' | 'orders' | 'system' | 'authentication';
  description: string;
  details?: any;
  ipAddress?: string;
  userAgent?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'success' | 'failed' | 'pending';
  createdAt: Date;
  updatedAt: Date;
}

const activityLogSchema = new mongoose.Schema({
  superAdminId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Super Admin ID is required'],
    index: true,
  },
  adminId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    index: true,
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    index: true,
  },
  action: {
    type: String,
    required: [true, 'Action is required'],
    index: true,
  },
  category: {
    type: String,
    enum: ['admin_management', 'user_management', 'billing', 'orders', 'system', 'authentication'],
    required: [true, 'Category is required'],
    index: true,
  },
  description: {
    type: String,
    required: [true, 'Description is required'],
  },
  details: {
    type: mongoose.Schema.Types.Mixed,
  },
  ipAddress: {
    type: String,
  },
  userAgent: {
    type: String,
  },
  severity: {
    type: String,
    enum: ['low', 'medium', 'high', 'critical'],
    default: 'low',
    index: true,
  },
  status: {
    type: String,
    enum: ['success', 'failed', 'pending'],
    default: 'success',
    index: true,
  }
}, {
  timestamps: true,
});

// Indexes for better performance
activityLogSchema.index({ superAdminId: 1, createdAt: -1 });
activityLogSchema.index({ adminId: 1, createdAt: -1 });
activityLogSchema.index({ category: 1, createdAt: -1 });
activityLogSchema.index({ severity: 1, createdAt: -1 });
activityLogSchema.index({ action: 1, createdAt: -1 });

// TTL index to automatically delete logs older than 1 year
activityLogSchema.index({ createdAt: 1 }, { expireAfterSeconds: 365 * 24 * 60 * 60 });

export default mongoose.models.ActivityLog || mongoose.model<IActivityLog>('ActivityLog', activityLogSchema);
