import mongoose from 'mongoose';

export interface IAngelUser extends mongoose.Document {
  userId: string;
  password: string;
  apiKey: string;
  totpKey: string;
  clientName: string;
  email: string;
  phoneNumber: string;
  jwtToken?: string;
  refreshToken?: string;
  feedToken?: string;
  state: 'live' | 'inactive';
  capital: number;
  owner: mongoose.Types.ObjectId; // Reference to the User who owns this Angel account
  createdAt: Date;
  updatedAt: Date;
}

const angelUserSchema = new mongoose.Schema({
  userId: {
    type: String,
    required: [true, 'User ID is required'],
    trim: true,
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
  },
  apiKey: {
    type: String,
    required: [true, 'API Key is required'],
  },
  totpKey: {
    type: String,
    required: [true, 'TOTP Key is required'],
  },
  clientName: {
    type: String,
    required: [true, 'Client Name is required'],
    trim: true,
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    lowercase: true,
    trim: true,
  },
  phoneNumber: {
    type: String,
    required: [true, 'Phone Number is required'],
    trim: true,
  },
  jwtToken: {
    type: String,
    default: null,
  },
  refreshToken: {
    type: String,
    default: null,
  },
  feedToken: {
    type: String,
    default: null,
  },
  state: {
    type: String,
    enum: ['live', 'inactive'],
    default: 'live',
  },
  capital: {
    type: Number,
    required: [true, 'Capital is required'],
    min: [0, 'Capital must be positive'],
  },
  owner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Owner is required'],
  },
}, {
  timestamps: true,
});

// Index for better query performance
angelUserSchema.index({ owner: 1, state: 1 });
angelUserSchema.index({ userId: 1 });

export default mongoose.models.AngelUser || mongoose.model<IAngelUser>('AngelUser', angelUserSchema);
