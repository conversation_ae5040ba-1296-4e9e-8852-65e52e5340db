import mongoose from 'mongoose';

export interface IBatchOrderHistory extends mongoose.Document {
  batchId: string;
  batchName: string;
  symbol: string;
  orderType: 'BUY' | 'SELL';
  quantity: number;
  price: number;
  productType: string;
  validity: string;
  variety: string;
  exchange: string;
  totalAccounts: number;
  successfulOrders: number;
  failedOrders: number;
  results: Array<{
    account: string;
    broker: 'angel' | 'motilal';
    success: boolean;
    orderId?: string;
    message?: string;
    error?: string;
  }>;
  owner: mongoose.Types.ObjectId;
  executedAt: Date;
  createdAt: Date;
  updatedAt: Date;
}

const batchOrderHistorySchema = new mongoose.Schema({
  batchId: {
    type: String,
    required: [true, 'Batch ID is required']
  },
  batchName: {
    type: String,
    required: [true, 'Batch name is required']
  },
  symbol: {
    type: String,
    required: [true, 'Symbol is required']
  },
  orderType: {
    type: String,
    enum: ['BUY', 'SELL'],
    required: [true, 'Order type is required']
  },
  quantity: {
    type: Number,
    required: [true, 'Quantity is required'],
    min: [1, 'Quantity must be at least 1']
  },
  price: {
    type: Number,
    required: [true, 'Price is required'],
    min: [0, 'Price must be non-negative']
  },
  productType: {
    type: String,
    required: [true, 'Product type is required']
  },
  validity: {
    type: String,
    required: [true, 'Validity is required']
  },
  variety: {
    type: String,
    required: [true, 'Variety is required']
  },
  exchange: {
    type: String,
    required: [true, 'Exchange is required']
  },
  totalAccounts: {
    type: Number,
    required: [true, 'Total accounts is required'],
    min: [1, 'Total accounts must be at least 1']
  },
  successfulOrders: {
    type: Number,
    required: [true, 'Successful orders count is required'],
    min: [0, 'Successful orders must be non-negative']
  },
  failedOrders: {
    type: Number,
    required: [true, 'Failed orders count is required'],
    min: [0, 'Failed orders must be non-negative']
  },
  results: [{
    account: {
      type: String,
      required: true
    },
    broker: {
      type: String,
      enum: ['angel', 'motilal'],
      required: true
    },
    success: {
      type: Boolean,
      required: true
    },
    orderId: {
      type: String
    },
    message: {
      type: String
    },
    error: {
      type: String
    }
  }],
  owner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Owner is required']
  },
  executedAt: {
    type: Date,
    required: [true, 'Execution time is required']
  }
}, {
  timestamps: true
});

// Index for better query performance
batchOrderHistorySchema.index({ owner: 1, executedAt: -1 });
batchOrderHistorySchema.index({ batchId: 1, executedAt: -1 });
batchOrderHistorySchema.index({ symbol: 1, executedAt: -1 });

export default mongoose.models.BatchOrderHistory || mongoose.model<IBatchOrderHistory>('BatchOrderHistory', batchOrderHistorySchema);
