import mongoose from 'mongoose';

// Billing Record Interface
export interface IBillingRecord extends mongoose.Document {
  adminId: mongoose.Types.ObjectId; // Reference to admin
  superAdminId: mongoose.Types.ObjectId; // Reference to super admin
  orderId: string; // Order ID from broker
  orderType: 'BUY' | 'SELL';
  symbol: string;
  quantity: number;
  price: number;
  broker: 'angel' | 'motilal';
  userId: mongoose.Types.ObjectId; // Reference to user who placed order
  clientId: string; // Broker client ID
  orderRate: number; // Rate charged for this order
  amount: number; // Amount to be charged (orderRate * 1)
  status: 'pending' | 'billed' | 'paid' | 'disputed';
  billingCycle: string; // Format: YYYY-MM (e.g., "2024-07")
  createdAt: Date;
  updatedAt: Date;
}

// Billing Summary Interface (monthly summary for each admin)
export interface IBillingSummary extends mongoose.Document {
  adminId: mongoose.Types.ObjectId;
  superAdminId: mongoose.Types.ObjectId;
  billingCycle: string; // Format: YYYY-MM
  totalOrders: number;
  totalAmount: number;
  orderRate: number; // Rate during this billing cycle
  status: 'draft' | 'generated' | 'sent' | 'paid' | 'overdue';
  generatedAt?: Date;
  sentAt?: Date;
  paidAt?: Date;
  dueDate?: Date;
  paymentMethod?: string;
  paymentReference?: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Payment Record Interface
export interface IPaymentRecord extends mongoose.Document {
  adminId: mongoose.Types.ObjectId;
  superAdminId: mongoose.Types.ObjectId;
  billingSummaryId: mongoose.Types.ObjectId;
  amount: number;
  paymentMethod: 'bank_transfer' | 'upi' | 'card' | 'cash' | 'adjustment';
  paymentReference: string;
  paymentDate: Date;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Billing Record Schema
const billingRecordSchema = new mongoose.Schema({
  adminId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Admin ID is required'],
    index: true,
  },
  superAdminId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Super Admin ID is required'],
    index: true,
  },
  orderId: {
    type: String,
    required: [true, 'Order ID is required'],
    // Index defined separately below with unique constraint
  },
  orderType: {
    type: String,
    enum: ['BUY', 'SELL'],
    required: [true, 'Order type is required'],
  },
  symbol: {
    type: String,
    required: [true, 'Symbol is required'],
    index: true,
  },
  quantity: {
    type: Number,
    required: [true, 'Quantity is required'],
    min: [1, 'Quantity must be positive'],
  },
  price: {
    type: Number,
    required: [true, 'Price is required'],
    min: [0, 'Price must be positive'],
  },
  broker: {
    type: String,
    enum: ['angel', 'motilal'],
    required: [true, 'Broker is required'],
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required'],
    index: true,
  },
  clientId: {
    type: String,
    required: [true, 'Client ID is required'],
  },
  orderRate: {
    type: Number,
    required: [true, 'Order rate is required'],
    min: [0, 'Order rate must be positive'],
  },
  amount: {
    type: Number,
    required: [true, 'Amount is required'],
    min: [0, 'Amount must be positive'],
  },
  status: {
    type: String,
    enum: ['pending', 'billed', 'paid', 'disputed'],
    default: 'pending',
  },
  billingCycle: {
    type: String,
    required: [true, 'Billing cycle is required'],
    index: true,
  },
}, {
  timestamps: true,
});

// Billing Summary Schema
const billingSummarySchema = new mongoose.Schema({
  adminId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Admin ID is required'],
    index: true,
  },
  superAdminId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Super Admin ID is required'],
    index: true,
  },
  billingCycle: {
    type: String,
    required: [true, 'Billing cycle is required'],
    index: true,
  },
  totalOrders: {
    type: Number,
    required: [true, 'Total orders is required'],
    min: [0, 'Total orders must be positive'],
  },
  totalAmount: {
    type: Number,
    required: [true, 'Total amount is required'],
    min: [0, 'Total amount must be positive'],
  },
  orderRate: {
    type: Number,
    required: [true, 'Order rate is required'],
    min: [0, 'Order rate must be positive'],
  },
  status: {
    type: String,
    enum: ['draft', 'generated', 'sent', 'paid', 'overdue'],
    default: 'draft',
  },
  generatedAt: Date,
  sentAt: Date,
  paidAt: Date,
  dueDate: Date,
  paymentMethod: String,
  paymentReference: String,
  notes: String,
}, {
  timestamps: true,
});

// Payment Record Schema
const paymentRecordSchema = new mongoose.Schema({
  adminId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Admin ID is required'],
    index: true,
  },
  superAdminId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Super Admin ID is required'],
    index: true,
  },
  billingSummaryId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'BillingSummary',
    required: [true, 'Billing summary ID is required'],
  },
  amount: {
    type: Number,
    required: [true, 'Amount is required'],
    min: [0, 'Amount must be positive'],
  },
  paymentMethod: {
    type: String,
    enum: ['bank_transfer', 'upi', 'card', 'cash', 'adjustment'],
    required: [true, 'Payment method is required'],
  },
  paymentReference: {
    type: String,
    required: [true, 'Payment reference is required'],
  },
  paymentDate: {
    type: Date,
    required: [true, 'Payment date is required'],
  },
  status: {
    type: String,
    enum: ['pending', 'completed', 'failed', 'refunded'],
    default: 'pending',
  },
  notes: String,
}, {
  timestamps: true,
});

// Indexes for better performance
billingRecordSchema.index({ adminId: 1, billingCycle: 1 });
billingRecordSchema.index({ superAdminId: 1, billingCycle: 1 });
billingRecordSchema.index({ userId: 1, createdAt: -1 });
billingRecordSchema.index({ orderId: 1 }, { unique: true });

billingSummarySchema.index({ adminId: 1, billingCycle: 1 }, { unique: true });
billingSummarySchema.index({ superAdminId: 1, status: 1 });

paymentRecordSchema.index({ adminId: 1, paymentDate: -1 });
paymentRecordSchema.index({ superAdminId: 1, status: 1 });

// Create models
export const BillingRecord = mongoose.models.BillingRecord || mongoose.model<IBillingRecord>('BillingRecord', billingRecordSchema);
export const BillingSummary = mongoose.models.BillingSummary || mongoose.model<IBillingSummary>('BillingSummary', billingSummarySchema);
export const PaymentRecord = mongoose.models.PaymentRecord || mongoose.model<IPaymentRecord>('PaymentRecord', paymentRecordSchema);
