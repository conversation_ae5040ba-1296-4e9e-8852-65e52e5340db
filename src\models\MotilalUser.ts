import mongoose from 'mongoose';

export interface IMotilalUser extends mongoose.Document {
  userId: string;
  password: string;
  apiKey: string;
  twoFA: string;
  totpKey: string;
  clientName: string;
  email: string;
  phoneNumber: string;
  authToken?: string;
  capital: number;
  status: 'active' | 'inactive';
  owner: mongoose.Types.ObjectId; // Reference to the User who owns this Motilal account
  createdAt: Date;
  updatedAt: Date;
}

const motilalUserSchema = new mongoose.Schema({
  userId: {
    type: String,
    required: [true, 'User ID is required'],
    trim: true,
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
  },
  apiKey: {
    type: String,
    required: [true, 'API Key is required'],
  },
  twoFA: {
    type: String,
    required: [true, '2FA is required'],
  },
  totpKey: {
    type: String,
    required: [true, 'TOTP Key is required'],
  },
  clientName: {
    type: String,
    required: [true, 'Client Name is required'],
    trim: true,
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    lowercase: true,
    trim: true,
  },
  phoneNumber: {
    type: String,
    required: [true, 'Phone Number is required'],
    trim: true,
  },
  authToken: {
    type: String,
    default: null,
  },
  capital: {
    type: Number,
    required: [true, 'Capital is required'],
    min: [0, 'Capital must be positive'],
  },
  status: {
    type: String,
    enum: ['active', 'inactive'],
    default: 'active',
  },
  owner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Owner is required'],
  },
}, {
  timestamps: true,
});

// Index for better query performance
motilalUserSchema.index({ owner: 1, status: 1 });
motilalUserSchema.index({ userId: 1 });

export default mongoose.models.MotilalUser || mongoose.model<IMotilalUser>('MotilalUser', motilalUserSchema);
