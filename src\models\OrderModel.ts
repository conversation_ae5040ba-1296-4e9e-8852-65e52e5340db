import mongoose from 'mongoose';

// Order Response Schema (based on epicrisenew/models/neworder.js)
const orderResponseSchema = new mongoose.Schema({
  clientId: { type: String, required: true }, // Client identifier
  orderType: { type: String, required: true }, // Buy/Sell, etc.
  strategyName: { type: String, required: true }, // Name of the trading strategy
  details: {
    status: { type: Boolean, required: true }, // Order status (true = success)
    message: { type: String }, // Response message
    script: { type: String }, // Script symbol (e.g., WELCORP-EQ)
    orderid: { type: String }, // Broker's order ID
    uniqueorderid: { type: String }, // Unique order identifier
    response: { type: mongoose.Schema.Types.Mixed }, // Complete broker response
    apiKey: { type: String }, // Client's API key (optional)
    jwtToken: { type: String }, // Client's JWT token (optional)
  },
  broker: { type: String, required: true }, // Broker name
  symboltoken: { type: String, required: true }, // Symbol token for the order
  createdAt: { type: Date, default: Date.now }, // Timestamp of record creation
});

// Simple Order Schema (based on epicrisenew/models/orderModel.js)
const simpleOrderSchema = new mongoose.Schema(
  {
    token: { type: String, required: true },
    symbol: { type: String, required: true },
    transactionType: { type: String, required: true },
    message: { type: String, required: true },
    price: { type: Number, required: true },
    createdAt: { type: Date, default: Date.now },
  },
  {
    timestamps: true, // Automatically add `createdAt` and `updatedAt` fields
  }
);

// Create models
const OrderResponse = mongoose.models.OrderResponse || mongoose.model('OrderResponse', orderResponseSchema);
const SimpleOrder = mongoose.models.SimpleOrder || mongoose.model('SimpleOrder', simpleOrderSchema);

export { OrderResponse, SimpleOrder };
