import mongoose from 'mongoose';

// TotalScript Schema for symbol token mapping (based on Angel_api database)
const totalScriptSchema = new mongoose.Schema({
  token: { type: String, required: true }, // Security ID / Symbol Token
  symbol: { type: String, required: true }, // Trading Symbol (e.g., SBIN-EQ)
  name: { type: String, required: true }, // Company Name (e.g., SBIN)
  expiry: { type: String }, // Expiry date for derivatives
  strike: { type: String }, // Strike price for options
  lotsize: { type: String }, // Lot size
  instrumenttype: { type: String }, // Instrument type
  exch_seg: { type: String, required: true }, // Exchange segment (NSE, BSE, etc.)
  tick_size: { type: String }, // Tick size
}, {
  timestamps: false,
  collection: 'totalscript' // Use the same collection name as in Angel_api
});

// Index for better query performance
totalScriptSchema.index({ token: 1 });
totalScriptSchema.index({ symbol: 1 });
totalScriptSchema.index({ name: 1, exch_seg: 1 });
totalScriptSchema.index({ exch_seg: 1, symbol: 1 });

const TotalScript = mongoose.models.TotalScript || mongoose.model('TotalScript', totalScriptSchema);

export default TotalScript;
