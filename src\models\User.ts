import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';

export interface IUser extends mongoose.Document {
  email: string;
  password: string;
  name: string;
  role: 'super_admin' | 'admin' | 'user';
  isActive: boolean;
  // Multi-tenant fields
  adminId?: mongoose.Types.ObjectId; // Reference to admin (for users only)
  superAdminId?: mongoose.Types.ObjectId; // Reference to super admin (for admins only)
  // Billing fields (for admins)
  orderRate?: number; // Rate per order (for admins, set by super admin)
  totalOrders?: number; // Total orders placed by this admin's users
  totalBilling?: number; // Total amount owed by this admin
  lastBillingDate?: Date; // Last billing calculation date
  // Company/Organization info
  companyName?: string;
  phoneNumber?: string;
  address?: string;
  // Invitation system
  invitedBy?: mongoose.Types.ObjectId; // Who invited this user
  invitationToken?: string; // Token for invitation acceptance
  invitationExpiry?: Date; // When invitation expires
  isInvitationAccepted?: boolean; // Whether invitation was accepted
  createdAt: Date;
  updatedAt: Date;
  comparePassword(candidatePassword: string): Promise<boolean>;
}

const userSchema = new mongoose.Schema({
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    trim: true,
  },
  userCode: {
    type: String,
    unique: true,
    sparse: true, // Allow null values but ensure uniqueness when present
    uppercase: true,
    trim: true,
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [6, 'Password must be at least 6 characters'],
  },
  name: {
    type: String,
    required: [true, 'Name is required'],
    trim: true,
  },
  role: {
    type: String,
    enum: ['super_admin', 'admin', 'user'],
    default: 'user',
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  // Multi-tenant fields
  adminId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: function(this: any) { return this.role === 'user'; }, // Required for users only
  },
  superAdminId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: function(this: any) { return this.role === 'admin'; }, // Required for admins only
  },
  // Billing fields (for admins)
  orderRate: {
    type: Number,
    default: 0,
    min: [0, 'Order rate must be positive'],
  },
  totalOrders: {
    type: Number,
    default: 0,
    min: [0, 'Total orders must be positive'],
  },
  totalBilling: {
    type: Number,
    default: 0,
    min: [0, 'Total billing must be positive'],
  },
  lastBillingDate: {
    type: Date,
    default: Date.now,
  },
  // Company/Organization info
  companyName: {
    type: String,
    trim: true,
  },
  phoneNumber: {
    type: String,
    trim: true,
  },
  address: {
    type: String,
    trim: true,
  },
  // Invitation system
  invitedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  invitationToken: {
    type: String,
    unique: true,
    sparse: true, // Allow multiple null values
  },
  invitationExpiry: {
    type: Date,
  },
  isInvitationAccepted: {
    type: Boolean,
    default: false,
  },

  // User OTP for client portal access
  userOtp: {
    type: String,
    maxlength: 6,
  },
}, {
  timestamps: true,
});

// Hash password before saving
userSchema.pre('save', async function (next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error: any) {
    next(error);
  }
});

// Compare password method
userSchema.methods.comparePassword = async function (candidatePassword: string): Promise<boolean> {
  return bcrypt.compare(candidatePassword, this.password);
};

// Remove password from JSON output
userSchema.methods.toJSON = function () {
  const userObject = this.toObject();
  delete userObject.password;
  return userObject;
};

export default mongoose.models.User || mongoose.model<IUser>('User', userSchema);
