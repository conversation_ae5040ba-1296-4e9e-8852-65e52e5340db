import mongoose from 'mongoose';

export interface IUserBatch extends mongoose.Document {
  name: string;
  description: string;
  accounts: Array<{
    id: string;
    clientCode: string;
    broker: 'angel' | 'motilal';
    isActive: boolean;
    clientName?: string;
    capital?: number;
  }>;
  owner: mongoose.Types.ObjectId;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const userBatchSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Batch name is required'],
    trim: true,
    maxlength: [100, 'Batch name cannot exceed 100 characters']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters'],
    default: ''
  },
  accounts: [{
    id: {
      type: String,
      required: true
    },
    clientCode: {
      type: String,
      required: true
    },
    broker: {
      type: String,
      enum: ['angel', 'motilal'],
      required: true
    },
    isActive: {
      type: Boolean,
      default: true
    },
    clientName: {
      type: String
    },
    capital: {
      type: Number
    }
  }],
  owner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Owner is required']
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Index for better query performance
userBatchSchema.index({ owner: 1, isActive: 1 });
userBatchSchema.index({ owner: 1, name: 1 }, { unique: true });

export default mongoose.models.UserBatch || mongoose.model<IUserBatch>('UserBatch', userBatchSchema);
