import { createServer } from "http";
import { Server } from "socket.io";
import { config } from "dotenv";
import { readFileSync, existsSync, mkdirSync } from "fs";
import { join, extname } from "path";
import { updateStats } from "../lib/stats";
import WebSocket from "ws";
import express, { Request } from "express";
import multer, { FileFilterCallback } from "multer";
import cors from "cors";
import { errorHandler, notFoundHandler } from "../middleware/error";
import * as xlsx from "xlsx";

// Load environment variables
config();

// Create uploads directory if it doesn't exist
const uploadsDir = join(process.cwd(), "uploads");
if (!existsSync(uploadsDir)) {
  mkdirSync(uploadsDir, { recursive: true });
}

const MARKET_FEED_URL = "wss://api-feed.dhan.co";

const EXCHANGE_SEGMENTS = {
  IDX_I: 0,
  NSE_EQ: 1,
  NSE_FNO: 2,
  NSE_CURRENCY: 3,
  BSE_EQ: 4,
  MCX_COMM: 5,
  BSE_CURRENCY: 7,
  BSE_FNO: 8,
} as const;

const SUBSCRIPTION_TYPES = {
  ticker: 15,
  quote: 17,
  full: 21,
} as const;

interface MarketData {
  ticker: string;
  securityId: string;
  exchange: string;
  exchangeCode: number;
  ltp: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  close: number;
  timestamp: number;
}

interface Instrument {
  ticker: string;
  exchange: keyof typeof EXCHANGE_SEGMENTS;
  exchangeCode: number;
  securityId: string;
}

interface PreviousCloseData {
  previousClose: number;
  previousOI: number;
}

class DhanMarketFeedServer {
  private accessToken: string;
  private clientId: string;
  private subscriptionType: string;
  private instruments: Instrument[];

  private io: Server;
  private app: express.Application;
  private server: any;
  private upload: multer.Multer;
  private marketData: Map<string, MarketData>;
  private previousCloseData: Map<string, PreviousCloseData>;
  private ws: WebSocket | null;
  private isConnected: boolean;
  private mockDataInterval: NodeJS.Timeout | null;

  // Batching for performance optimization
  private updateBatch: MarketData[] = [];
  private batchTimeout: NodeJS.Timeout | null = null;
  private readonly BATCH_SIZE = 50;
  private readonly BATCH_TIMEOUT = 100; // ms

  constructor() {
    this.accessToken = process.env.ACCESS_TOKEN?.trim() || "";
    this.clientId = process.env.CLIENT_ID?.trim() || "";
    this.subscriptionType = process.env.SUBSCRIPTION_TYPE?.trim() || "quote";
    this.instruments = [];

    this.marketData = new Map();
    this.previousCloseData = new Map();
    this.ws = null;
    this.isConnected = false;
    this.mockDataInterval = null;
    this.updateBatch = [];
    this.batchTimeout = null;

    // Express and Socket.IO setup
    this.app = express();
    this.server = createServer(this.app);
    this.io = new Server(this.server, {
      cors: {
        origin: "*",
        methods: ["GET", "POST"],
      },
      transports: ["websocket", "polling"],
      allowEIO3: true,
      pingTimeout: 60000,
      pingInterval: 25000,
      upgradeTimeout: 30000,
      maxHttpBufferSize: 1e6,
      allowRequest: (req, callback) => {
        // Allow all requests but log them
        console.log(`📡 WebSocket connection attempt from: ${req.headers.origin || 'unknown'}`);
        callback(null, true);
      }
    });

    // Setup multer for file uploads
    this.upload = multer({
      dest: "uploads/",
      fileFilter: (
        _req: Request,
        file: Express.Multer.File,
        cb: FileFilterCallback
      ) => {
        const ext = extname(file.originalname).toLowerCase();
        if (ext === ".xlsx" || ext === ".xls") {
          cb(null, true);
        } else {
          cb(new Error("Only Excel files are allowed!"));
        }
      },
    });

    this.loadInstrumentsFromExcel();
    this.setupWebServer();
    this.validateConfig();
  }

  private loadInstrumentsFromExcel() {
    try {
      // Try multiple possible locations for the Excel file
      const possiblePaths = [
        join(process.cwd(), "webdata.xlsx"),
        join(process.cwd(), "data", "webdata.xlsx"),
        join(process.cwd(), "..", "webdata.xlsx"),
        process.env.EXCEL_FILE_PATH || "",
      ].filter(Boolean);

      let excelPath = "";
      for (const path of possiblePaths) {
        if (existsSync(path)) {
          excelPath = path;
          break;
        }
      }

      if (!excelPath) {
        console.log(
          "Excel file not found in any of the expected locations. Using default instruments."
        );
        this.setDefaultInstruments();
        return;
      }

      console.log(`Loading instruments from: ${excelPath}`);

      // This check is redundant now, but keeping for safety
      if (!existsSync(excelPath)) {
        this.setDefaultInstruments();
        return;
      }

      const workbook = xlsx.readFile(excelPath);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const data = xlsx.utils.sheet_to_json(worksheet);

      const instruments: Instrument[] = [];
      let validCount = 0;
      let invalidCount = 0;

      data.forEach((row: any, index: number) => {
        try {
          const ticker = row.Ticker || row.ticker || row.TICKER;
          const securityId =
            row.SecurityId ||
            row.securityId ||
            row.SecurityID ||
            row.SECURITYID;
          const exchangeSegment =
            row.ExchangeSegment ||
            row.exchangeSegment ||
            row.Exchange ||
            row.EXCHANGE;

          if (!securityId || !exchangeSegment) {
            invalidCount++;
            return;
          }

          let standardExchange = exchangeSegment;
          if (exchangeSegment === "1" || exchangeSegment === 1) {
            standardExchange = "NSE_EQ";
          } else if (exchangeSegment === "2" || exchangeSegment === 2) {
            standardExchange = "NSE_FNO";
          } else if (exchangeSegment === "3" || exchangeSegment === 3) {
            standardExchange = "NSE_CURRENCY";
          } else if (exchangeSegment === "4" || exchangeSegment === 4) {
            standardExchange = "MCX_COMM";
          }

          if (!(standardExchange in EXCHANGE_SEGMENTS)) {
            invalidCount++;
            return;
          }

          instruments.push({
            ticker: ticker || `${standardExchange}_${securityId}`,
            exchange: standardExchange as keyof typeof EXCHANGE_SEGMENTS,
            exchangeCode:
              EXCHANGE_SEGMENTS[
                standardExchange as keyof typeof EXCHANGE_SEGMENTS
              ],
            securityId: securityId.toString(),
          });

          validCount++;
        } catch (error) {
          invalidCount++;
        }
      });

      this.instruments = instruments;
      console.log(
        `Successfully loaded ${this.instruments.length} instruments (${validCount} valid, ${invalidCount} invalid)`
      );

      if (this.instruments.length === 0) {
        console.log(
          "No valid instruments found in Excel file. Using default instruments."
        );
        this.setDefaultInstruments();
      }
    } catch (error) {
      console.error("Error loading instruments from Excel:", error);
      this.setDefaultInstruments();
    }
  }

  private setDefaultInstruments() {
    this.instruments = [
      {
        ticker: "HDFCBANK",
        exchange: "NSE_EQ" as keyof typeof EXCHANGE_SEGMENTS,
        exchangeCode: EXCHANGE_SEGMENTS.NSE_EQ,
        securityId: "1333",
      },
      {
        ticker: "RELIANCE",
        exchange: "NSE_EQ" as keyof typeof EXCHANGE_SEGMENTS,
        exchangeCode: EXCHANGE_SEGMENTS.NSE_EQ,
        securityId: "3045",
      },
      {
        ticker: "INFY",
        exchange: "NSE_EQ" as keyof typeof EXCHANGE_SEGMENTS,
        exchangeCode: EXCHANGE_SEGMENTS.NSE_EQ,
        securityId: "1594",
      },
      {
        ticker: "TCS",
        exchange: "NSE_EQ" as keyof typeof EXCHANGE_SEGMENTS,
        exchangeCode: EXCHANGE_SEGMENTS.NSE_EQ,
        securityId: "11536",
      },
    ];
    console.log(`Using ${this.instruments.length} default instruments`);
  }

  private validateConfig() {
    if (!this.instruments.length) throw new Error("No instruments loaded");
    if (!(this.subscriptionType in SUBSCRIPTION_TYPES)) {
      throw new Error(`Invalid subscription type: ${this.subscriptionType}`);
    }

    // Warn if credentials are missing but don't throw error (allow demo mode)
    if (!this.accessToken) {
      console.warn("ACCESS_TOKEN not provided - running in demo mode");
    }
    if (!this.clientId) {
      console.warn("CLIENT_ID not provided - running in demo mode");
    }
  }

  private setupWebServer() {
    // Enable CORS for all routes
    this.app.use(
      cors({
        origin: ["http://localhost:3000", "http://localhost:3001"],
        methods: ["GET", "POST"],
        credentials: true,
      })
    );

    // Parse JSON bodies
    this.app.use(express.json());

    // Serve static files
    this.app.use(express.static("public"));

    // API Routes
    this.app.get("/api/data", (req, res) => {
      const { search, exchange, limit = 50 } = req.query;
      let filteredData = Array.from(this.marketData.values());

      // Apply search filter
      if (search) {
        const searchTerm = search.toString().toLowerCase();
        filteredData = filteredData.filter(
          (item) =>
            item.ticker.toLowerCase().includes(searchTerm) ||
            item.securityId.includes(searchTerm)
        );
      }

      // Apply exchange filter
      if (exchange) {
        filteredData = filteredData.filter(
          (item) => item.exchange === exchange
        );
      }

      // Apply limit
      const limitNum = parseInt(limit.toString()) || 50;
      filteredData = filteredData.slice(0, limitNum);

      res.json({
        connected: this.isConnected,
        instruments: this.instruments.length,
        subscriptionType: this.subscriptionType,
        latestData: filteredData,
        totalInstruments: this.instruments.length,
        activeInstruments: this.marketData.size,
      });
    });

    // Get specific instrument data
    this.app.get("/api/instrument/:securityId", (req, res) => {
      const { securityId } = req.params;
      const data = this.marketData.get(securityId);

      if (data) {
        res.json(data);
      } else {
        res.status(404).json({ error: "Instrument not found" });
      }
    });

    // Get instruments list with pagination
    this.app.get("/api/instruments", (req, res) => {
      const { page = 1, limit = 50, search } = req.query;
      const pageNum = parseInt(page.toString()) || 1;
      const limitNum = parseInt(limit.toString()) || 50;
      const offset = (pageNum - 1) * limitNum;

      let filteredInstruments = this.instruments;

      if (search) {
        const searchTerm = search.toString().toLowerCase();
        filteredInstruments = this.instruments.filter(
          (instrument) =>
            instrument.ticker.toLowerCase().includes(searchTerm) ||
            instrument.securityId.includes(searchTerm)
        );
      }

      const paginatedInstruments = filteredInstruments.slice(
        offset,
        offset + limitNum
      );

      res.json({
        instruments: paginatedInstruments,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total: filteredInstruments.length,
          pages: Math.ceil(filteredInstruments.length / limitNum),
        },
      });
    });

    // API endpoint to get individual stock data by ticker
    this.app.get("/api/stock/:ticker", (req, res) => {
      const ticker = req.params.ticker?.toUpperCase();

      if (!ticker) {
        return res.status(400).json({ error: "Ticker parameter is required" });
      }

      // Find the stock by ticker
      const stock = Array.from(this.marketData.values()).find(
        (item) => item.ticker?.toUpperCase() === ticker
      );

      if (!stock) {
        return res.status(404).json({ error: "Stock not found" });
      }

      // Enhance the stock data with additional calculated fields
      const enhancedStock = {
        ...stock,
        dayRange: `₹${stock.low?.toFixed(2) || "0.00"} - ₹${
          stock.high?.toFixed(2) || "0.00"
        }`,
        weekRange52: `₹${(stock.low * 0.8)?.toFixed(2) || "0.00"} - ₹${
          (stock.high * 1.2)?.toFixed(2) || "0.00"
        }`,
        marketCap: this.calculateMarketCap(stock.ltp, stock.volume),
        sector: this.getSector(stock.ticker),
        industry: this.getIndustry(stock.ticker),
      };

      res.json(enhancedStock);
    });

    // Error handling
    this.app.use(notFoundHandler);
    this.app.use(errorHandler);

    // Optimized Socket.IO connection handler
    this.io.on("connection", (socket) => {
      const clientId = socket.id;
      console.log(`📱 Client connected: ${clientId}`);

      // Send initial data asynchronously to prevent blocking
      setImmediate(() => {
        try {
          socket.emit("initialData", {
            instruments: this.instruments.slice(0, 100), // Limit initial data
            liveData: Array.from(this.marketData.values()).slice(0, 50), // Limit initial market data
          });
          console.log(`📊 Initial data sent to client: ${clientId}`);
        } catch (error) {
          console.error(`❌ Error sending initial data to ${clientId}:`, error);
        }
      });

      // Handle client disconnection
      socket.on("disconnect", (reason) => {
        console.log(`📱 Client disconnected: ${clientId}, reason: ${reason}`);
      });

      // Handle connection errors
      socket.on("error", (error) => {
        console.error(`❌ Socket error for client ${clientId}:`, error);
      });

      // Add heartbeat to keep connection alive
      const heartbeat = setInterval(() => {
        if (socket.connected) {
          socket.emit("ping", Date.now());
        } else {
          clearInterval(heartbeat);
        }
      }, 30000);

      socket.on("disconnect", () => {
        clearInterval(heartbeat);
      });
    });
  }

  public async start(port: number) {
    return new Promise<void>((resolve, reject) => {
      const tryPort = (currentPort: number) => {
        const server = this.server.listen(currentPort)
          .on('listening', () => {
            console.log(`Server running on port ${currentPort}`);
            this.connectToMarketFeed();
            resolve();
          })
          .on('error', (err: any) => {
            if (err.code === 'EADDRINUSE') {
              console.log(`Port ${currentPort} is busy, trying ${currentPort + 1}...`);
              server.close();
              tryPort(currentPort + 1);
            } else {
              reject(err);
            }
          });
      };

      tryPort(port);
    });

    // Graceful shutdown handling
    process.on("SIGINT", () => {
      console.log("Shutting down gracefully...");
      this.flushBatch(); // Ensure any pending batches are sent
      process.exit(0);
    });
  }

  private connectToMarketFeed() {
    console.log("Attempting to connect to Dhan market feed...");

    // Skip connection if no access token (for testing)
    if (!this.accessToken) {
      console.log(
        "No access token provided. Running in demo mode with mock data."
      );
      this.startMockDataGeneration();
      return;
    }

    // Construct WebSocket URL with authentication parameters (Dhan API v2)
    const wsUrl = `${MARKET_FEED_URL}?version=2&token=${this.accessToken}&clientId=${this.clientId}&authType=2`;

    console.log(
      `🔗 Connecting to WebSocket: ${wsUrl.replace(
        this.accessToken,
        "TOKEN_HIDDEN"
      )}`
    );
    this.ws = new WebSocket(wsUrl);

    this.ws.on("open", () => {
      console.log("Connected to market feed");
      this.isConnected = true;
      this.subscribeToInstruments();
    });

    this.ws.on("message", (data: Buffer) => {
      try {
        // Dhan sends binary data, need to parse it properly
        if (data.length > 0) {
          // Try to parse as JSON first (for text messages)
          try {
            const textData = data.toString("utf8");
            const message = JSON.parse(textData);
            // Only log important JSON messages
            if (message.type === "error" || message.type === "status") {
              console.log("📄 JSON message:", message);
            }
          } catch (jsonError) {
            // Binary data - this is likely the market data format
            this.handleBinaryMarketData(data);
          }
        }
      } catch (error) {
        console.error("❌ Error processing message:", error);
      }
    });

    this.ws.on("close", () => {
      console.log("Disconnected from market feed");
      this.isConnected = false;
      // Retry connection after 5 seconds
      setTimeout(() => this.connectToMarketFeed(), 5000);
    });

    this.ws.on("error", (error) => {
      console.error("WebSocket connection error:", error.message);
      this.isConnected = false;
      // Start mock data if real connection fails
      console.log("Starting mock data generation due to connection failure...");
      this.startMockDataGeneration();
    });
  }

  private getRealisticPrice(ticker: string): number {
    // Realistic base prices for common stocks (approximate current market prices)
    const stockPrices: { [key: string]: number } = {
      HDFCBANK: 1650,
      RELIANCE: 2850,
      INFY: 1800,
      TCS: 4200,
      ICICIBANK: 1200,
      SBIN: 820,
      BHARTIARTL: 1150,
      ITC: 460,
      HINDUNILVR: 2650,
      KOTAKBANK: 1750,
      LT: 3600,
      ASIANPAINT: 3200,
      MARUTI: 11500,
      BAJFINANCE: 7200,
      HCLTECH: 1650,
      WIPRO: 550,
      ULTRACEMCO: 11000,
      TITAN: 3400,
      NESTLEIND: 2200,
      POWERGRID: 320,
    };

    // If ticker exists in our price map, use it; otherwise generate based on ticker hash
    if (stockPrices[ticker]) {
      return stockPrices[ticker];
    }

    // For unknown tickers, generate consistent price based on ticker name
    let hash = 0;
    for (let i = 0; i < ticker.length; i++) {
      hash = ((hash << 5) - hash + ticker.charCodeAt(i)) & 0xffffffff;
    }
    return Math.abs(hash % 5000) + 100; // Price between 100-5100
  }

  // Helper function to calculate market cap (mock calculation)
  private calculateMarketCap(ltp: number, volume: number): string {
    // This is a simplified calculation - in reality, you'd need shares outstanding
    const mockShares = volume * 1000; // Mock shares outstanding
    const marketCap = ltp * mockShares;

    if (marketCap >= ********000) {
      // 1000 Cr
      return `₹${(marketCap / ********).toFixed(2)} Cr`;
    } else if (marketCap >= ********0) {
      // 10 Cr
      return `₹${(marketCap / ********).toFixed(2)} Cr`;
    } else {
      return `₹${(marketCap / 100000).toFixed(2)} L`;
    }
  }

  // Helper function to get sector (mock data)
  private getSector(ticker: string): string {
    const sectorMap: { [key: string]: string } = {
      RELIANCE: "Energy",
      TCS: "Information Technology",
      INFY: "Information Technology",
      HDFCBANK: "Financial Services",
      ICICIBANK: "Financial Services",
      BHARTIARTL: "Telecommunication",
      ITC: "FMCG",
      SBIN: "Financial Services",
      LT: "Construction",
      HCLTECH: "Information Technology",
      MARUTI: "Automobile",
      BAJFINANCE: "Financial Services",
      ASIANPAINT: "Paints",
      NESTLEIND: "FMCG",
      KOTAKBANK: "Financial Services",
    };

    return sectorMap[ticker.toUpperCase()] || "Others";
  }

  // Helper function to get industry (mock data)
  private getIndustry(ticker: string): string {
    const industryMap: { [key: string]: string } = {
      RELIANCE: "Oil & Gas",
      TCS: "IT Services",
      INFY: "IT Services",
      HDFCBANK: "Private Bank",
      ICICIBANK: "Private Bank",
      BHARTIARTL: "Telecom Services",
      ITC: "Tobacco & FMCG",
      SBIN: "Public Bank",
      LT: "Engineering & Construction",
      HCLTECH: "IT Services",
      MARUTI: "Auto Manufacturer",
      BAJFINANCE: "NBFC",
      ASIANPAINT: "Paints & Coatings",
      NESTLEIND: "Food Products",
      KOTAKBANK: "Private Bank",
    };

    return industryMap[ticker.toUpperCase()] || "Others";
  }

  private startMockDataGeneration() {
    // Prevent multiple instances
    if (this.mockDataInterval) {
      console.log("Mock data generation already running");
      return;
    }

    console.log("Starting realistic market data simulation...");
    this.isConnected = true;

    // Limit to top 50 instruments for better performance
    const activeInstruments = this.instruments.slice(0, 50);
    console.log(
      `Using ${activeInstruments.length} active instruments for real-time updates`
    );

    // Batch updates to reduce Socket.IO load
    const batchSize = 10;
    let currentBatch = 0;

    this.mockDataInterval = setInterval(() => {
      const batch: MarketData[] = [];
      const startIndex = currentBatch * batchSize;
      const endIndex = Math.min(
        startIndex + batchSize,
        activeInstruments.length
      );

      for (let i = startIndex; i < endIndex; i++) {
        const instrument = activeInstruments[i];

        // Use the realistic price method for this specific instrument
        const basePrice = this.getRealisticPrice(instrument.ticker);

        // Get existing data to maintain price continuity
        const existingData = this.marketData.get(instrument.securityId);
        const previousPrice = existingData?.ltp || basePrice;

        // Generate realistic price movement (±1% max change per update)
        const maxChange = previousPrice * 0.01;
        const change = (Math.random() - 0.5) * maxChange * 2;
        const ltp = Math.max(1, previousPrice + change);

        // Calculate change from base price (previous day's close)
        const totalChange = ltp - basePrice;

        const marketData: MarketData = {
          ticker: instrument.ticker,
          securityId: instrument.securityId,
          exchange: instrument.exchange,
          exchangeCode: instrument.exchangeCode,
          ltp: Math.round(ltp * 100) / 100,
          change: Math.round(totalChange * 100) / 100,
          changePercent: Math.round((totalChange / basePrice) * 10000) / 100,
          volume: Math.floor(Math.random() * 1000000) + 10000, // 10K to 1M volume
          high:
            Math.round(
              (Math.max(ltp, previousPrice) + Math.random() * (ltp * 0.01)) *
                100
            ) / 100,
          low:
            Math.round(
              (Math.min(ltp, previousPrice) - Math.random() * (ltp * 0.01)) *
                100
            ) / 100,
          open:
            Math.round(
              (basePrice + (Math.random() - 0.5) * (basePrice * 0.005)) * 100
            ) / 100,
          close: basePrice, // Previous day's close price
          timestamp: Date.now(),
        };

        this.marketData.set(marketData.securityId, marketData);
        batch.push(marketData);
      }

      // Send batch update instead of individual updates
      if (batch.length > 0) {
        this.io.emit("marketDataBatch", batch);
      }

      // Move to next batch
      currentBatch =
        (currentBatch + 1) % Math.ceil(activeInstruments.length / batchSize);

      updateStats(0, this.instruments);
    }, 2000); // 2 second intervals for better performance
  }

  private handleBinaryMarketData(data: Buffer) {
    try {
      if (data.length < 8) return; // Need at least 8 bytes for header

      // Parse binary message header (8 bytes)
      const responseCode = data.readUInt8(0);
      const exchangeSegment = data.readUInt8(3);
      const securityId = data.readUInt32LE(4);

      const exchangeName = this.getExchangeSegmentName(exchangeSegment);
      const timestamp = Date.now();
      const instrumentKey = `${exchangeSegment}-${securityId}`;

      // Find ticker for this instrument
      const instrument = this.instruments.find(
        (inst) =>
          inst.securityId == securityId.toString() &&
          EXCHANGE_SEGMENTS[inst.exchange] == exchangeSegment
      );
      const ticker = instrument ? instrument.ticker : `UNKNOWN_${securityId}`;

      let marketData: any = {
        timestamp: timestamp,
        responseCode: responseCode,
        exchangeSegment: exchangeSegment,
        exchangeName: exchangeName,
        securityId: securityId.toString(),
        dataLength: data.length,
        ticker: ticker,
      };

      // Parse different response types based on response code
      switch (responseCode) {
        case 2: // Ticker packet
          if (data.length >= 16) {
            marketData.type = "ticker";
            marketData.ltp = data.readFloatLE(8);
            marketData.ltt = data.readUInt32LE(12);
          }
          break;

        case 4: // Quote packet
          if (data.length >= 50) {
            marketData.type = "quote";
            marketData.ltp = data.readFloatLE(8);
            marketData.ltq = data.readUInt16LE(12);
            marketData.ltt = data.readUInt32LE(14);
            marketData.atp = data.readFloatLE(18);
            marketData.volume = data.readUInt32LE(22);
            marketData.totalSellQuantity = data.readUInt32LE(26);
            marketData.totalBuyQuantity = data.readUInt32LE(30);
            marketData.dayOpen = data.readFloatLE(34);
            marketData.dayClose = data.readFloatLE(38);
            marketData.dayHigh = data.readFloatLE(42);
            marketData.dayLow = data.readFloatLE(46);
          }
          break;

        case 6: // Previous close packet
          if (data.length >= 16) {
            marketData.type = "previousClose";
            marketData.previousClose = data.readFloatLE(8);
            marketData.previousOI = data.readUInt32LE(12);

            // Store previous close data for change calculation
            this.previousCloseData.set(instrumentKey, {
              previousClose: marketData.previousClose,
              previousOI: marketData.previousOI,
            });
          }
          break;

        case 8: // Full packet (with market depth)
          if (data.length >= 162) {
            marketData.type = "full";
            marketData.ltp = data.readFloatLE(8);
            marketData.ltq = data.readUInt16LE(12);
            marketData.ltt = data.readUInt32LE(14);
            marketData.atp = data.readFloatLE(18);
            marketData.volume = data.readUInt32LE(22);
            marketData.totalSellQuantity = data.readUInt32LE(26);
            marketData.totalBuyQuantity = data.readUInt32LE(30);
            marketData.openInterest = data.readUInt32LE(34);
            marketData.dayOpen = data.readFloatLE(46);
            marketData.dayClose = data.readFloatLE(50);
            marketData.dayHigh = data.readFloatLE(54);
            marketData.dayLow = data.readFloatLE(58);
          }
          break;

        default:
          marketData.type = "unknown";
      }

      // Convert to our MarketData format
      const formattedData: MarketData = {
        ticker: marketData.ticker,
        securityId: marketData.securityId,
        exchange: marketData.exchangeName,
        exchangeCode: marketData.exchangeSegment,
        ltp: marketData.ltp || 0,
        change: 0, // Will calculate below
        changePercent: 0, // Will calculate below
        volume: marketData.volume || 0,
        high: marketData.dayHigh || 0,
        low: marketData.dayLow || 0,
        open: marketData.dayOpen || 0,
        close: marketData.dayClose || 0,
        timestamp: marketData.timestamp,
      };

      // Calculate change using dayClose (previous day's close) from the market data
      if (formattedData.ltp && formattedData.close && formattedData.close > 0) {
        formattedData.change = formattedData.ltp - formattedData.close;
        formattedData.changePercent =
          (formattedData.change / formattedData.close) * 100;

        // Round to 2 decimal places
        formattedData.change = Math.round(formattedData.change * 100) / 100;
        formattedData.changePercent =
          Math.round(formattedData.changePercent * 100) / 100;
      }

      // Store and batch the data for efficient emission
      this.marketData.set(formattedData.securityId, formattedData);
      this.addToBatch(formattedData);

      // Data processed silently for better performance
    } catch (error) {
      console.error("❌ Error handling binary data:", error);
    }
  }

  // Optimized batching methods for better performance
  private addToBatch(data: MarketData) {
    this.updateBatch.push(data);

    // If batch is full, emit immediately
    if (this.updateBatch.length >= this.BATCH_SIZE) {
      this.flushBatch();
    } else {
      // Set timeout to flush batch if it's not full
      if (!this.batchTimeout) {
        this.batchTimeout = setTimeout(() => {
          this.flushBatch();
        }, this.BATCH_TIMEOUT);
      }
    }
  }

  private flushBatch() {
    if (this.updateBatch.length === 0) return;

    // Clear timeout
    if (this.batchTimeout) {
      clearTimeout(this.batchTimeout);
      this.batchTimeout = null;
    }

    // Emit batch or individual updates based on batch size
    if (this.updateBatch.length === 1) {
      // Single update - use legacy event for compatibility
      this.io.emit("marketData", this.updateBatch[0]);
    } else {
      // Batch update - use optimized event
      this.io.emit("marketDataBatch", this.updateBatch);
    }

    // Clear batch
    this.updateBatch = [];
  }

  private getExchangeSegmentName(code: number): string {
    const reverseMap = Object.fromEntries(
      Object.entries(EXCHANGE_SEGMENTS).map(([key, value]) => [value, key])
    );
    return reverseMap[code] || `Unknown(${code})`;
  }

  private async subscribeToInstruments() {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.log("WebSocket not ready for subscription");
      return;
    }

    console.log(`📡 Subscribing to ${this.instruments.length} instruments...`);

    // Get subscription type code
    const requestCode =
      SUBSCRIPTION_TYPES[
        this.subscriptionType as keyof typeof SUBSCRIPTION_TYPES
      ] || SUBSCRIPTION_TYPES.quote;

    // Prepare instrument list in Dhan format - Use STRING values like working market-feed.js
    const instrumentList = this.instruments.map((instrument) => ({
      ExchangeSegment: instrument.exchange, // ✅ Use STRING like working market-feed.js
      SecurityId: instrument.securityId, // ✅ Use STRING like working market-feed.js
    }));

    // Split into chunks of 100 instruments (Dhan API limit)
    const chunkSize = 100;

    for (let i = 0; i < instrumentList.length; i += chunkSize) {
      const chunk = instrumentList.slice(i, i + chunkSize);

      const subscriptionMessage = {
        RequestCode: requestCode,
        InstrumentCount: chunk.length,
        InstrumentList: chunk,
      };

      // Only log first and last chunks to reduce spam
      const chunkNumber = Math.floor(i / chunkSize) + 1;
      const totalChunks = Math.ceil(instrumentList.length / chunkSize);
      if (chunkNumber === 1 || chunkNumber === totalChunks) {
        console.log(
          `📤 Sending subscription chunk ${chunkNumber}/${totalChunks}`
        );
      }
      this.ws.send(JSON.stringify(subscriptionMessage));

      // Small delay between chunks to avoid overwhelming the server
      if (i + chunkSize < instrumentList.length) {
        await new Promise((resolve) => setTimeout(resolve, 200));
      }
    }
  }
}

const server = new DhanMarketFeedServer();

// Port configuration - prioritize BACKEND_PORT, fallback to PORT, then default
// BACKEND_PORT: New standardized environment variable
// PORT: Legacy environment variable (for backward compatibility)
// Default: 3003 (to avoid conflict with Next.js which uses 3001)
const port = Number(process.env.BACKEND_PORT) || Number(process.env.PORT) || 3003;

console.log(`Starting Dhan Market Feed Server on port ${port}`);
console.log(`📡 WebSocket server will be available at: ws://localhost:${port}`);
console.log(`🌐 REST API will be available at: http://localhost:${port}`);

// Start server with error handling
server.start(port).catch((error) => {
  console.error('Failed to start server:', error);
  process.exit(1);
});
