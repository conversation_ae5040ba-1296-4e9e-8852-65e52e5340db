// Comprehensive API types and interfaces

// Base API Response
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  statusCode?: number;
}

// Paginated Response
export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// User Types
export interface User {
  _id: string;
  name: string;
  email: string;
  role: 'user' | 'admin' | 'super_admin';
  userCode: string;
  adminId?: string;
  superAdminId?: string;
  companyName?: string;
  orderRate?: number;
  userOtp?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateUserRequest {
  name: string;
  email: string;
  password: string;
  role: 'user' | 'admin' | 'super_admin';
  companyName?: string;
  adminId?: string;
  superAdminId?: string;
}

export interface UpdateUserRequest {
  name?: string;
  email?: string;
  companyName?: string;
  orderRate?: number;
  userOtp?: string;
  isActive?: boolean;
}

// Authentication Types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse extends ApiResponse {
  data: {
    user: User;
    token: string;
    expiresIn: number;
  };
}

export interface AuthUser {
  userId: string;
  email: string;
  role: 'super_admin' | 'admin' | 'user';
  adminId?: string;
  superAdminId?: string;
  name: string;
  companyName?: string;
}

// Broker Account Types
export interface BrokerAccount {
  _id: string;
  owner: string | User;
  clientCode: string;
  broker: 'angel' | 'motilal';
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface AngelUser extends BrokerAccount {
  broker: 'angel';
  apiKey?: string;
  totpKey?: string;
}

export interface MotilalUser extends BrokerAccount {
  broker: 'motilal';
  apiKey?: string;
  totpKey?: string;
}

export interface CreateBrokerAccountRequest {
  clientCode: string;
  broker: 'angel' | 'motilal';
  apiKey?: string;
  totpKey?: string;
  owner?: string;
}

// Order Types
export interface OrderRequest {
  clientCode: string;
  securityId: string;
  ticker: string;
  quantity: number;
  price: number;
  productType: 'INTRADAY' | 'DELIVERY' | 'CNC' | 'MIS';
  validity: 'DAY' | 'IOC' | 'GTD';
  orderType: 'BUY' | 'SELL';
  broker?: 'angel' | 'motilal';
}

export interface OrderResponse {
  _id: string;
  orderId: string;
  userId: string;
  adminId?: string;
  clientCode: string;
  securityId: string;
  ticker: string;
  quantity: number;
  price: number;
  productType: string;
  validity: string;
  orderType: 'BUY' | 'SELL';
  broker: 'angel' | 'motilal';
  status: 'PENDING' | 'COMPLETED' | 'REJECTED' | 'CANCELLED';
  brokerOrderId?: string;
  brokerResponse?: any;
  errorMessage?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface BulkOrderRequest {
  orders: OrderRequest[];
  clientCodes: string[];
  broker?: 'angel' | 'motilal' | 'all';
}

export interface BulkOrderResponse extends ApiResponse {
  data: {
    totalOrders: number;
    successfulOrders: number;
    failedOrders: number;
    results: OrderResponse[];
    errors: string[];
  };
}

// Market Data Types
export interface MarketData {
  ticker: string;
  securityId: string;
  exchange: string;
  exchangeCode: number;
  ltp: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  close: number;
  timestamp: number;
}

export interface MarketDataSubscription {
  securityId: string;
  ticker: string;
  exchange: string;
}

// Billing Types
export interface BillingRecord {
  _id: string;
  userId: string;
  adminId: string;
  superAdminId?: string;
  orderId: string;
  amount: number;
  rate: number;
  billingCycle: string;
  status: 'pending' | 'paid' | 'overdue';
  createdAt: Date;
  updatedAt: Date;
}

export interface BillingSummary {
  _id: string;
  adminId: string;
  superAdminId?: string;
  billingCycle: string;
  totalAmount: number;
  totalOrders: number;
  averageRate: number;
  status: 'pending' | 'paid' | 'overdue';
  createdAt: Date;
  updatedAt: Date;
}

export interface BillingAnalytics {
  totalRevenue: number;
  totalOrders: number;
  averageOrderValue: number;
  topAdmins: Array<{
    adminId: string;
    adminName: string;
    totalAmount: number;
    totalOrders: number;
  }>;
  monthlyTrends: Array<{
    month: string;
    revenue: number;
    orders: number;
  }>;
}

// Dashboard Types
export interface AdminDashboard {
  stats: {
    totalUsers: number;
    activeUsers: number;
    totalOrders: number;
    totalRevenue: number;
  };
  recentOrders: OrderResponse[];
  billingInfo: BillingSummary;
  userAccounts: {
    angel: number;
    motilal: number;
  };
}

export interface SuperAdminDashboard {
  stats: {
    totalAdmins: number;
    totalUsers: number;
    totalOrders: number;
    totalRevenue: number;
  };
  admins: User[];
  recentOrders: OrderResponse[];
  billingAnalytics: BillingAnalytics;
}

// WebSocket Types
export interface WebSocketMessage {
  type: 'market_data' | 'order_update' | 'system_message' | 'error';
  data: any;
  timestamp: number;
}

export interface WebSocketSubscription {
  type: 'market_data' | 'order_updates';
  symbols?: string[];
  userId?: string;
}

// Error Types
export interface ApiError {
  message: string;
  statusCode: number;
  code?: string;
  details?: any;
  stack?: string;
}

export interface ValidationError extends ApiError {
  code: 'VALIDATION_ERROR';
  fields?: Record<string, string>;
}

// Rate Limiting Types
export interface RateLimitInfo {
  limit: number;
  remaining: number;
  resetTime: number;
  retryAfter?: number;
}

// Cache Types
export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

export interface CacheStats {
  size: number;
  maxSize: number;
  hitRate: number;
  entries: string[];
}

// Configuration Types
export interface ClientConfig {
  apiUrl: string;
  wsUrl: string;
  maxRetries: number;
  retryDelay: number;
  timeout: number;
}

export interface ServerConfig {
  port: number;
  mongoUri: string;
  jwtSecret: string;
  environment: 'development' | 'production' | 'test';
  corsOrigins: string[];
  rateLimits: Record<string, { maxRequests: number; windowMs: number }>;
}

// Export all types (removed duplicate export declarations)
