import { WebSocket as WS } from "ws";

// Re-export all API types
export * from './api';

// WebSocket specific types
export interface WebSocketMessage {
  type:
    | "subscribe"
    | "unsubscribe"
    | "connection"
    | "subscription"
    | "error"
    | "marketData";
  symbols?: string[];
  status?: string;
  message?: string;
  data?: MarketData;
}

export interface MarketData {
  symbol: string;
  exchange: string;
  lastPrice: number;
  change: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  close: number;
  timestamp: number;
}

export interface Symbol {
  exchange: string;
  symbol: string;
}

export interface Client {
  id: string;
  ws: WS;
  subscriptions: Set<string>;
}

// Enhanced WebSocket types
export interface WebSocketConnection {
  id: string;
  ws: WS;
  subscriptions: Set<string>;
  lastPing: number;
  isAlive: boolean;
  userId?: string;
  userRole?: string;
}

export interface WebSocketServer {
  clients: Map<string, WebSocketConnection>;
  subscriptions: Map<string, Set<string>>; // symbol -> client IDs
  messageQueue: WebSocketMessage[];
  isProcessing: boolean;
}

// Market data processing types
export interface MarketDataBatch {
  data: MarketData[];
  timestamp: number;
  source: string;
}

export interface MarketDataProcessor {
  process(batch: MarketDataBatch): Promise<void>;
  subscribe(symbols: string[]): Promise<void>;
  unsubscribe(symbols: string[]): Promise<void>;
}

// Server configuration types
export interface ServerConfig {
  port: number;
  host: string;
  maxConnections: number;
  pingInterval: number;
  pongTimeout: number;
  messageQueueSize: number;
  batchSize: number;
  batchInterval: number;
}

// Error types for WebSocket
export interface WebSocketError {
  code: string;
  message: string;
  timestamp: number;
  clientId?: string;
  data?: any;
}
