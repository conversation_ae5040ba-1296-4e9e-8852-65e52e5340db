#!/usr/bin/env node

/**
 * Comprehensive Testing Script for Trading Platform
 * Tests all major features and fixes implemented
 */

const https = require('https');
const http = require('http');

// Configuration
const BASE_URL = 'http://localhost:3000';
const BACKEND_URL = 'http://localhost:3003';

// Test results tracking
let testResults = {
  passed: 0,
  failed: 0,
  total: 0,
  details: []
};

// Helper function to make HTTP requests
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https') ? https : http;
    const req = protocol.request(url, options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ status: res.statusCode, data: jsonData, headers: res.headers });
        } catch (e) {
          resolve({ status: res.statusCode, data: data, headers: res.headers });
        }
      });
    });
    
    req.on('error', reject);
    
    if (options.body) {
      req.write(JSON.stringify(options.body));
    }
    
    req.end();
  });
}

// Test function wrapper
async function runTest(testName, testFunction) {
  testResults.total++;
  console.log(`\n🧪 Testing: ${testName}`);
  
  try {
    const result = await testFunction();
    if (result) {
      testResults.passed++;
      testResults.details.push({ name: testName, status: 'PASSED', message: result });
      console.log(`✅ PASSED: ${testName}`);
      if (typeof result === 'string') {
        console.log(`   ${result}`);
      }
    } else {
      testResults.failed++;
      testResults.details.push({ name: testName, status: 'FAILED', message: 'Test returned false' });
      console.log(`❌ FAILED: ${testName}`);
    }
  } catch (error) {
    testResults.failed++;
    testResults.details.push({ name: testName, status: 'FAILED', message: error.message });
    console.log(`❌ FAILED: ${testName} - ${error.message}`);
  }
}

// Individual test functions
async function testHealthEndpoint() {
  const response = await makeRequest(`${BASE_URL}/api/health`);
  if (response.status === 200 && response.data.status === 'healthy') {
    return `Health endpoint responding correctly (${response.data.uptime})`;
  }
  throw new Error(`Health check failed: ${response.status}`);
}

async function testBackendServer() {
  const response = await makeRequest(`${BACKEND_URL}/health`);
  if (response.status === 200) {
    return `Backend server responding on port 3003`;
  }
  throw new Error(`Backend server not responding: ${response.status}`);
}

async function testHomePage() {
  const response = await makeRequest(BASE_URL);
  if (response.status === 200) {
    return `Home page loading correctly`;
  }
  throw new Error(`Home page failed to load: ${response.status}`);
}

async function testLoginPage() {
  const response = await makeRequest(`${BASE_URL}/login`);
  if (response.status === 200) {
    return `Login page accessible`;
  }
  throw new Error(`Login page not accessible: ${response.status}`);
}

async function testAdminDashboard() {
  const response = await makeRequest(`${BASE_URL}/admin`);
  if (response.status === 200) {
    return `Admin dashboard accessible (will redirect to login if not authenticated)`;
  }
  throw new Error(`Admin dashboard not accessible: ${response.status}`);
}

async function testSuperAdminDashboard() {
  const response = await makeRequest(`${BASE_URL}/super-admin`);
  if (response.status === 200) {
    return `Super admin dashboard accessible (will redirect to login if not authenticated)`;
  }
  throw new Error(`Super admin dashboard not accessible: ${response.status}`);
}

async function testTradingPage() {
  const response = await makeRequest(`${BASE_URL}/trading`);
  if (response.status === 200) {
    return `Trading page accessible`;
  }
  throw new Error(`Trading page not accessible: ${response.status}`);
}

async function testAPIRoutes() {
  // Test various API endpoints
  const endpoints = [
    '/api/auth/me',
    '/api/data',
    '/api/client-accounts'
  ];
  
  let successCount = 0;
  for (const endpoint of endpoints) {
    try {
      const response = await makeRequest(`${BASE_URL}${endpoint}`);
      if (response.status < 500) { // Accept 401, 403 as valid responses for protected routes
        successCount++;
      }
    } catch (error) {
      // Continue testing other endpoints
    }
  }
  
  if (successCount === endpoints.length) {
    return `All ${endpoints.length} API endpoints responding correctly`;
  } else {
    return `${successCount}/${endpoints.length} API endpoints responding correctly`;
  }
}

async function testAccountManagementAPI() {
  // Test the new account management endpoints
  const endpoints = [
    '/api/admin/accounts',
    '/api/super-admin/accounts'
  ];
  
  let successCount = 0;
  for (const endpoint of endpoints) {
    try {
      const response = await makeRequest(`${BASE_URL}${endpoint}`);
      // These should return 401/403 without authentication, which is correct
      if (response.status === 401 || response.status === 403) {
        successCount++;
      }
    } catch (error) {
      // Continue testing
    }
  }
  
  if (successCount === endpoints.length) {
    return `Account management APIs properly protected`;
  }
  throw new Error(`Account management APIs not properly configured`);
}

async function testErrorHandling() {
  // Test error boundary and error handling
  const response = await makeRequest(`${BASE_URL}/api/nonexistent`);
  if (response.status === 404) {
    return `Error handling working correctly (404 for non-existent routes)`;
  }
  throw new Error(`Error handling not working properly`);
}

async function testPortConfiguration() {
  // Verify correct port configuration
  try {
    const frontendResponse = await makeRequest(`${BASE_URL}/api/health`);
    const backendResponse = await makeRequest(`${BACKEND_URL}/health`);
    
    if (frontendResponse.status === 200 && backendResponse.status === 200) {
      return `Port configuration correct (Frontend: 3000, Backend: 3003)`;
    }
  } catch (error) {
    throw new Error(`Port configuration issue: ${error.message}`);
  }
}

// Main test execution
async function runAllTests() {
  console.log('🚀 Starting Comprehensive Testing Suite');
  console.log('==========================================');
  
  // Core Infrastructure Tests
  await runTest('Health Endpoint', testHealthEndpoint);
  await runTest('Backend Server', testBackendServer);
  await runTest('Port Configuration', testPortConfiguration);
  
  // Page Accessibility Tests
  await runTest('Home Page', testHomePage);
  await runTest('Login Page', testLoginPage);
  await runTest('Admin Dashboard', testAdminDashboard);
  await runTest('Super Admin Dashboard', testSuperAdminDashboard);
  await runTest('Trading Page', testTradingPage);
  
  // API Tests
  await runTest('API Routes', testAPIRoutes);
  await runTest('Account Management API', testAccountManagementAPI);
  await runTest('Error Handling', testErrorHandling);
  
  // Print final results
  console.log('\n📊 TEST RESULTS SUMMARY');
  console.log('========================');
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`📈 Total:  ${testResults.total}`);
  console.log(`🎯 Success Rate: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);
  
  if (testResults.failed > 0) {
    console.log('\n❌ FAILED TESTS:');
    testResults.details
      .filter(test => test.status === 'FAILED')
      .forEach(test => {
        console.log(`   • ${test.name}: ${test.message}`);
      });
  }
  
  console.log('\n🎉 Testing Complete!');
  
  if (testResults.passed === testResults.total) {
    console.log('🌟 ALL TESTS PASSED! The application is working correctly.');
    process.exit(0);
  } else {
    console.log('⚠️  Some tests failed. Please review the issues above.');
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAllTests().catch(error => {
    console.error('💥 Test suite crashed:', error);
    process.exit(1);
  });
}

module.exports = { runAllTests, testResults };
