const fetch = require('node-fetch');

async function testBrokerAPISecurity() {
  console.log('🔒 TESTING BROKER API SECURITY');
  console.log('==================================================');

  // Test credentials (you'll need to get these from your login)
  const avisekh_token = 'your-avisekh-jwt-token'; // Replace with actual token
  const rahul_token = 'your-rahul-jwt-token';     // Replace with actual token

  const baseUrl = 'http://localhost:3000';

  console.log('\n1️⃣ Testing Angel Users API');
  console.log('--------------------------------------------------');

  // Test Avisekh admin access to Angel users
  console.log('\n👨‍💼 Testing Avisekh admin access to Angel users:');
  try {
    const response = await fetch(`${baseUrl}/api/users/angel`, {
      headers: {
        'Authorization': `Bearer ${avisekh_token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log(`✅ Avisekh can access Angel API`);
      console.log(`   Users returned: ${data.count}`);
      console.log(`   User role: ${data.userRole}`);
      data.users.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.clientName} (${user.userId})`);
      });
    } else {
      console.log(`❌ Avisekh Angel API failed: ${response.status}`);
    }
  } catch (error) {
    console.log(`❌ Error testing Avisekh Angel API: ${error.message}`);
  }

  // Test Rahul admin access to Angel users
  console.log('\n👨‍💼 Testing Rahul admin access to Angel users:');
  try {
    const response = await fetch(`${baseUrl}/api/users/angel`, {
      headers: {
        'Authorization': `Bearer ${rahul_token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log(`✅ Rahul can access Angel API`);
      console.log(`   Users returned: ${data.count}`);
      console.log(`   User role: ${data.userRole}`);
      data.users.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.clientName} (${user.userId})`);
      });
    } else {
      console.log(`❌ Rahul Angel API failed: ${response.status}`);
    }
  } catch (error) {
    console.log(`❌ Error testing Rahul Angel API: ${error.message}`);
  }

  console.log('\n2️⃣ Testing Motilal Users API');
  console.log('--------------------------------------------------');

  // Test Avisekh admin access to Motilal users
  console.log('\n👨‍💼 Testing Avisekh admin access to Motilal users:');
  try {
    const response = await fetch(`${baseUrl}/api/users/motilal`, {
      headers: {
        'Authorization': `Bearer ${avisekh_token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log(`✅ Avisekh can access Motilal API`);
      console.log(`   Users returned: ${data.count}`);
      console.log(`   User role: ${data.userRole}`);
      data.users.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.clientName} (${user.userId})`);
      });
    } else {
      console.log(`❌ Avisekh Motilal API failed: ${response.status}`);
    }
  } catch (error) {
    console.log(`❌ Error testing Avisekh Motilal API: ${error.message}`);
  }

  // Test Rahul admin access to Motilal users
  console.log('\n👨‍💼 Testing Rahul admin access to Motilal users:');
  try {
    const response = await fetch(`${baseUrl}/api/users/motilal`, {
      headers: {
        'Authorization': `Bearer ${rahul_token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log(`✅ Rahul can access Motilal API`);
      console.log(`   Users returned: ${data.count}`);
      console.log(`   User role: ${data.userRole}`);
      data.users.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.clientName} (${user.userId})`);
      });
    } else {
      console.log(`❌ Rahul Motilal API failed: ${response.status}`);
    }
  } catch (error) {
    console.log(`❌ Error testing Rahul Motilal API: ${error.message}`);
  }

  console.log('\n📊 EXPECTED RESULTS:');
  console.log('==================================================');
  console.log('✅ Avisekh admin should see:');
  console.log('   - 1 Angel user (SAHIL BALANI - AAAF277513)');
  console.log('   - 1 Motilal user (SAHIL BALANI - GMV265)');
  console.log('');
  console.log('✅ Rahul admin should see:');
  console.log('   - 0 Angel users');
  console.log('   - 0 Motilal users');
  console.log('');
  console.log('🔒 SECURITY VERIFICATION:');
  console.log('- Each admin should only see broker accounts of their own managed users');
  console.log('- No cross-admin visibility should occur');
  console.log('- Super admin would see all accounts (not tested here)');
}

// Note: To run this test, you need to:
// 1. Start the development server (npm run dev)
// 2. Login as Avisekh admin and get the JWT token from browser dev tools
// 3. Login as Rahul admin and get the JWT token from browser dev tools
// 4. Replace the token variables above with actual tokens
// 5. Run: node test-broker-api-security.js

console.log('📝 INSTRUCTIONS TO RUN THIS TEST:');
console.log('1. Start the development server: npm run dev');
console.log('2. Open browser and login as Avisekh admin');
console.log('3. Open browser dev tools > Application > Cookies > Copy auth-token value');
console.log('4. Replace avisekh_token variable with the copied token');
console.log('5. Repeat steps 2-4 for Rahul admin');
console.log('6. Run: node test-broker-api-security.js');
console.log('');
console.log('⚠️ Tokens are currently set to placeholder values');

// Uncomment the line below to run the test (after setting up tokens)
// testBrokerAPISecurity();
